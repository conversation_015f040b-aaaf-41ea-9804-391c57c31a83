#!/usr/bin/env python3
"""
Enhanced database operations for the cargo manifest processing system.

This module implements the new business logic requirements:
- Processing status lifecycle management
- Expected vs declared vs checked-in tracking
- Partial suffix generation for P and D split types
- SHA-256 duplicate prevention for files and AWBs
- In-transit logic based on destination branches
"""

import hashlib
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from .base_operations import BaseDatabaseOperations


class EnhancedDatabaseOperations(BaseDatabaseOperations):
    """
    Enhanced database operations implementing the new cargo manifest processing logic.

    Key Features:
    - Processing status lifecycle (PENDING -> AWAITING_CHECKIN -> CHECKED_IN -> RECONCILED)
    - Expected/declared/checked-in field separation
    - Partial suffix generation (one per flight arrival)
    - File and AWB content duplicate prevention
    - In-transit logic based on destination branches
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize enhanced database operations.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            logger: Logger instance
        """
        super().__init__(db_connection, db_cursor, logger)
        self.branch_id = branch_id
        self.user_id = user_id

    # ==================== FILE DUPLICATE PREVENTION ====================

    def generate_file_hash(self, file_path: str) -> str:
        """
        Generate SHA-256 hash for entire file content.

        Args:
            file_path (str): Path to the file

        Returns:
            str: SHA-256 hash string
        """
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error generating file hash for {file_path}: {str(e)}")
            raise

    def check_file_duplicate(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """
        Check if file with given hash already exists.

        Args:
            file_hash (str): SHA-256 hash of file

        Returns:
            dict: Existing file record if duplicate, None otherwise
        """
        result = self.find_record(
            'duplicate_files',
            'file_hash = %s',
            (file_hash,),
            'id, file_name, first_processed_at, attempt_count'
        )

        if result:
            return {
                'id': result[0],
                'file_name': result[1],
                'first_processed_at': result[2],
                'attempt_count': result[3]
            }
        return None

    def record_file_processed(self, file_path: str, file_hash: str, message_type: str, file_size: int) -> int:
        """
        Record file as processed in duplicate prevention system.

        Args:
            file_path (str): Path to the file
            file_hash (str): SHA-256 hash of file
            message_type (str): XFFM, XFWB, or XFZB
            file_size (int): File size in bytes

        Returns:
            int: Record ID
        """
        import os

        data = {
            'file_name': os.path.basename(file_path),
            'file_hash': file_hash,
            'message_type': message_type.upper(),
            'file_path': file_path,
            'file_size': file_size,
            'first_processed_at': datetime.now(),
            'last_attempted_at': datetime.now(),
            'attempt_count': 1,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('duplicate_files', data, 'id')

    def record_duplicate_attempt(self, file_hash: str) -> None:
        """
        Record a duplicate file attempt.

        Args:
            file_hash (str): SHA-256 hash of file
        """
        self.execute_query(
            """UPDATE duplicate_files
               SET attempt_count = attempt_count + 1,
                   last_attempted_at = %s
               WHERE file_hash = %s""",
            (datetime.now(), file_hash)
        )

    # ==================== AWB CONTENT DUPLICATE PREVENTION ====================

    def generate_awb_content_hash(self, awb_data: Dict[str, Any]) -> str:
        """
        Generate SHA-256 hash for AWB content.

        Args:
            awb_data (dict): AWB data containing pieces, weight, volume, etc.

        Returns:
            str: SHA-256 hash string
        """
        # Create normalized data for consistent hashing
        normalized_data = {
            'awb_number': awb_data.get('awb_number', ''),
            'origin': awb_data.get('origin_airport', ''),
            'destination': awb_data.get('destination_airport', ''),
            'pieces': awb_data.get('pieces', 0),
            'weight': float(awb_data.get('weight', 0)),
            'volume': float(awb_data.get('volume', 0)),
            'description': awb_data.get('description', ''),
            'special_handling': awb_data.get('special_handling', ''),
        }

        # Sort keys for consistency
        normalized_json = json.dumps(normalized_data, sort_keys=True)
        return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()

    def check_awb_content_duplicate(self, awb_number: str, content_hash: str) -> bool:
        """
        Check if AWB with identical content already exists.

        Args:
            awb_number (str): AWB number
            content_hash (str): SHA-256 hash of AWB content

        Returns:
            bool: True if duplicate exists, False otherwise
        """
        return self.record_exists(
            'awb_hashes',
            'awb_number = %s AND content_hash = %s',
            (awb_number, content_hash)
        )

    def record_awb_processed(self, awb_number: str, content_hash: str, manifest_id: str, source_file: str) -> int:
        """
        Record AWB as processed in duplicate prevention system.

        Args:
            awb_number (str): AWB number
            content_hash (str): SHA-256 hash of AWB content
            manifest_id (str): Manifest ID
            source_file (str): Source file name

        Returns:
            int: Record ID
        """
        data = {
            'awb_number': awb_number,
            'manifest_id': manifest_id,
            'content_hash': content_hash,
            'source_file': source_file,
            'processed_at': datetime.now(),
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('awb_hashes', data, 'id')

    # ==================== PROCESSING LOG ====================

    def start_processing_log(self, file_name: str, message_type: str) -> str:
        """
        Start a processing log session.

        Args:
            file_name (str): Name of file being processed
            message_type (str): XFFM, XFWB, or XFZB

        Returns:
            str: Unique log ID
        """
        log_id = f"LOG-{uuid.uuid4()}"

        data = {
            'log_id': log_id,
            'file_name': file_name,
            'message_type': message_type.upper(),
            'status': 'STARTED',
            'started_at': datetime.now(),
            'awbs_processed': 0,
            'ulds_processed': 0,
            'errors_count': 0,
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.insert_record('processing_logs', data)
        return log_id

    def complete_processing_log(self, log_id: str, success: bool, awbs_processed: int = 0,
                               ulds_processed: int = 0, error_message: str = None,
                               processing_summary: Dict[str, Any] = None) -> None:
        """
        Complete a processing log session.

        Args:
            log_id (str): Log ID from start_processing_log
            success (bool): Whether processing was successful
            awbs_processed (int): Number of AWBs processed
            ulds_processed (int): Number of ULDs processed
            error_message (str): Error message if failed
            processing_summary (dict): Summary of processing results
        """
        # Calculate duration
        result = self.find_record(
            'processing_logs',
            'log_id = %s',
            (log_id,),
            'started_at'
        )

        duration_seconds = 0
        if result:
            started_at = result[0]
            duration_seconds = int((datetime.now() - started_at).total_seconds())

        update_data = {
            'status': 'SUCCESS' if success else 'FAILED',
            'completed_at': datetime.now(),
            'duration_seconds': duration_seconds,
            'awbs_processed': awbs_processed,
            'ulds_processed': ulds_processed,
            'error_message': error_message,
            'processing_summary': self.format_json_field(processing_summary),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    def mark_processing_duplicate(self, log_id: str) -> None:
        """
        Mark processing log as duplicate.

        Args:
            log_id (str): Log ID
        """
        update_data = {
            'status': 'DUPLICATE',
            'completed_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.update_record(
            'processing_logs',
            update_data,
            'log_id = %s',
            (log_id,)
        )

    # ==================== PARTIAL SUFFIX TRACKING ====================

    def generate_partial_suffix(self, awb_number: str, manifest_id: str, flight_number: str,
                                flight_date: str, split_type: str) -> str:
        """
        Generate suffix for partial shipments (P and D types).
        One suffix per flight arrival regardless of ULD count.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID
            flight_number (str): Flight number
            flight_date (str): Flight date
            split_type (str): P or D

        Returns:
            str: Generated suffix (e.g., "AWB-1", "AWB-2")
        """
        # Check if suffix already exists for this flight
        existing = self.find_record(
            'partial_suffix_tracking',
            'awb_number = %s AND manifest_id = %s',
            (awb_number, manifest_id),
            'generated_suffix'
        )

        if existing:
            return existing[0]

        # Get next suffix number for this AWB
        result = self.execute_query(
            "SELECT COALESCE(MAX(suffix_number), 0) + 1 FROM partial_suffix_tracking WHERE awb_number = %s",
            (awb_number,),
            fetch_one=True
        )

        next_suffix = result[0] if result else 1
        generated_suffix = f"{awb_number}-{next_suffix}"

        # Create tracking record
        data = {
            'awb_number': awb_number,
            'flight_number': flight_number,
            'flight_date': flight_date,
            'manifest_id': manifest_id,
            'suffix_number': next_suffix,
            'generated_suffix': generated_suffix,
            'split_type': split_type,
            'branch_id': self.branch_id,
            'created_at': datetime.now()
        }

        self.insert_record('partial_suffix_tracking', data)
        return generated_suffix

    def get_existing_suffix(self, awb_number: str, manifest_id: str) -> Optional[str]:
        """
        Get existing suffix for AWB on specific flight.

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID

        Returns:
            str: Existing suffix or None
        """
        result = self.find_record(
            'partial_suffix_tracking',
            'awb_number = %s AND manifest_id = %s',
            (awb_number, manifest_id),
            'generated_suffix'
        )
        return result[0] if result else None

    # ==================== BRANCH OPERATIONS ====================

    def check_destination_branch_exists(self, destination_code: str) -> Optional[int]:
        """
        Check if destination airport has a corresponding branch.

        Args:
            destination_code (str): Destination airport code

        Returns:
            int: Branch ID if exists, None otherwise
        """
        result = self.find_record(
            'branches',
            'iata_code = %s OR code = %s',
            (destination_code, destination_code),
            'id'
        )
        return result[0] if result else None

    # ==================== MASTER WAYBILL OPERATIONS ====================

    def create_or_update_master_waybill_enhanced(self, awb_data: Dict[str, Any],
                                                 source_type: str = 'XFFM') -> Tuple[str, bool]:
        """
        Create or update master waybill with enhanced processing logic.

        Args:
            awb_data (dict): AWB data
            source_type (str): XFFM or XFWB

        Returns:
            tuple: (awb_number, was_created)
        """
        awb_number = awb_data['awb_number']

        # Check if AWB already exists
        existing = self.find_record(
            'master_waybills',
            'awb_number = %s',
            (awb_number,),
            'awb_number, processing_status, total_pieces_expected, total_pieces_declared'
        )

        if existing:
            return self._update_existing_master_waybill(awb_number, awb_data, source_type, existing)
        else:
            return self._create_new_master_waybill(awb_data, source_type)

    def _update_existing_master_waybill(self, awb_number: str, awb_data: Dict[str, Any],
                                       source_type: str, existing: Tuple) -> Tuple[str, bool]:
        """Update existing master waybill."""
        update_data = {
            'updated_at': datetime.now(),
            'updated_by': self.user_id
        }

        if source_type == 'XFFM':
            # Update expected fields from manifest
            pieces = awb_data.get('pieces', 0)
            weight = awb_data.get('weight', 0)
            volume = awb_data.get('volume', 0)

            update_data.update({
                'total_pieces_expected': (existing[2] or 0) + pieces,
                'gross_weight_expected': awb_data.get('weight'),
                'volume_expected': awb_data.get('volume'),
            })

            # Update processing status if needed
            if existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'

        elif source_type == 'XFWB':
            # Update declared fields from XFWB
            update_data.update({
                'total_pieces_declared': awb_data.get('total_pieces'),
                'gross_weight_declared': awb_data.get('total_weight'),
                'volume_declared': awb_data.get('gross_volume'),
                'shipper_code': awb_data.get('shipper_code'),
                'consignee_code': awb_data.get('consignee_code'),
                'special_handling_codes': self.format_json_field(awb_data.get('special_handling_codes')),
                'goods_descriptions': self.format_json_field(awb_data.get('goods_descriptions')),
                'xml_data': self.format_json_field(awb_data.get('xml_data'))
            })

            # Update processing status
            if existing[1] == 'PENDING':
                update_data['processing_status'] = 'AWAITING_CHECKIN'

        # Handle in-transit logic
        self._update_in_transit_status(update_data, awb_data)

        self.update_record(
            'master_waybills',
            update_data,
            'awb_number = %s',
            (awb_number,)
        )

        return awb_number, False

    def _create_new_master_waybill(self, awb_data: Dict[str, Any], source_type: str) -> Tuple[str, bool]:
        """Create new master waybill."""
        awb_number = awb_data['awb_number']

        data = {
            'awb_number': awb_number,
            'type_code': awb_data.get('type_code', '740'),
            'manifest_id': awb_data.get('manifest_id'),
            'origin_airport': awb_data.get('origin_airport'),
            'destination_airport': awb_data.get('destination_airport'),
            'processing_status': 'PENDING',
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'updated_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        if source_type == 'XFFM':
            # Set expected fields from manifest
            data.update({
                'total_pieces_expected': awb_data.get('pieces', 0),
                'gross_weight_expected': awb_data.get('weight'),
                'volume_expected': awb_data.get('volume'),
                'processing_status': 'AWAITING_CHECKIN'
            })
        elif source_type == 'XFWB':
            # Set declared fields from XFWB
            data.update({
                'total_pieces': awb_data.get('total_pieces', 0),
                'total_weight': awb_data.get('total_weight', 0),
                'total_pieces_declared': awb_data.get('total_pieces'),
                'gross_weight_declared': awb_data.get('total_weight'),
                'volume_declared': awb_data.get('gross_volume'),
                'weight_unit': awb_data.get('weight_unit', 'KGM'),
                'shipper_code': awb_data.get('shipper_code'),
                'consignee_code': awb_data.get('consignee_code'),
                'special_handling_codes': self.format_json_field(awb_data.get('special_handling_codes')),
                'goods_descriptions': self.format_json_field(awb_data.get('goods_descriptions')),
                'xml_data': self.format_json_field(awb_data.get('xml_data')),
                'processing_status': 'AWAITING_CHECKIN'
            })

        # Handle in-transit logic
        self._update_in_transit_status(data, awb_data)

        self.insert_record('master_waybills', data)
        return awb_number, True

    def _update_in_transit_status(self, data: Dict[str, Any], awb_data: Dict[str, Any]) -> None:
        """Update in-transit status based on destination."""
        destination = awb_data.get('destination_airport')
        if destination:
            # Check if destination has a corresponding branch
            destination_branch = self.check_destination_branch_exists(destination)
            if destination_branch and destination_branch != self.branch_id:
                data['in_transit'] = True
            else:
                data['in_transit'] = False

    # ==================== PARTIAL WAYBILL OPERATIONS ====================

    def create_partial_waybill_enhanced(self, awb_number: str, partial_data: Dict[str, Any],
                                       suffix: str, uld_id: str = None, manifest_id: str = None) -> str:
        """
        Create partial waybill record with enhanced processing logic.

        Args:
            awb_number (str): Parent AWB number
            partial_data (dict): Partial shipment data
            suffix (str): Partial suffix (e.g., 'AWB-1')
            uld_id (str): ULD ID if applicable
            manifest_id (str): Manifest ID

        Returns:
            str: Partial ID
        """
        partial_id = suffix  # Use the generated suffix as partial ID

        data = {
            'partial_id': partial_id,
            'master_awb_number': awb_number,
            'manifest_id': manifest_id or partial_data.get('manifest_id'),
            'flight_number': partial_data.get('flight_number'),
            'flight_date': partial_data.get('flight_date'),
            'origin_airport': partial_data.get('origin_airport'),
            'destination_airport': partial_data.get('destination_airport'),
            'manifest_expected_pieces': partial_data.get('pieces', 0),
            'manifest_expected_weight': partial_data.get('weight', 0),
            'manifest_expected_volume': partial_data.get('volume', 0),
            'expected_pieces': partial_data.get('pieces', 0),
            'expected_weight': partial_data.get('weight', 0),
            'received_pieces': 0,  # Will be updated by check-in module
            'received_weight': 0,  # Will be updated by check-in module
            'remaining_pieces': partial_data.get('pieces', 0),
            'remaining_weight': partial_data.get('weight', 0),
            'transport_split_description': partial_data.get('split_type', 'P'),
            'partial_suffix': suffix,
            'processing_status': 'AWAITING_CHECKIN',
            'status': 'PENDING',
            'uld_id': uld_id,
            'weight_unit': partial_data.get('weight_unit', 'KGM'),
            'special_handling_code': partial_data.get('special_handling_code'),
            'source': 'XFFM',
            'content_hash': self.generate_awb_content_hash(partial_data),
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'updated_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        self.insert_record('partial_waybills', data)
        return partial_id

    # ==================== ULD OPERATIONS ====================

    def create_uld_record_enhanced(self, uld_data: Dict[str, Any], manifest_id: str = None) -> int:
        """
        Create ULD detail record with enhanced processing logic.

        Args:
            uld_data (dict): ULD data
            manifest_id (str): Manifest ID

        Returns:
            int: ULD record ID
        """
        data = {
            'manifest_id': manifest_id or uld_data.get('manifest_id'),
            'uld_id': uld_data['uld_id'],
            'uld_type': uld_data.get('uld_type'),
            'uld_owner': uld_data.get('uld_owner'),
            'loading_indicator': uld_data.get('loading_indicator'),
            'weight': uld_data.get('weight', 0),
            'pieces': uld_data.get('pieces', 0),
            'status': 'ARRIVED',
            'current_location': uld_data.get('current_location'),
            'arrived_at': datetime.now(),
            'branch_id': self.branch_id,
            'created_by': self.user_id,
            'updated_by': self.user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

        return self.insert_record('uld_details', data, 'id')

    # ==================== SPLIT TYPE HANDLING ====================

    def handle_split_types(self, awb_data: Dict[str, Any], uld_data: Dict[str, Any] = None,
                          manifest_id: str = None) -> Dict[str, Any]:
        """
        Handle different split types (T, S, P, D) according to business rules.

        Args:
            awb_data (dict): AWB data
            uld_data (dict): ULD data if applicable
            manifest_id (str): Manifest ID

        Returns:
            dict: Processing result
        """
        split_type = awb_data.get('transport_split_description', 'T')
        awb_number = awb_data['awb_number']

        result = {
            'awb_number': awb_number,
            'split_type': split_type,
            'partial_created': False,
            'suffix': None,
            'uld_recorded': False
        }

        # Create or update master waybill
        master_awb, was_created = self.create_or_update_master_waybill_enhanced(awb_data, 'XFFM')
        result['master_created'] = was_created

        # Handle ULD if present
        if uld_data:
            uld_id = self.create_uld_record_enhanced(uld_data, manifest_id)
            result['uld_recorded'] = True
            result['uld_id'] = uld_id

        # Handle split types
        if split_type in ['P', 'D']:  # Partial shipments require suffix
            suffix = self.generate_partial_suffix(
                awb_number,
                manifest_id,
                awb_data.get('flight_number', 'UNKNOWN'),
                awb_data.get('flight_date', datetime.now().strftime('%Y-%m-%d')),
                split_type
            )

            partial_id = self.create_partial_waybill_enhanced(
                awb_number,
                awb_data,
                suffix,
                uld_data.get('uld_id') if uld_data else None,
                manifest_id
            )

            result['partial_created'] = True
            result['suffix'] = suffix
            result['partial_id'] = partial_id

            # Update master waybill to mark as partial
            self.update_record(
                'master_waybills',
                {'is_partial': True, 'updated_at': datetime.now()},
                'awb_number = %s',
                (awb_number,)
            )

        return result

    # ==================== UTILITY METHODS ====================

    def save_data(self, data):
        """
        Save parsed data to the database. Implementation required by base class.

        Args:
            data (dict): Parsed data to save

        Returns:
            dict: Result of save operation
        """
        # This will be implemented by specific processor classes
        raise NotImplementedError("save_data must be implemented by specific processor classes")
