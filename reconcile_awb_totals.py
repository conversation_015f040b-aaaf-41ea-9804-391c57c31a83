#!/usr/bin/env python3
"""
Reconcile master waybill totals with ULD allocations.

This script identifies and fixes discrepancies between master waybill totals
and the sum of their ULD allocations.
"""

import sys
import psycopg2
from decimal import Decimal

# Add the parent directory to the path
sys.path.append('/var/www/cargo-mis')

def reconcile_awb_totals():
    """Reconcile master waybill totals with ULD allocations."""
    try:
        from python.xml_parsers.config.database import DB_CONFIG
        
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== AWB Totals Reconciliation ===")
        print()
        
        # Find AWBs with mismatched totals
        print("1. Finding AWBs with mismatched totals...")
        
        cursor.execute("""
            SELECT 
                mw.awb_number,
                mw.total_pieces as master_pieces,
                mw.total_weight as master_weight,
                COALESCE(SUM(ua.assigned_pieces), 0) as uld_pieces,
                COALESCE(SUM(ua.assigned_weight), 0) as uld_weight,
                mw.manifest_id,
                mw.xml_data
            FROM master_waybills mw
            LEFT JOIN uld_awb_allocations ua ON mw.awb_number = ua.awb_number
            GROUP BY mw.awb_number, mw.total_pieces, mw.total_weight, mw.manifest_id, mw.xml_data
            HAVING 
                mw.total_pieces != COALESCE(SUM(ua.assigned_pieces), 0) OR
                ABS(mw.total_weight - COALESCE(SUM(ua.assigned_weight), 0)) > 0.01
            ORDER BY mw.awb_number
        """)
        
        mismatched_awbs = cursor.fetchall()
        
        if not mismatched_awbs:
            print("✅ No mismatched AWB totals found!")
            cursor.close()
            conn.close()
            return True
        
        print(f"Found {len(mismatched_awbs)} AWBs with mismatched totals:")
        print()
        
        fixes_applied = 0
        
        for row in mismatched_awbs:
            awb_number, master_pieces, master_weight, uld_pieces, uld_weight, manifest_id, xml_data = row
            
            print(f"AWB {awb_number} (Manifest: {manifest_id}):")
            print(f"  Master Waybill: {master_pieces} pieces, {master_weight} kg")
            print(f"  ULD Allocations: {uld_pieces} pieces, {uld_weight} kg")
            
            # Check if this AWB has split code information
            split_code = None
            if xml_data and isinstance(xml_data, dict):
                split_code = xml_data.get('transport_split_code')
                split_info = xml_data.get('split_info', {})
                if split_info.get('is_split_across_ulds'):
                    print(f"  Split Code: {split_code} (Split across ULDs)")
                elif split_info.get('is_partial'):
                    print(f"  Split Code: {split_code} (Partial shipment)")
            
            # Determine the correct action
            if uld_pieces > 0 or uld_weight > 0:
                # ULD allocations exist, update master waybill to match
                print(f"  Action: Update master waybill to match ULD totals")
                
                cursor.execute("""
                    UPDATE master_waybills 
                    SET total_pieces = %s, 
                        total_weight = %s,
                        updated_at = NOW()
                    WHERE awb_number = %s
                """, (uld_pieces, uld_weight, awb_number))
                
                fixes_applied += 1
                print(f"  ✅ Updated master waybill totals")
                
            else:
                # No ULD allocations, this might be a standalone AWB
                print(f"  Action: No ULD allocations found - keeping master waybill totals")
                print(f"  ℹ️  This might be a standalone AWB or bulk cargo")
            
            print()
        
        # Commit changes
        if fixes_applied > 0:
            conn.commit()
            print(f"✅ Applied {fixes_applied} fixes to master waybill totals")
        else:
            print("ℹ️  No fixes were needed")
        
        print()
        print("2. Verification - checking for remaining mismatches...")
        
        # Re-run the check
        cursor.execute("""
            SELECT 
                mw.awb_number,
                mw.total_pieces as master_pieces,
                mw.total_weight as master_weight,
                COALESCE(SUM(ua.assigned_pieces), 0) as uld_pieces,
                COALESCE(SUM(ua.assigned_weight), 0) as uld_weight
            FROM master_waybills mw
            LEFT JOIN uld_awb_allocations ua ON mw.awb_number = ua.awb_number
            GROUP BY mw.awb_number, mw.total_pieces, mw.total_weight
            HAVING 
                mw.total_pieces != COALESCE(SUM(ua.assigned_pieces), 0) OR
                ABS(mw.total_weight - COALESCE(SUM(ua.assigned_weight), 0)) > 0.01
            ORDER BY mw.awb_number
        """)
        
        remaining_mismatches = cursor.fetchall()
        
        if remaining_mismatches:
            print(f"⚠️  {len(remaining_mismatches)} AWBs still have mismatched totals:")
            for row in remaining_mismatches:
                awb_number, master_pieces, master_weight, uld_pieces, uld_weight = row
                print(f"  {awb_number}: Master({master_pieces}, {master_weight}) vs ULD({uld_pieces}, {uld_weight})")
        else:
            print("✅ All AWB totals are now consistent!")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_awb_details(awb_number):
    """Show detailed information for a specific AWB."""
    try:
        from python.xml_parsers.config.database import DB_CONFIG
        
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print(f"=== AWB {awb_number} Details ===")
        print()
        
        # Master waybill
        cursor.execute("""
            SELECT total_pieces, total_weight, manifest_id, xml_data, status
            FROM master_waybills 
            WHERE awb_number = %s
        """, (awb_number,))
        
        result = cursor.fetchone()
        if result:
            total_pieces, total_weight, manifest_id, xml_data, status = result
            print(f"Master Waybill:")
            print(f"  Pieces: {total_pieces}")
            print(f"  Weight: {total_weight} kg")
            print(f"  Manifest: {manifest_id}")
            print(f"  Status: {status}")
            
            if xml_data and isinstance(xml_data, dict):
                split_code = xml_data.get('transport_split_code')
                split_info = xml_data.get('split_info', {})
                if split_code:
                    print(f"  Split Code: {split_code}")
                if split_info:
                    print(f"  Split Info: {split_info}")
        else:
            print("❌ Master waybill not found!")
            return False
        
        print()
        
        # ULD allocations
        cursor.execute("""
            SELECT uld_id, assigned_pieces, assigned_weight, manifest_id
            FROM uld_awb_allocations 
            WHERE awb_number = %s
            ORDER BY uld_id
        """, (awb_number,))
        
        allocations = cursor.fetchall()
        if allocations:
            print(f"ULD Allocations:")
            total_allocated_pieces = 0
            total_allocated_weight = 0
            
            for uld_id, pieces, weight, manifest_id in allocations:
                print(f"  ULD {uld_id}: {pieces} pieces, {weight} kg (Manifest: {manifest_id})")
                total_allocated_pieces += pieces
                total_allocated_weight += weight
            
            print(f"  Total: {total_allocated_pieces} pieces, {total_allocated_weight} kg")
        else:
            print("No ULD allocations found")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Show details for specific AWB
        awb_number = sys.argv[1]
        show_awb_details(awb_number)
    else:
        # Run full reconciliation
        reconcile_awb_totals()
