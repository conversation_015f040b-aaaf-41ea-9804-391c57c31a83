#!/usr/bin/env python3
"""
XFZB data validator.

This module provides functionality for validating data extracted from XFZB XML files.
"""

import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validators.base_validator import BaseValidator


class XFZBValidator(BaseValidator):
    """
    Validator for XFZB (House Air Waybill) XML data.
    
    This class validates all extracted data from XFZB XML files including
    house waybill information, party details, cargo details, and handling instructions.
    """
    
    def validate(self, data):
        """
        Validate extracted XFZB data.
        
        Args:
            data (dict): Extracted data to validate.
            
        Returns:
            bool: True if valid, False otherwise.
        """
        self.clear_errors()
        
        # Validate basic house waybill information
        self.validate_waybill_info(data)
        
        # Validate party information
        self.validate_party_info(data)
        
        # Validate cargo information
        self.validate_cargo_info(data)
        
        # Validate handling information
        self.validate_handling_info(data)
        
        # Validate business rules
        self.validate_business_rules(data)
        
        return not self.has_errors()
    
    def validate_waybill_info(self, data):
        """Validate basic house waybill information."""
        # House AWB number is required
        if not self.validate_required_field(data.get('hawb_number'), 'hawb_number'):
            return
        
        # Validate house AWB number format (can be various formats)
        hawb_number = data.get('hawb_number')
        if hawb_number:
            self.validate_string_length(hawb_number, 'hawb_number', min_length=1, max_length=35)
        
        # Master AWB number is required
        if not self.validate_required_field(data.get('mawb_number'), 'mawb_number'):
            return
        
        # Validate master AWB number format
        self.validate_awb_number(data.get('mawb_number'), 'mawb_number')
        
        # Type code should be 703 for house waybills
        type_code = data.get('type_code')
        if type_code and type_code != '703':
            self.add_warning(f"Type code '{type_code}' is unusual for house waybills (expected '703')", 'type_code')
        
        # Airport codes validation
        self.validate_airport_code(data.get('origin_airport'), 'origin_airport')
        self.validate_airport_code(data.get('destination_airport'), 'destination_airport')
        
        # Check that origin and destination are different
        if (data.get('origin_airport') and data.get('destination_airport') and
            data.get('origin_airport') == data.get('destination_airport')):
            self.add_warning("Origin and destination airports are the same", 'airports')
    
    def validate_party_info(self, data):
        """Validate party information."""
        # Validate shipper data
        shipper_data = data.get('shipper_data')
        if shipper_data:
            self.validate_party_data(shipper_data, 'shipper')
        else:
            self.add_error("Shipper information is required", 'shipper_data')
        
        # Validate consignee data
        consignee_data = data.get('consignee_data')
        if consignee_data:
            self.validate_party_data(consignee_data, 'consignee')
        else:
            self.add_error("Consignee information is required", 'consignee_data')
    
    def validate_party_data(self, party_data, party_type):
        """Validate individual party data."""
        # Party name is required
        if not self.validate_required_field(party_data.get('name'), f'{party_type}_name'):
            return
        
        # Validate name length
        self.validate_string_length(party_data.get('name'), f'{party_type}_name', max_length=100)
        
        # Address validation (optional but validate if present)
        address = party_data.get('address')
        if address:
            self.validate_string_length(address, f'{party_type}_address', max_length=500)
        
        # City validation (optional)
        city = party_data.get('city')
        if city:
            self.validate_string_length(city, f'{party_type}_city', max_length=50)
        
        # Country code validation (optional but validate format if present)
        country_code = party_data.get('country_code')
        if country_code:
            self.validate_pattern(country_code, f'{party_type}_country_code', 
                                r'^[A-Z]{2}$', '2-letter country code')
        
        # Postal code validation (optional)
        postal_code = party_data.get('postal_code')
        if postal_code:
            self.validate_string_length(postal_code, f'{party_type}_postal_code', max_length=20)
    
    def validate_cargo_info(self, data):
        """Validate cargo information."""
        # Pieces validation
        total_pieces = data.get('total_pieces')
        if not self.validate_required_field(total_pieces, 'total_pieces'):
            return
        
        self.validate_numeric_range(total_pieces, 'total_pieces', min_value=1, max_value=99999)
        
        # Weight validation
        total_weight = data.get('total_weight')
        if not self.validate_required_field(total_weight, 'total_weight'):
            return
        
        self.validate_numeric_range(total_weight, 'total_weight', min_value=0.1, max_value=999999.99)
        
        # Weight unit validation
        weight_unit = data.get('weight_unit')
        if weight_unit:
            self.validate_choice(weight_unit, 'weight_unit', ['KGM', 'LBR'])
        
        # Goods description validation
        description = data.get('description')
        if description:
            self.validate_string_length(description, 'description', max_length=1000)
        else:
            self.add_warning("No goods description found", 'description')
    
    def validate_handling_info(self, data):
        """Validate handling instructions and special service codes."""
        # Special handling code validation
        shc_code = data.get('special_handling_code')
        if shc_code:
            self.validate_string_length(shc_code, 'special_handling_code', max_length=3)
            if not shc_code.isalpha():
                self.add_warning("Special handling code contains non-alphabetic characters", 
                               'special_handling_code')
    
    def validate_business_rules(self, data):
        """Validate business rules and logical consistency."""
        # House waybill should have a master AWB
        mawb_number = data.get('mawb_number')
        hawb_number = data.get('hawb_number')
        
        if mawb_number and hawb_number and mawb_number == hawb_number:
            self.add_error("House AWB number cannot be the same as Master AWB number", 'hawb_number')
        
        # Validate flag consistency
        is_mail = data.get('is_mail', False)
        is_human_remains = data.get('is_human_remains', False)
        
        if is_mail and is_human_remains:
            self.add_error("Shipment cannot be both mail and human remains", 'flags')
        
        # Weight and pieces consistency check
        total_pieces = data.get('total_pieces', 0)
        total_weight = data.get('total_weight', 0)
        
        if total_pieces > 0 and total_weight > 0:
            weight_per_piece = total_weight / total_pieces
            if weight_per_piece > 1000:  # More than 1000 kg per piece
                self.add_warning(f"Very high weight per piece: {weight_per_piece:.2f} kg", 'cargo_consistency')
            elif weight_per_piece < 0.1:  # Less than 100g per piece
                self.add_warning(f"Very low weight per piece: {weight_per_piece:.2f} kg", 'cargo_consistency')
        
        # House waybill specific validations
        if data.get('is_partial', False):
            self.add_warning("House waybills are typically not marked as partial", 'is_partial')
        
        # Validate that required master AWB exists (this would need database check in real implementation)
        # For now, just validate the format
        if mawb_number:
            if not self.validate_awb_number(mawb_number, 'mawb_number'):
                self.add_error("Invalid master AWB number format", 'mawb_number')
