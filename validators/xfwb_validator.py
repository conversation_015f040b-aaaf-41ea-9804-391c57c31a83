#!/usr/bin/env python3
"""
XFWB data validator.

This module provides functionality for validating data extracted from XFWB XML files.
"""

import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validators.base_validator import BaseValidator


class XFWBValidator(BaseValidator):
    """
    Validator for XFWB (Master Air Waybill) XML data.
    
    This class validates all extracted data from XFWB XML files including
    waybill information, party details, cargo details, and handling instructions.
    """
    
    def validate(self, data):
        """
        Validate extracted XFWB data.
        
        Args:
            data (dict): Extracted data to validate.
            
        Returns:
            bool: True if valid, False otherwise.
        """
        self.clear_errors()
        
        # Validate basic waybill information
        self.validate_waybill_info(data)
        
        # Validate party information
        self.validate_party_info(data)
        
        # Validate cargo information
        self.validate_cargo_info(data)
        
        # Validate handling information
        self.validate_handling_info(data)
        
        # Validate business rules
        self.validate_business_rules(data)
        
        return not self.has_errors()
    
    def validate_waybill_info(self, data):
        """Validate basic waybill information."""
        # AWB number is required
        if not self.validate_required_field(data.get('awb_number'), 'awb_number'):
            self.add_error("AWB number not found in XFWB file", 'awb_number')
            return
        
        # Validate AWB number format
        self.validate_awb_number(data.get('awb_number'))
        
        # Type code validation
        type_code = data.get('type_code')
        if not self.validate_required_field(type_code, 'type_code'):
            return
        
        self.validate_choice(type_code, 'type_code', ['740', '741'])
        
        # Airport codes validation
        self.validate_airport_code(data.get('origin_airport'), 'origin_airport')
        self.validate_airport_code(data.get('destination_airport'), 'destination_airport')
        
        # Check that origin and destination are different
        if (data.get('origin_airport') and data.get('destination_airport') and
            data.get('origin_airport') == data.get('destination_airport')):
            self.add_warning("Origin and destination airports are the same", 'airports')
    
    def validate_party_info(self, data):
        """Validate party information."""
        # Validate shipper data
        shipper_data = data.get('shipper_data')
        if shipper_data:
            self.validate_party_data(shipper_data, 'shipper')
        else:
            self.add_error("Shipper information is required", 'shipper_data')
        
        # Validate consignee data
        consignee_data = data.get('consignee_data')
        if consignee_data:
            self.validate_party_data(consignee_data, 'consignee')
        else:
            self.add_error("Consignee information is required", 'consignee_data')
        
        # Agent data is optional but validate if present
        agent_data = data.get('agent_data')
        if agent_data:
            self.validate_party_data(agent_data, 'agent')
        
        # Carrier data is optional but validate if present
        carrier_data = data.get('carrier_data')
        if carrier_data:
            self.validate_carrier_data(carrier_data)
    
    def validate_party_data(self, party_data, party_type):
        """Validate individual party data."""
        # Party name is required
        if not self.validate_required_field(party_data.get('name'), f'{party_type}_name'):
            return
        
        # Validate name length
        self.validate_string_length(party_data.get('name'), f'{party_type}_name', max_length=100)
        
        # Address validation (optional but validate if present)
        address = party_data.get('address')
        if address:
            self.validate_string_length(address, f'{party_type}_address', max_length=500)
        
        # City validation (optional)
        city = party_data.get('city')
        if city:
            self.validate_string_length(city, f'{party_type}_city', max_length=50)
        
        # Country code validation (optional but validate format if present)
        country_code = party_data.get('country_code')
        if country_code:
            self.validate_pattern(country_code, f'{party_type}_country_code', 
                                r'^[A-Z]{2}$', '2-letter country code')
        
        # Postal code validation (optional)
        postal_code = party_data.get('postal_code')
        if postal_code:
            self.validate_string_length(postal_code, f'{party_type}_postal_code', max_length=20)
    
    def validate_carrier_data(self, carrier_data):
        """Validate carrier data."""
        # Carrier code validation
        code = carrier_data.get('code')
        if code:
            self.validate_string_length(code, 'carrier_code', max_length=10)
            # Carrier codes are typically 2-3 characters
            if len(code) < 2:
                self.add_warning("Carrier code is unusually short", 'carrier_code')
    
    def validate_cargo_info(self, data):
        """Validate cargo information."""
        # Pieces validation
        total_pieces = data.get('total_pieces')
        if not self.validate_required_field(total_pieces, 'total_pieces'):
            return
        
        self.validate_numeric_range(total_pieces, 'total_pieces', min_value=1, max_value=99999)
        
        # Weight validation
        total_weight = data.get('total_weight')
        if not self.validate_required_field(total_weight, 'total_weight'):
            return
        
        self.validate_numeric_range(total_weight, 'total_weight', min_value=0.1, max_value=999999.99)
        
        # Weight unit validation
        weight_unit = data.get('weight_unit')
        if weight_unit:
            self.validate_choice(weight_unit, 'weight_unit', ['KGM', 'LBR'])
        
        # Volume validation (optional)
        gross_volume = data.get('gross_volume')
        if gross_volume is not None:
            self.validate_numeric_range(gross_volume, 'gross_volume', min_value=0.001, max_value=99999.999)
        
        # Volume unit validation
        volume_unit = data.get('volume_unit')
        if volume_unit:
            self.validate_choice(volume_unit, 'volume_unit', ['MC', 'MTQ', 'FTQ'])
        
        # Goods descriptions validation
        descriptions = data.get('goods_descriptions')
        if descriptions:
            if not isinstance(descriptions, list):
                self.add_error("Goods descriptions must be a list", 'goods_descriptions')
            elif len(descriptions) == 0:
                self.add_warning("No goods descriptions found", 'goods_descriptions')
            else:
                for i, desc in enumerate(descriptions):
                    if not desc or not desc.strip():
                        self.add_warning(f"Empty goods description at index {i}", 'goods_descriptions')
                    elif len(desc) > 1000:
                        self.add_warning(f"Goods description {i} is very long ({len(desc)} characters)", 
                                       'goods_descriptions')
        
        # Summary description validation
        summary_desc = data.get('summary_description')
        if summary_desc:
            self.validate_string_length(summary_desc, 'summary_description', max_length=200)
    
    def validate_handling_info(self, data):
        """Validate handling instructions and special service codes."""
        # Special handling codes validation
        shc_codes = data.get('special_handling_codes')
        if shc_codes:
            if not isinstance(shc_codes, list):
                self.add_error("Special handling codes must be a list", 'special_handling_codes')
            else:
                for i, code in enumerate(shc_codes):
                    if not code or len(code) > 3:
                        self.add_error(f"Invalid special handling code at index {i}: '{code}'", 
                                     'special_handling_codes')
                    elif not code.isalpha():
                        self.add_warning(f"Special handling code '{code}' contains non-alphabetic characters", 
                                       'special_handling_codes')
        
        # Primary special handling code validation
        primary_shc = data.get('special_handling_code')
        if primary_shc:
            self.validate_string_length(primary_shc, 'special_handling_code', max_length=3)
            if not primary_shc.isalpha():
                self.add_warning("Primary special handling code contains non-alphabetic characters", 
                               'special_handling_code')
    
    def validate_business_rules(self, data):
        """Validate business rules and logical consistency."""
        # Type code 741 should have house waybills or consolidation indicators
        type_code = data.get('type_code')
        if type_code == '741':
            # This is a master waybill that should contain house waybills
            # We can't validate the actual presence of house waybills here since
            # they're in separate XML files, but we can check for consolidation indicators
            descriptions = data.get('goods_descriptions', [])
            has_consolidation_indicator = any('CONSOLIDATION' in desc.upper() for desc in descriptions)
            
            if not has_consolidation_indicator:
                self.add_warning("Type code 741 (Master AWB) but no consolidation indicators found", 
                               'type_code')
        
        # Validate currency and payment consistency
        currency_code = data.get('currency_code')
        prepaid_collect = data.get('prepaid_collect_indicator')
        
        if currency_code and len(currency_code) != 3:
            self.add_error("Currency code must be 3 characters", 'currency_code')
        
        if prepaid_collect and prepaid_collect not in ['P', 'C']:
            self.add_error("Prepaid/collect indicator must be 'P' or 'C'", 'prepaid_collect_indicator')
        
        # Validate flag consistency
        is_mail = data.get('is_mail', False)
        is_human_remains = data.get('is_human_remains', False)
        
        if is_mail and is_human_remains:
            self.add_error("Shipment cannot be both mail and human remains", 'flags')
        
        # Weight and pieces consistency check
        total_pieces = data.get('total_pieces', 0)
        total_weight = data.get('total_weight', 0)
        
        if total_pieces > 0 and total_weight > 0:
            weight_per_piece = total_weight / total_pieces
            if weight_per_piece > 1000:  # More than 1000 kg per piece
                self.add_warning(f"Very high weight per piece: {weight_per_piece:.2f} kg", 'cargo_consistency')
            elif weight_per_piece < 0.1:  # Less than 100g per piece
                self.add_warning(f"Very low weight per piece: {weight_per_piece:.2f} kg", 'cargo_consistency')
        
        # Volume and weight consistency check
        gross_volume = data.get('gross_volume')
        if gross_volume and total_weight > 0:
            density = total_weight / gross_volume  # kg/m³
            if density > 2000:  # Very dense cargo
                self.add_warning(f"Very high cargo density: {density:.2f} kg/m³", 'cargo_consistency')
            elif density < 10:  # Very light cargo
                self.add_warning(f"Very low cargo density: {density:.2f} kg/m³", 'cargo_consistency')
