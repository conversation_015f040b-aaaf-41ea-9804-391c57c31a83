#!/usr/bin/env python3
"""
Configuration Management System for XML Parsers.

This module provides centralized configuration management with support for
YAML/JSON files, environment-specific settings, and validation rules.
"""

import os
import logging
import yaml
import json
from typing import Dict, Any, Optional, List
from pathlib import Path


class ParserConfiguration:
    """Centralized configuration management for XML parser."""
    
    def __init__(self, config_file: Optional[str] = None, environment: str = 'production'):
        """
        Initialize configuration.
        
        Args:
            config_file: Path to configuration file (YAML or JSON)
            environment: Environment name (development, staging, production)
        """
        self.environment = environment
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Default configuration
        self.config = self._get_default_config()
        
        # Load from file if provided
        if config_file:
            self.load_from_file(config_file)
        
        # Override with environment variables
        self._load_from_environment()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'validation': {
                'strict_mode': True,
                'fail_on_errors': False,
                'max_warnings': 50,
                'max_hints': 100,
                'required_fields': [
                    'awb_number', 'origin_airport', 'destination_airport',
                    'total_pieces', 'total_weight', 'shipper_name', 'consignee_name'
                ],
                'awb_validation': {
                    'check_digit_validation': True,
                    'format_validation': True,
                    'length_validation': True
                },
                'airport_validation': {
                    'format_validation': True,
                    'case_validation': True
                },
                'weight_validation': {
                    'min_weight': 0.01,
                    'max_weight': 50000.0,
                    'warning_threshold': 10000.0
                },
                'pieces_validation': {
                    'min_pieces': 1,
                    'max_pieces': 10000,
                    'warning_threshold': 1000
                }
            },
            'conversion': {
                'output_formats': ['database', 'one_record'],
                'ontology_version': '2.1',
                'include_validation_results': True,
                'include_quality_metrics': True,
                'one_record_context': 'https://onerecord.iata.org/ns/cargo#'
            },
            'performance': {
                'enable_caching': True,
                'cache_size': 1000,
                'cache_ttl': 3600,
                'batch_size': 100,
                'max_processing_time_ms': 30000,
                'enable_profiling': False,
                'memory_limit_mb': 512
            },
            'database': {
                'connection_pool_size': 10,
                'connection_timeout': 30,
                'retry_attempts': 3,
                'retry_delay': 1,
                'enable_transactions': True,
                'isolation_level': 'READ_COMMITTED'
            },
            'uld_management': {
                'enable_uld_tracking': True,
                'exclude_bulk_cargo': True,
                'bulk_cargo_types': ['BLK', 'BULK'],
                'auto_create_uld': True,
                'uld_status_workflow': ['PENDING', 'LOADED', 'DEPARTED', 'ARRIVED', 'UNLOADED'],
                'default_uld_type': 'AKE',
                'track_uld_movements': True
            },
            'xml_standards': {
                'supported_versions': ['3.00', '2.00'],
                'default_version': '3.00',
                'enable_backward_compatibility': True,
                'namespace_handling': 'auto',
                'validate_schema': False
            },
            'cimp_processing': {
                'enable_segment_processing': True,
                'validate_segment_order': True,
                'required_segments': ['02', '05', '06'],  # AWB details, Shipper, Consignee
                'optional_segments': ['03', '04', '07', '08', '09', '10'],
                'segment_validation': True
            },
            'monitoring': {
                'enable_metrics': True,
                'enable_alerts': True,
                'alert_thresholds': {
                    'error_rate': 0.1,
                    'processing_time_ms': 30000,
                    'quality_score': 0.7
                },
                'metrics_retention_days': 30,
                'health_check_interval': 60
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_logging': True,
                'console_logging': True,
                'log_file': 'xml_parser.log',
                'max_file_size_mb': 10,
                'backup_count': 5
            }
        }
    
    def load_from_file(self, config_file: str) -> None:
        """
        Load configuration from YAML or JSON file.
        
        Args:
            config_file: Path to configuration file
        """
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                self.logger.warning(f"Configuration file not found: {config_file}")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    self.logger.error(f"Unsupported configuration file format: {config_path.suffix}")
                    return
            
            # Merge with default configuration
            self._merge_config(self.config, file_config)
            self.logger.info(f"Loaded configuration from: {config_file}")
            
        except Exception as e:
            self.logger.error(f"Error loading configuration file {config_file}: {e}")
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables."""
        env_mappings = {
            'XML_PARSER_VALIDATION_STRICT': ('validation', 'strict_mode'),
            'XML_PARSER_VALIDATION_FAIL_ON_ERRORS': ('validation', 'fail_on_errors'),
            'XML_PARSER_CACHE_ENABLED': ('performance', 'enable_caching'),
            'XML_PARSER_CACHE_SIZE': ('performance', 'cache_size'),
            'XML_PARSER_CACHE_TTL': ('performance', 'cache_ttl'),
            'XML_PARSER_DB_POOL_SIZE': ('database', 'connection_pool_size'),
            'XML_PARSER_DB_TIMEOUT': ('database', 'connection_timeout'),
            'XML_PARSER_ULD_TRACKING': ('uld_management', 'enable_uld_tracking'),
            'XML_PARSER_LOG_LEVEL': ('logging', 'level'),
            'XML_PARSER_METRICS_ENABLED': ('monitoring', 'enable_metrics')
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert string values to appropriate types
                converted_value = self._convert_env_value(value)
                self._set_nested_config(section, key, converted_value)
    
    def _convert_env_value(self, value: str) -> Any:
        """Convert environment variable string to appropriate type."""
        # Boolean conversion
        if value.lower() in ['true', 'yes', '1', 'on']:
            return True
        elif value.lower() in ['false', 'no', '0', 'off']:
            return False
        
        # Number conversion
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def _set_nested_config(self, section: str, key: str, value: Any) -> None:
        """Set nested configuration value."""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def _merge_config(self, base_config: Dict[str, Any], new_config: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries."""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get(self, section: str, key: Optional[str] = None, default: Any = None) -> Any:
        """
        Get configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key (optional)
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        if key is None:
            return self.config.get(section, default)
        
        section_config = self.config.get(section, {})
        return section_config.get(key, default)
    
    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to set
        """
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration."""
        return self.config.get('validation', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration."""
        return self.config.get('performance', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return self.config.get('database', {})
    
    def get_uld_config(self) -> Dict[str, Any]:
        """Get ULD management configuration."""
        return self.config.get('uld_management', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration."""
        return self.config.get('monitoring', {})
    
    def is_strict_mode(self) -> bool:
        """Check if strict validation mode is enabled."""
        return self.get('validation', 'strict_mode', True)
    
    def should_fail_on_errors(self) -> bool:
        """Check if parser should fail on validation errors."""
        return self.get('validation', 'fail_on_errors', False)
    
    def is_uld_tracking_enabled(self) -> bool:
        """Check if ULD tracking is enabled."""
        return self.get('uld_management', 'enable_uld_tracking', True)
    
    def get_bulk_cargo_types(self) -> List[str]:
        """Get list of bulk cargo types to exclude from ULD tracking."""
        return self.get('uld_management', 'bulk_cargo_types', ['BLK', 'BULK'])
    
    def save_to_file(self, config_file: str, format: str = 'yaml') -> None:
        """
        Save current configuration to file.
        
        Args:
            config_file: Path to save configuration
            format: File format ('yaml' or 'json')
        """
        try:
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(self.config, f, default_flow_style=False, indent=2)
                elif format.lower() == 'json':
                    json.dump(self.config, f, indent=2)
                else:
                    raise ValueError(f"Unsupported format: {format}")
            
            self.logger.info(f"Configuration saved to: {config_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving configuration to {config_file}: {e}")
    
    def validate_config(self) -> List[str]:
        """
        Validate configuration and return list of issues.
        
        Returns:
            List of validation issues
        """
        issues = []
        
        # Validate required sections
        required_sections = ['validation', 'performance', 'database']
        for section in required_sections:
            if section not in self.config:
                issues.append(f"Missing required configuration section: {section}")
        
        # Validate specific values
        cache_size = self.get('performance', 'cache_size', 0)
        if cache_size < 0:
            issues.append("Cache size must be non-negative")
        
        pool_size = self.get('database', 'connection_pool_size', 0)
        if pool_size <= 0:
            issues.append("Database connection pool size must be positive")
        
        return issues
