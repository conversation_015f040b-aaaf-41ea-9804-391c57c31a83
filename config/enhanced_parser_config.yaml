# Enhanced XML Parser Configuration
# Air Cargo Malawi - Enhanced Parser System

validation:
  strict_mode: true
  fail_on_errors: false
  max_warnings: 50
  max_hints: 100
  required_fields:
    - awb_number
    - origin_airport
    - destination_airport
    - total_pieces
    - total_weight
    - shipper_name
    - consignee_name
  
  awb_validation:
    check_digit_validation: true
    format_validation: true
    length_validation: true
  
  airport_validation:
    format_validation: true
    case_validation: true
  
  weight_validation:
    min_weight: 0.01
    max_weight: 50000.0
    warning_threshold: 10000.0
  
  pieces_validation:
    min_pieces: 1
    max_pieces: 10000
    warning_threshold: 1000

conversion:
  output_formats:
    - database
    - one_record
  ontology_version: "2.1"
  include_validation_results: true
  include_quality_metrics: true
  one_record_context: "https://onerecord.iata.org/ns/cargo#"

performance:
  enable_caching: true
  cache_size: 1000
  cache_ttl: 3600
  batch_size: 100
  max_processing_time_ms: 30000
  enable_profiling: false
  memory_limit_mb: 512

database:
  connection_pool_size: 10
  connection_timeout: 30
  retry_attempts: 3
  retry_delay: 1
  enable_transactions: true
  isolation_level: "READ_COMMITTED"

uld_management:
  enable_uld_tracking: true
  exclude_bulk_cargo: true
  bulk_cargo_types:
    - BLK
    - BULK
  auto_create_uld: true
  uld_status_workflow:
    - PENDING
    - LOADED
    - DEPARTED
    - ARRIVED
    - UNLOADED
  default_uld_type: "AKE"
  track_uld_movements: true

xml_standards:
  supported_versions:
    - "3.00"
    - "2.00"
  default_version: "3.00"
  enable_backward_compatibility: true
  namespace_handling: "auto"
  validate_schema: false

cimp_processing:
  enable_segment_processing: true
  validate_segment_order: true
  required_segments:
    - "02"  # AWB details
    - "05"  # Shipper
    - "06"  # Consignee
  optional_segments:
    - "03"  # Flight Booking
    - "04"  # Flight Routing
    - "07"  # Agent
    - "08"  # Accounting
    - "09"  # Shipment Description
    - "10"  # Customs
  segment_validation: true

monitoring:
  enable_metrics: true
  enable_alerts: true
  alert_thresholds:
    error_rate: 0.1
    processing_time_ms: 30000
    quality_score: 0.7
    memory_usage_mb: 512
    cpu_usage_percent: 80
  metrics_retention_days: 30
  health_check_interval: 60

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: true
  console_logging: true
  log_file: "xml_parser.log"
  max_file_size_mb: 10
  backup_count: 5

# Environment-specific overrides
environments:
  development:
    validation:
      strict_mode: false
      fail_on_errors: false
    logging:
      level: "DEBUG"
    monitoring:
      enable_alerts: false
  
  staging:
    validation:
      strict_mode: true
      fail_on_errors: false
    monitoring:
      enable_alerts: true
      alert_thresholds:
        error_rate: 0.15
        processing_time_ms: 45000
  
  production:
    validation:
      strict_mode: true
      fail_on_errors: false
    performance:
      enable_profiling: false
    monitoring:
      enable_alerts: true
      alert_thresholds:
        error_rate: 0.05
        processing_time_ms: 20000
        quality_score: 0.8
