#!/usr/bin/env python3
"""
Performance Monitoring System for XML Parsers.

This module provides comprehensive performance monitoring, metrics collection,
and alerting capabilities for the XML parser system.
"""

import logging
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from collections import deque
import json
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validation.validation_framework import ValidationResult
from validation.data_quality import DataQualityMetrics


class PerformanceMetrics:
    """Container for performance metrics."""
    
    def __init__(self):
        """Initialize metrics."""
        self.files_processed = 0
        self.total_processing_time_ms = 0
        self.errors_count = 0
        self.warnings_count = 0
        self.hints_count = 0
        self.average_quality_score = 0.0
        self.memory_usage_mb = 0.0
        self.cpu_usage_percent = 0.0
        self.processing_times = deque(maxlen=1000)  # Keep last 1000 processing times
        self.quality_scores = deque(maxlen=1000)    # Keep last 1000 quality scores
        self.start_time = datetime.now()
        self.last_reset = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        
        return {
            'files_processed': self.files_processed,
            'total_processing_time_ms': self.total_processing_time_ms,
            'average_processing_time_ms': self._calculate_average_processing_time(),
            'errors_count': self.errors_count,
            'warnings_count': self.warnings_count,
            'hints_count': self.hints_count,
            'average_quality_score': round(self.average_quality_score, 3),
            'memory_usage_mb': round(self.memory_usage_mb, 2),
            'cpu_usage_percent': round(self.cpu_usage_percent, 2),
            'uptime_seconds': round(uptime_seconds, 2),
            'throughput_files_per_hour': self._calculate_throughput(),
            'last_reset': self.last_reset.isoformat(),
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_average_processing_time(self) -> float:
        """Calculate average processing time from recent samples."""
        if not self.processing_times:
            return 0.0
        return sum(self.processing_times) / len(self.processing_times)
    
    def _calculate_throughput(self) -> float:
        """Calculate files processed per hour."""
        uptime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
        if uptime_hours == 0:
            return 0.0
        return self.files_processed / uptime_hours


class AlertManager:
    """Manages alerts and notifications."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize alert manager.
        
        Args:
            config: Alert configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.alert_handlers: List[Callable[[str, str, Dict[str, Any]], None]] = []
        self.alert_history = deque(maxlen=100)  # Keep last 100 alerts
    
    def add_alert_handler(self, handler: Callable[[str, str, Dict[str, Any]], None]) -> None:
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    def check_and_send_alerts(self, metrics: PerformanceMetrics, 
                            validation_result: ValidationResult,
                            quality_metrics: DataQualityMetrics) -> None:
        """Check metrics against thresholds and send alerts if needed."""
        thresholds = self.config.get('alert_thresholds', {})
        
        # Check error rate
        if metrics.files_processed > 0:
            error_rate = metrics.errors_count / metrics.files_processed
            if error_rate > thresholds.get('error_rate', 0.1):
                self._send_alert(
                    'HIGH_ERROR_RATE',
                    f'Error rate is {error_rate:.2%} (threshold: {thresholds.get("error_rate", 0.1):.2%})',
                    {'error_rate': error_rate, 'errors_count': metrics.errors_count}
                )
        
        # Check processing time
        avg_processing_time = metrics._calculate_average_processing_time()
        max_processing_time = thresholds.get('processing_time_ms', 30000)
        if avg_processing_time > max_processing_time:
            self._send_alert(
                'HIGH_PROCESSING_TIME',
                f'Average processing time is {avg_processing_time:.0f}ms (threshold: {max_processing_time}ms)',
                {'avg_processing_time_ms': avg_processing_time}
            )
        
        # Check quality score
        min_quality_score = thresholds.get('quality_score', 0.7)
        if quality_metrics.overall_score < min_quality_score:
            self._send_alert(
                'LOW_QUALITY_SCORE',
                f'Quality score is {quality_metrics.overall_score:.3f} (threshold: {min_quality_score})',
                {'quality_score': quality_metrics.overall_score}
            )
        
        # Check memory usage
        max_memory_mb = thresholds.get('memory_usage_mb', 512)
        if metrics.memory_usage_mb > max_memory_mb:
            self._send_alert(
                'HIGH_MEMORY_USAGE',
                f'Memory usage is {metrics.memory_usage_mb:.1f}MB (threshold: {max_memory_mb}MB)',
                {'memory_usage_mb': metrics.memory_usage_mb}
            )
        
        # Check CPU usage
        max_cpu_percent = thresholds.get('cpu_usage_percent', 80)
        if metrics.cpu_usage_percent > max_cpu_percent:
            self._send_alert(
                'HIGH_CPU_USAGE',
                f'CPU usage is {metrics.cpu_usage_percent:.1f}% (threshold: {max_cpu_percent}%)',
                {'cpu_usage_percent': metrics.cpu_usage_percent}
            )
    
    def _send_alert(self, alert_type: str, message: str, data: Dict[str, Any]) -> None:
        """Send alert to all registered handlers."""
        alert = {
            'type': alert_type,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
        self.alert_history.append(alert)
        self.logger.warning(f"ALERT [{alert_type}]: {message}")
        
        # Send to all handlers
        for handler in self.alert_handlers:
            try:
                handler(alert_type, message, data)
            except Exception as e:
                self.logger.error(f"Error in alert handler: {e}")


class PerformanceMonitor:
    """Monitor parser performance and generate alerts."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize performance monitor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.metrics = PerformanceMetrics()
        self.alert_manager = AlertManager(config.get('monitoring', {}))
        self.monitoring_enabled = config.get('monitoring', {}).get('enable_metrics', True)
        self.alerts_enabled = config.get('monitoring', {}).get('enable_alerts', True)
        
        # System monitoring
        self._system_monitor_thread = None
        self._stop_monitoring = threading.Event()
        
        if self.monitoring_enabled:
            self._start_system_monitoring()
    
    def _start_system_monitoring(self) -> None:
        """Start system resource monitoring in background thread."""
        def monitor_system():
            while not self._stop_monitoring.is_set():
                try:
                    # Update system metrics
                    process = psutil.Process()
                    self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
                    self.metrics.cpu_usage_percent = process.cpu_percent()
                    
                    # Sleep for monitoring interval
                    self._stop_monitoring.wait(5)  # Update every 5 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {e}")
                    self._stop_monitoring.wait(10)  # Wait longer on error
        
        self._system_monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        self._system_monitor_thread.start()
        self.logger.info("Started system monitoring thread")
    
    def record_processing_metrics(self, file_name: str, processing_time_ms: int,
                                validation_result: ValidationResult,
                                quality_metrics: DataQualityMetrics) -> None:
        """
        Record metrics for a processed file.
        
        Args:
            file_name: Name of processed file
            processing_time_ms: Processing time in milliseconds
            validation_result: Validation results
            quality_metrics: Data quality metrics
        """
        if not self.monitoring_enabled:
            return
        
        try:
            # Update counters
            self.metrics.files_processed += 1
            self.metrics.total_processing_time_ms += processing_time_ms
            self.metrics.errors_count += len(validation_result.errors)
            self.metrics.warnings_count += len(validation_result.warnings)
            self.metrics.hints_count += len(validation_result.hints)
            
            # Update processing times
            self.metrics.processing_times.append(processing_time_ms)
            
            # Update quality scores
            self.metrics.quality_scores.append(quality_metrics.overall_score)
            
            # Calculate average quality score
            if self.metrics.quality_scores:
                self.metrics.average_quality_score = sum(self.metrics.quality_scores) / len(self.metrics.quality_scores)
            
            # Log processing metrics
            self.logger.debug(
                f"Processed {file_name}: {processing_time_ms}ms, "
                f"Quality: {quality_metrics.overall_score:.3f}, "
                f"Errors: {len(validation_result.errors)}, "
                f"Warnings: {len(validation_result.warnings)}"
            )
            
            # Check for alerts
            if self.alerts_enabled:
                self.alert_manager.check_and_send_alerts(
                    self.metrics, validation_result, quality_metrics
                )
            
            # Store metrics to database if configured
            self._store_metrics_to_database(file_name, processing_time_ms, validation_result, quality_metrics)
            
        except Exception as e:
            self.logger.error(f"Error recording processing metrics: {e}")
    
    def _store_metrics_to_database(self, file_name: str, processing_time_ms: int,
                                 validation_result: ValidationResult,
                                 quality_metrics: DataQualityMetrics) -> None:
        """Store metrics to database for historical analysis."""
        # This would be implemented to store metrics in the database
        # For now, we'll just log the metrics
        self.logger.debug(f"Storing metrics for {file_name} to database")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return self.metrics.to_dict()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get system health status."""
        try:
            # Check database connectivity
            db_healthy = self._check_database_health()
            
            # Check parser performance
            performance_healthy = self._check_parser_performance()
            
            # Check system resources
            resources_healthy = self._check_system_resources()
            
            overall_healthy = db_healthy and performance_healthy and resources_healthy
            
            return {
                'healthy': overall_healthy,
                'database': db_healthy,
                'performance': performance_healthy,
                'resources': resources_healthy,
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': (datetime.now() - self.metrics.start_time).total_seconds()
            }
            
        except Exception as e:
            self.logger.error(f"Error checking health status: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _check_database_health(self) -> bool:
        """Check database connection health."""
        # This would implement actual database health check
        # For now, return True
        return True
    
    def _check_parser_performance(self) -> bool:
        """Check parser performance metrics."""
        # Check if average processing time is within acceptable limits
        avg_time = self.metrics._calculate_average_processing_time()
        max_time = self.config.get('monitoring', {}).get('alert_thresholds', {}).get('processing_time_ms', 30000)
        
        return avg_time <= max_time
    
    def _check_system_resources(self) -> bool:
        """Check system resource usage."""
        # Check memory and CPU usage
        max_memory = self.config.get('monitoring', {}).get('alert_thresholds', {}).get('memory_usage_mb', 512)
        max_cpu = self.config.get('monitoring', {}).get('alert_thresholds', {}).get('cpu_usage_percent', 80)
        
        return (self.metrics.memory_usage_mb <= max_memory and 
                self.metrics.cpu_usage_percent <= max_cpu)
    
    def reset_metrics(self) -> None:
        """Reset all metrics counters."""
        self.logger.info("Resetting performance metrics")
        self.metrics = PerformanceMetrics()
    
    def export_metrics(self, format: str = 'json') -> str:
        """
        Export metrics in specified format.
        
        Args:
            format: Export format ('json', 'csv')
            
        Returns:
            Exported metrics as string
        """
        if format.lower() == 'json':
            return json.dumps(self.metrics.to_dict(), indent=2)
        elif format.lower() == 'csv':
            # Implement CSV export if needed
            return "CSV export not implemented"
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def add_alert_handler(self, handler: Callable[[str, str, Dict[str, Any]], None]) -> None:
        """Add custom alert handler."""
        self.alert_manager.add_alert_handler(handler)
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        if self._system_monitor_thread and self._system_monitor_thread.is_alive():
            self._stop_monitoring.set()
            self._system_monitor_thread.join(timeout=10)
            self.logger.info("Stopped system monitoring thread")


# Example alert handlers
def log_alert_handler(alert_type: str, message: str, data: Dict[str, Any]) -> None:
    """Simple log-based alert handler."""
    logger = logging.getLogger('AlertHandler')
    logger.warning(f"ALERT [{alert_type}]: {message} - Data: {data}")


def email_alert_handler(alert_type: str, message: str, data: Dict[str, Any]) -> None:
    """Email-based alert handler (placeholder)."""
    # This would implement actual email sending
    logger = logging.getLogger('EmailAlertHandler')
    logger.info(f"Would send email alert: [{alert_type}] {message}")


def webhook_alert_handler(alert_type: str, message: str, data: Dict[str, Any]) -> None:
    """Webhook-based alert handler (placeholder)."""
    # This would implement actual webhook posting
    logger = logging.getLogger('WebhookAlertHandler')
    logger.info(f"Would send webhook alert: [{alert_type}] {message}")
