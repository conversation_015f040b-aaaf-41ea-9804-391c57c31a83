#!/usr/bin/env python3
"""
XFFM (Flight Manifest) XML Parser wrapper script.
This script processes XFFM XML files and updates or inserts flight manifests,
ULD details, and AWB allocations to ULDs.
"""

import json
import logging
import os
import sys
import time

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the modular parser
from parsers.xffm_parser import XFFMParser

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("xffm_processor.log"),
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger("XFFMProcessor")


def process_xffm(xml_file, branch_id=1, user_id=1):
    """Process XFFM XML file and return structured data."""
    parser = None
    start_time = time.time()

    try:
        # Initialize the parser with modular architecture
        logger.info(f"Starting to process XFFM file: {xml_file}")
        parser = XFFMParser(user_id=user_id, branch_id=branch_id)

        # Parse the file using modular architecture
        logger.info(f"Parsing XFFM file: {xml_file}")
        parse_start = time.time()
        result = parser.parse_file(xml_file)
        parse_end = time.time()
        logger.info(f"Parsing completed in {parse_end - parse_start:.2f} seconds")

        if not result["success"]:
            error_msg = "Failed to parse XFFM file"
            if result["errors"]:
                error_msg += f": {'; '.join(result['errors'])}"
            raise Exception(error_msg)

        manifest_data = result["data"]
        manifest_id = manifest_data.get("manifest_id", "Unknown")
        flight_number = manifest_data.get("flight_number", "Unknown")

        end_time = time.time()
        logger.info(f"Successfully processed XFFM file: {xml_file}")
        logger.info(
            f"Created flight manifest with ID: {manifest_id} (Flight: {flight_number})"
        )
        logger.info(f"Manifest DB ID: {result['manifest_id']}")
        logger.info(f"Total processing time: {end_time - start_time:.2f} seconds")

        # Log any warnings
        if result["warnings"]:
            logger.warning("Processing warnings:")
            for warning in result["warnings"]:
                logger.warning(f"  - {warning}")

        return {
            "success": True,
            "manifest_id": manifest_id,
            "manifest_db_id": result["manifest_id"],
            "flight_number": flight_number,
            "carrier_code": manifest_data.get("carrier_code", "Unknown"),
            "departure_airport": manifest_data.get("departure_airport", "Unknown"),
            "arrival_airport": manifest_data.get("arrival_airport", "Unknown"),
            "uld_count": len(manifest_data.get("ulds", [])),
            "uld_ids": result["uld_ids"],
            "awb_count": len(result.get("awb_ids", [])),
            "awb_ids": result.get("awb_ids", []),
            "processing_time": end_time - start_time,
            "warnings": result["warnings"],
        }
    except Exception as e:
        logger.error(f"Error processing XFFM file: {e}")
        import traceback

        logger.error(traceback.format_exc())
        raise
    finally:
        if parser:
            parser.close()


def main():
    """Main function."""
    # Check if we're being called with standardized parameters: <xml_file_path> <user_id> <branch_id>
    if len(sys.argv) == 4 and os.path.isfile(sys.argv[1]):
        xml_file = sys.argv[1]
        try:
            user_id = int(sys.argv[2])
            branch_id = int(sys.argv[3])
        except ValueError:
            print(
                json.dumps(
                    {
                        "success": False,
                        "error": "User ID and Branch ID must be integers",
                    }
                )
            )
            return 1

        if not os.path.exists(xml_file):
            print(
                json.dumps(
                    {"success": False, "error": f"File {xml_file} does not exist"}
                )
            )
            return 1

        try:
            result = process_xffm(xml_file, branch_id, user_id)
            print(json.dumps(result))
            return 0
        except Exception as e:
            print(json.dumps({"success": False, "error": str(e)}))
            return 1
    else:
        print("Usage: python process_xffm.py <xml_file_path> <user_id> <branch_id>")
        return 1


if __name__ == "__main__":
    sys.exit(main())
