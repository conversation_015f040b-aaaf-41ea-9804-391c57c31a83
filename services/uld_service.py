#!/usr/bin/env python3
"""
ULD (Unit Load Device) Management Service.

This service handles ULD inventory tracking, status management, and flight associations
for the Air Cargo Handling application. It excludes BLK/BULK cargo types as these
are loose cargo without containers.
"""

import logging
import psycopg2
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from enum import Enum


class ULDStatus(Enum):
    """ULD status enumeration."""
    PENDING = "PENDING"
    LOADED = "LOADED"
    DEPARTED = "DEPARTED"
    ARRIVED = "ARRIVED"
    UNLOADED = "UNLOADED"
    AVAILABLE = "AVAILABLE"
    MAINTENANCE = "MAINTENANCE"


class ULDType(Enum):
    """Common ULD type codes."""
    AKE = "AKE"  # LD3 Container
    AKH = "AKH"  # LD3 Container (high)
    AMA = "AMA"  # LD7 Container
    AMF = "AMF"  # LD7 Container (fork)
    AMP = "AMP"  # LD7 Pallet
    PAG = "PAG"  # 88x125 Pallet
    PMC = "PMC"  # 96x125 Pallet
    PLA = "PLA"  # 88x125 Pallet (automotive)


class ULDService:
    """Service for managing ULD inventory and tracking."""
    
    def __init__(self, db_connection, config: Dict[str, Any]):
        """
        Initialize ULD service.
        
        Args:
            db_connection: Database connection
            config: Configuration dictionary
        """
        self.db_connection = db_connection
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Get ULD configuration
        self.uld_config = config.get('uld_management', {})
        self.bulk_cargo_types = self.uld_config.get('bulk_cargo_types', ['BLK', 'BULK'])
        self.auto_create_uld = self.uld_config.get('auto_create_uld', True)
        self.track_movements = self.uld_config.get('track_uld_movements', True)
    
    def process_uld_from_manifest(self, manifest_data: Dict[str, Any], 
                                branch_id: int, user_id: int) -> List[Dict[str, Any]]:
        """
        Process ULD information from flight manifest data.
        
        Args:
            manifest_data: Flight manifest data
            branch_id: Branch ID
            user_id: User ID
            
        Returns:
            List of processed ULD records
        """
        processed_ulds = []
        
        try:
            # Extract ULD information from manifest
            uld_data_list = self._extract_uld_data(manifest_data)
            
            for uld_data in uld_data_list:
                # Skip bulk cargo types
                if self._is_bulk_cargo(uld_data):
                    self.logger.info(f"Skipping bulk cargo ULD: {uld_data.get('uld_id')}")
                    continue
                
                # Process ULD
                processed_uld = self._process_single_uld(uld_data, branch_id, user_id)
                if processed_uld:
                    processed_ulds.append(processed_uld)
        
        except Exception as e:
            self.logger.error(f"Error processing ULD from manifest: {e}")
            raise
        
        return processed_ulds
    
    def _extract_uld_data(self, manifest_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract ULD data from manifest."""
        uld_data_list = []
        
        # Extract from ULD details if present
        uld_details = manifest_data.get('uld_details', [])
        if isinstance(uld_details, list):
            uld_data_list.extend(uld_details)
        
        # Extract from AWB allocations
        awb_allocations = manifest_data.get('awb_allocations', [])
        for allocation in awb_allocations:
            uld_id = allocation.get('uld_id')
            if uld_id and not self._uld_exists_in_list(uld_id, uld_data_list):
                uld_data_list.append({
                    'uld_id': uld_id,
                    'manifest_id': manifest_data.get('manifest_id'),
                    'uld_type': self._determine_uld_type(uld_id),
                    'status': ULDStatus.PENDING.value,
                    'weight': allocation.get('weight', 0),
                    'pieces': allocation.get('pieces', 0)
                })
        
        return uld_data_list
    
    def _uld_exists_in_list(self, uld_id: str, uld_list: List[Dict[str, Any]]) -> bool:
        """Check if ULD already exists in the list."""
        return any(uld.get('uld_id') == uld_id for uld in uld_list)
    
    def _is_bulk_cargo(self, uld_data: Dict[str, Any]) -> bool:
        """Check if ULD represents bulk cargo that should be excluded."""
        uld_id = uld_data.get('uld_id', '')
        uld_type = uld_data.get('uld_type', '')
        
        # Check if ULD ID or type indicates bulk cargo
        for bulk_type in self.bulk_cargo_types:
            if bulk_type.upper() in uld_id.upper() or bulk_type.upper() in uld_type.upper():
                return True
        
        return False
    
    def _determine_uld_type(self, uld_id: str) -> str:
        """Determine ULD type from ULD ID."""
        if not uld_id or len(uld_id) < 3:
            return self.uld_config.get('default_uld_type', 'AKE')
        
        # Extract type code from ULD ID (first 3 characters)
        type_code = uld_id[:3].upper()
        
        # Validate against known ULD types
        valid_types = [uld_type.value for uld_type in ULDType]
        if type_code in valid_types:
            return type_code
        
        return self.uld_config.get('default_uld_type', 'AKE')
    
    def _process_single_uld(self, uld_data: Dict[str, Any], 
                          branch_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Process a single ULD record."""
        try:
            uld_id = uld_data.get('uld_id')
            manifest_id = uld_data.get('manifest_id')
            
            if not uld_id or not manifest_id:
                self.logger.warning("Missing ULD ID or manifest ID")
                return None
            
            # Check if ULD already exists for this manifest
            existing_uld = self._get_existing_uld(uld_id, manifest_id)
            
            if existing_uld:
                # Update existing ULD
                return self._update_uld(existing_uld['id'], uld_data, user_id)
            else:
                # Create new ULD
                return self._create_uld(uld_data, branch_id, user_id)
        
        except Exception as e:
            self.logger.error(f"Error processing ULD {uld_data.get('uld_id')}: {e}")
            return None
    
    def _get_existing_uld(self, uld_id: str, manifest_id: str) -> Optional[Dict[str, Any]]:
        """Get existing ULD record."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT id, uld_id, manifest_id, uld_type, uld_owner, 
                       weight, pieces, status, created_at, updated_at
                FROM uld_details 
                WHERE uld_id = %s AND manifest_id = %s AND deleted_at IS NULL
            """, (uld_id, manifest_id))
            
            result = cursor.fetchone()
            if result:
                return {
                    'id': result[0],
                    'uld_id': result[1],
                    'manifest_id': result[2],
                    'uld_type': result[3],
                    'uld_owner': result[4],
                    'weight': result[5],
                    'pieces': result[6],
                    'status': result[7],
                    'created_at': result[8],
                    'updated_at': result[9]
                }
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error getting existing ULD {uld_id}: {e}")
            return None
    
    def _create_uld(self, uld_data: Dict[str, Any], 
                   branch_id: int, user_id: int) -> Dict[str, Any]:
        """Create new ULD record."""
        try:
            cursor = self.db_connection.cursor()
            
            # Prepare ULD data
            uld_record = {
                'uld_id': uld_data.get('uld_id'),
                'manifest_id': uld_data.get('manifest_id'),
                'uld_type': uld_data.get('uld_type', self._determine_uld_type(uld_data.get('uld_id', ''))),
                'uld_owner': uld_data.get('uld_owner'),
                'loading_indicator': uld_data.get('loading_indicator'),
                'weight': float(uld_data.get('weight', 0)),
                'pieces': int(uld_data.get('pieces', 0)),
                'status': uld_data.get('status', ULDStatus.PENDING.value),
                'branch_id': branch_id,
                'created_by': user_id,
                'updated_by': user_id
            }
            
            # Insert ULD record
            cursor.execute("""
                INSERT INTO uld_details (
                    uld_id, manifest_id, uld_type, uld_owner, loading_indicator,
                    weight, pieces, status, branch_id, created_by, updated_by,
                    created_at, updated_at
                ) VALUES (
                    %(uld_id)s, %(manifest_id)s, %(uld_type)s, %(uld_owner)s, %(loading_indicator)s,
                    %(weight)s, %(pieces)s, %(status)s, %(branch_id)s, %(created_by)s, %(updated_by)s,
                    NOW(), NOW()
                ) RETURNING id
            """, uld_record)
            
            uld_id = cursor.fetchone()[0]
            self.db_connection.commit()
            
            self.logger.info(f"Created ULD record: {uld_record['uld_id']} (ID: {uld_id})")
            
            # Add ID to record and return
            uld_record['id'] = uld_id
            return uld_record
        
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error creating ULD record: {e}")
            raise
    
    def _update_uld(self, uld_db_id: int, uld_data: Dict[str, Any], 
                   user_id: int) -> Dict[str, Any]:
        """Update existing ULD record."""
        try:
            cursor = self.db_connection.cursor()
            
            # Update ULD record
            cursor.execute("""
                UPDATE uld_details SET
                    uld_type = %s,
                    uld_owner = %s,
                    loading_indicator = %s,
                    weight = %s,
                    pieces = %s,
                    status = %s,
                    updated_by = %s,
                    updated_at = NOW()
                WHERE id = %s
            """, (
                uld_data.get('uld_type'),
                uld_data.get('uld_owner'),
                uld_data.get('loading_indicator'),
                float(uld_data.get('weight', 0)),
                int(uld_data.get('pieces', 0)),
                uld_data.get('status', ULDStatus.PENDING.value),
                user_id,
                uld_db_id
            ))
            
            self.db_connection.commit()
            
            self.logger.info(f"Updated ULD record ID: {uld_db_id}")
            
            # Return updated data
            updated_data = uld_data.copy()
            updated_data['id'] = uld_db_id
            return updated_data
        
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error updating ULD record {uld_db_id}: {e}")
            raise
    
    def update_uld_status(self, uld_id: str, manifest_id: str, 
                         new_status: str, user_id: int) -> bool:
        """
        Update ULD status.
        
        Args:
            uld_id: ULD identifier
            manifest_id: Manifest identifier
            new_status: New status
            user_id: User ID making the change
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate status
            valid_statuses = [status.value for status in ULDStatus]
            if new_status not in valid_statuses:
                self.logger.error(f"Invalid ULD status: {new_status}")
                return False
            
            cursor = self.db_connection.cursor()
            cursor.execute("""
                UPDATE uld_details SET
                    status = %s,
                    updated_by = %s,
                    updated_at = NOW()
                WHERE uld_id = %s AND manifest_id = %s AND deleted_at IS NULL
            """, (new_status, user_id, uld_id, manifest_id))
            
            if cursor.rowcount > 0:
                self.db_connection.commit()
                self.logger.info(f"Updated ULD {uld_id} status to {new_status}")
                
                # Track movement if enabled
                if self.track_movements:
                    self._track_uld_movement(uld_id, manifest_id, new_status, user_id)
                
                return True
            else:
                self.logger.warning(f"No ULD found to update: {uld_id}")
                return False
        
        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error updating ULD status: {e}")
            return False
    
    def _track_uld_movement(self, uld_id: str, manifest_id: str, 
                          status: str, user_id: int) -> None:
        """Track ULD movement for audit purposes."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO uld_movements (
                    uld_id, manifest_id, status, movement_time, recorded_by, created_at
                ) VALUES (%s, %s, %s, NOW(), %s, NOW())
            """, (uld_id, manifest_id, status, user_id))
            
            self.db_connection.commit()
            self.logger.debug(f"Tracked ULD movement: {uld_id} -> {status}")
        
        except Exception as e:
            # Don't fail the main operation if movement tracking fails
            self.logger.warning(f"Failed to track ULD movement: {e}")
    
    def get_uld_inventory(self, manifest_id: Optional[str] = None, 
                         status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get ULD inventory.
        
        Args:
            manifest_id: Filter by manifest ID (optional)
            status: Filter by status (optional)
            
        Returns:
            List of ULD records
        """
        try:
            cursor = self.db_connection.cursor()
            
            query = """
                SELECT id, uld_id, manifest_id, uld_type, uld_owner, 
                       weight, pieces, status, created_at, updated_at
                FROM uld_details 
                WHERE deleted_at IS NULL
            """
            params = []
            
            if manifest_id:
                query += " AND manifest_id = %s"
                params.append(manifest_id)
            
            if status:
                query += " AND status = %s"
                params.append(status)
            
            query += " ORDER BY created_at DESC"
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            inventory = []
            for result in results:
                inventory.append({
                    'id': result[0],
                    'uld_id': result[1],
                    'manifest_id': result[2],
                    'uld_type': result[3],
                    'uld_owner': result[4],
                    'weight': result[5],
                    'pieces': result[6],
                    'status': result[7],
                    'created_at': result[8],
                    'updated_at': result[9]
                })
            
            return inventory
        
        except Exception as e:
            self.logger.error(f"Error getting ULD inventory: {e}")
            return []
