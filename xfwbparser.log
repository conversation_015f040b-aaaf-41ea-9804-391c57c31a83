2025-06-28 11:35:44,242 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Connected to the database
2025-06-28 11:35:44,242 - XF<PERSON>B<PERSON>arser - INFO - XFW<PERSON> Parser initialized with modular architecture
2025-06-28 11:35:44,243 - XF<PERSON><PERSON><PERSON>arser - INFO - Starting to parse XFWB file: examples/XFWB-706-51634332.xml
2025-06-28 11:35:44,243 - XFWB<PERSON>arser - INFO - Starting to parse XFWB XML string
2025-06-28 11:35:44,243 - XFWBParser - INFO - XML parsed successfully
2025-06-28 11:35:44,243 - XFWBParser - INFO - Extracting data from XML
2025-06-28 11:35:44,245 - XFWBParser - INFO - Extracted data for AWB: 706-51634332
2025-06-28 11:35:44,245 - XFWBParser - INFO - Validating extracted data
2025-06-28 11:35:44,245 - XFWBParser - INFO - Saving data to database
2025-06-28 11:35:44,249 - XF<PERSON><PERSON><PERSON>arser - INFO - Storing original XML content (9622 characters) for AWB 706-51634332
2025-06-28 11:35:44,252 - XFWBParser - INFO - Saved master waybill with ID 7
2025-06-28 11:35:44,253 - XFWBParser - INFO - Created new consignee: DUMMY CONSIGNEE (ID: 1)
2025-06-28 11:35:44,254 - XFWBParser - INFO - Updated AWB 7 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-28 11:35:44,256 - XFWBParser - INFO - Saved special handling code ELI with ID 1
2025-06-28 11:35:44,258 - XFWBParser - INFO - Saved special handling code GEN with ID 2
2025-06-28 11:35:44,260 - XFWBParser - INFO - Successfully saved XFWB data for AWB 706-51634332
2025-06-28 11:35:44,260 - XFWBParser - INFO - Successfully processed XFWB for AWB 706-51634332
