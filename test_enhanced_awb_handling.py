#!/usr/bin/env python3
"""
Test script for enhanced partial and split AWB handling.

This script tests the new logic for processing:
- Partial AWBs (codes 'P' and 'D') -> stored in partial_waybills table
- Split-complete AWBs (code 'S') -> aggregated and stored in master_waybills table
- Standard AWBs (code 'T' or no tag) -> stored in master_waybills table
"""

import sys
import os
import logging

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from extractors.xffm_extractor import XFFMExtractor

def setup_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def create_test_uld_data():
    """Create test ULD data with different split codes."""
    return [
        {
            "uld_id": "ULD001",
            "uld_type": "AKE",
            "awbs": [
                {
                    "awb_number": "12345678901",
                    "pieces": 5,
                    "weight": 100.0,
                    "transport_split_code": "P",  # Partial
                    "is_partial": True
                },
                {
                    "awb_number": "12345678902", 
                    "pieces": 10,
                    "weight": 200.0,
                    "transport_split_code": "S",  # Split-complete
                    "is_split_across_ulds": True
                },
                {
                    "awb_number": "12345678903",
                    "pieces": 3,
                    "weight": 50.0,
                    "transport_split_code": "T",  # Complete
                    "is_complete_shipment": True
                }
            ]
        },
        {
            "uld_id": "ULD002", 
            "uld_type": "AKE",
            "awbs": [
                {
                    "awb_number": "12345678902",  # Same AWB as ULD001 - should be aggregated
                    "pieces": 15,
                    "weight": 300.0,
                    "transport_split_code": "S",  # Split-complete
                    "is_split_across_ulds": True
                },
                {
                    "awb_number": "12345678904",
                    "pieces": 2,
                    "weight": 25.0,
                    "transport_split_code": "D",  # Partial (different code)
                    "is_partial": True
                }
            ]
        }
    ]

def create_test_standalone_waybills():
    """Create test standalone waybill data."""
    return [
        {
            "awb_number": "12345678905",
            "pieces": 8,
            "weight": 150.0,
            "transport_split_code": "P",  # Standalone partial
            "is_partial": True
        },
        {
            "awb_number": "12345678906",
            "pieces": 12,
            "weight": 250.0,
            "transport_split_code": "T",  # Standalone complete
            "is_complete_shipment": True
        }
    ]

def test_enhanced_aggregation():
    """Test the enhanced AWB aggregation logic."""
    logger = setup_logging()
    logger.info("Starting enhanced AWB handling test")
    
    # Create test data
    test_ulds = create_test_uld_data()
    test_standalone = create_test_standalone_waybills()
    
    # Initialize extractor
    extractor = XFFMExtractor(logger)
    
    # Test the aggregation
    logger.info("Testing aggregate_awb_data method...")
    result = extractor.aggregate_awb_data(test_ulds, test_standalone)
    
    # Verify results
    logger.info("=== AGGREGATION RESULTS ===")
    logger.info(f"Result type: {type(result)}")
    
    if isinstance(result, dict):
        complete_awbs = result.get("complete_awbs", [])
        partial_awbs = result.get("partial_awbs", [])
        
        logger.info(f"Complete AWBs: {len(complete_awbs)}")
        for awb in complete_awbs:
            logger.info(f"  - AWB {awb['awb_number']}: {awb['pieces']} pieces, {awb['weight']} kg, split: {awb.get('transport_split_code', 'None')}")
        
        logger.info(f"Partial AWBs: {len(partial_awbs)}")
        for awb in partial_awbs:
            logger.info(f"  - AWB {awb['awb_number']}: {awb['pieces']} pieces, {awb['weight']} kg, split: {awb.get('transport_split_code', 'None')}, ULD: {awb.get('uld_id', 'standalone')}")
        
        # Verify expected results
        logger.info("=== VERIFICATION ===")
        
        # Should have 3 complete AWBs: 12345678902 (aggregated), 12345678903, 12345678906
        expected_complete = 3
        if len(complete_awbs) == expected_complete:
            logger.info(f"✓ Complete AWBs count correct: {len(complete_awbs)}")
        else:
            logger.error(f"✗ Complete AWBs count incorrect: expected {expected_complete}, got {len(complete_awbs)}")
        
        # Should have 3 partial AWBs: 12345678901, 12345678904, 12345678905
        expected_partial = 3
        if len(partial_awbs) == expected_partial:
            logger.info(f"✓ Partial AWBs count correct: {len(partial_awbs)}")
        else:
            logger.error(f"✗ Partial AWBs count incorrect: expected {expected_partial}, got {len(partial_awbs)}")
        
        # Check aggregation of split AWB 12345678902
        split_awb = next((awb for awb in complete_awbs if awb['awb_number'] == '12345678902'), None)
        if split_awb:
            expected_pieces = 25  # 10 + 15
            expected_weight = 500.0  # 200.0 + 300.0
            if split_awb['pieces'] == expected_pieces and split_awb['weight'] == expected_weight:
                logger.info(f"✓ Split AWB aggregation correct: {split_awb['pieces']} pieces, {split_awb['weight']} kg")
            else:
                logger.error(f"✗ Split AWB aggregation incorrect: expected {expected_pieces} pieces, {expected_weight} kg, got {split_awb['pieces']} pieces, {split_awb['weight']} kg")
        else:
            logger.error("✗ Split AWB 12345678902 not found in complete AWBs")
        
    else:
        logger.error(f"✗ Expected dict result, got {type(result)}")
    
    logger.info("Enhanced AWB handling test completed")

if __name__ == "__main__":
    test_enhanced_aggregation()
