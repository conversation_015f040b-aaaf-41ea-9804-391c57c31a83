#!/usr/bin/env python3
"""
XFFM data extractor.

This module provides functionality for extracting data from XFFM XML files.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extractors.base_extractor import BaseExtractor


class XFFMExtractor(BaseExtractor):
    """
    Extractor for XFFM (Flight Manifest) XML data.

    This class extracts all relevant data from XFFM XML files including
    flight information, ULD details, and waybill allocations.
    """

    def extract_from_file(self, file_path):
        """
        Extract data from XFFM XML file.

        Args:
            file_path (str): Path to the XML file

        Returns:
            dict: Extracted data with success flag
        """
        try:
            from lxml import etree

            # Parse the XML file
            parser = etree.XMLParser(remove_blank_text=True)
            tree = etree.parse(file_path, parser)
            root = tree.getroot()

            # Extract data using the main extract method
            extracted_data = self.extract(root)

            # Add metadata
            result = {
                'success': True,
                'ulds': extracted_data.get('ulds', []),
                'waybills': extracted_data.get('waybills', []),
                'flight_info': {k: v for k, v in extracted_data.items()
                              if k not in ['ulds', 'waybills']},
                'message_id': extracted_data.get('manifest_id', 'UNKNOWN'),
                'manifest_id': extracted_data.get('manifest_id'),
                'xml_content': etree.tostring(root, encoding='unicode', pretty_print=True)
            }

            return result

        except Exception as e:
            self.logger.error(f"Error extracting data from XFFM file {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'ulds': [],
                'waybills': [],
                'flight_info': {},
                'message_id': None,
                'manifest_id': None
            }

    def extract(self, root_element):
        """
        Extract all data from XFFM XML.

        Args:
            root_element (Element): Root XML element.

        Returns:
            dict: Extracted flight manifest data.
        """
        data = {}

        # Extract basic flight manifest information
        data.update(self.extract_manifest_info(root_element))

        # Extract flight information
        data.update(self.extract_flight_info(root_element))

        # Extract ULD information
        data.update(self.extract_uld_info(root_element))

        # Extract waybill information
        data.update(self.extract_waybill_info(root_element))

        # Aggregate AWB data from ULDs and standalone waybills
        data["waybills"] = self.aggregate_awb_data(
            data.get("ulds", []), data.get("waybills", [])
        )

        return data

    def extract_manifest_info(self, root):
        """Extract basic manifest information."""
        data = {}

        # Manifest ID from MessageHeaderDocument/ID
        manifest_id = self.extract_text(
            root, ".//*[local-name()='MessageHeaderDocument']/*[local-name()='ID']"
        )

        if manifest_id:
            # Use the manifest ID as is, but prefix with FM- if it doesn't have a prefix
            if not manifest_id.startswith("FM-") and not manifest_id.startswith("MAN-"):
                manifest_id = f"FM-{manifest_id}"
            data["manifest_id"] = manifest_id

        return data

    def extract_flight_info(self, root):
        """Extract flight information."""
        data = {
            "flight_number": "",
            "flight_date": datetime.now().strftime("%Y-%m-%d"),
            "carrier_code": "",
            "departure_airport": "",
            "arrival_airport": "",
            "scheduled_departure": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "scheduled_arrival": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "actual_departure": None,
            "actual_arrival": None,
            "flight_type": "NORMAL",
            "status": "SCHEDULED",
            "total_pieces": 0,
            "total_weight": 0.0,
            "sync_status": "PENDING",
        }

        # Extract flight number from LogisticsTransportMovement/ID
        flight_number = self.extract_text(
            root, ".//*[local-name()='LogisticsTransportMovement']/*[local-name()='ID']"
        )
        if flight_number:
            data["flight_number"] = flight_number
            # If no manifest_id yet, generate one from flight number
            if not data.get("manifest_id") and flight_number:
                data["manifest_id"] = (
                    f"FM-{flight_number}-{datetime.now().strftime('%Y%m%d')}"
                )

        # Extract carrier code
        carrier_code = self.extract_text(
            root,
            ".//*[local-name()='LogisticsTransportMovement']/*[local-name()='UsedLogisticsTransportMeans']/*[local-name()='OperatingParty']/*[local-name()='PrimaryID']",
        )
        if carrier_code:
            data["carrier_code"] = carrier_code
        elif flight_number and len(flight_number) >= 2:
            # Extract from flight number (first 2 characters)
            data["carrier_code"] = flight_number[:2]

        # Extract departure airport
        departure_airport = self.extract_first_available(
            root,
            [
                ".//*[local-name()='DepartureEvent']/*[local-name()='OccurrenceDepartureLocation']/*[local-name()='ID']",
                ".//*[local-name()='DepartureEvent']/*[local-name()='ID']",
            ],
        )
        if departure_airport:
            data["departure_airport"] = departure_airport

        # Extract arrival airport
        arrival_airport = self.extract_first_available(
            root,
            [
                ".//*[local-name()='ArrivalEvent']/*[local-name()='OccurrenceArrivalLocation']/*[local-name()='ID']",
                ".//*[local-name()='ArrivalEvent']/*[local-name()='ID']",
            ],
        )
        if arrival_airport:
            data["arrival_airport"] = arrival_airport

        # Extract scheduled departure
        scheduled_departure = self.extract_first_available(
            root,
            [
                ".//*[local-name()='DepartureEvent']/*[local-name()='DepartureOccurrenceDateTime']",
                ".//*[local-name()='DepartureEvent']/*[local-name()='OccurrenceDateTime']",
            ],
        )
        if scheduled_departure:
            data["scheduled_departure"] = self.safe_parse_datetime(scheduled_departure)
            # Extract flight_date from scheduled_departure
            try:
                data["flight_date"] = scheduled_departure.split("T")[0]
            except (IndexError, AttributeError):
                pass

        # Extract scheduled arrival
        scheduled_arrival = self.extract_first_available(
            root,
            [
                ".//*[local-name()='ArrivalEvent']/*[local-name()='ArrivalOccurrenceDateTime']",
                ".//*[local-name()='ArrivalEvent']/*[local-name()='OccurrenceDateTime']",
            ],
        )
        if scheduled_arrival:
            data["scheduled_arrival"] = self.safe_parse_datetime(scheduled_arrival)

        # Extract actual departure
        actual_departure = self.extract_first_available(
            root,
            [
                ".//*[local-name()='DepartureEvent']/*[local-name()='ActualOccurrenceDateTime']",
                ".//*[local-name()='DepartureEvent']/*[local-name()='ActualDateTime']",
                ".//*[local-name()='DepartureEvent']/*[local-name()='ActualDepartureDateTime']",
            ],
        )
        if actual_departure:
            data["actual_departure"] = self.safe_parse_datetime(actual_departure)

        # Extract actual arrival
        actual_arrival = self.extract_first_available(
            root,
            [
                ".//*[local-name()='ArrivalEvent']/*[local-name()='ActualOccurrenceDateTime']",
                ".//*[local-name()='ArrivalEvent']/*[local-name()='ActualDateTime']",
                ".//*[local-name()='ArrivalEvent']/*[local-name()='ActualArrivalDateTime']",
            ],
        )
        if actual_arrival:
            data["actual_arrival"] = self.safe_parse_datetime(actual_arrival)

        # Extract total pieces
        total_pieces = self.extract_number(
            root,
            ".//*[local-name()='LogisticsTransportMovement']/*[local-name()='TotalPieceQuantity']",
            default=0,
        )
        data["total_pieces"] = total_pieces

        return data

    def extract_uld_info(self, root):
        """Extract ULD information."""
        data = {"ulds": []}

        # Look for ULDs in AssociatedTransportCargo elements (common XFFM structure)
        cargo_elements = root.xpath(".//*[local-name()='AssociatedTransportCargo']")

        if cargo_elements:
            for cargo_elem in cargo_elements:
                # Check if this cargo contains a ULD
                uld_elem = cargo_elem.xpath(
                    ".//*[local-name()='UtilizedUnitLoadTransportEquipment']"
                )
                if uld_elem:
                    uld_data = self.extract_single_uld_from_cargo(
                        cargo_elem, uld_elem[0]
                    )
                    if uld_data:
                        data["ulds"].append(uld_data)
                else:
                    # Check if this is bulk cargo (BLK, BULK, MAIL)
                    type_code = self.extract_text(cargo_elem, ".//*[local-name()='TypeCode']")
                    if type_code and type_code.upper() in ["BLK", "BULK", "MAIL"]:
                        # Create synthetic ULD for this bulk cargo element
                        bulk_uld = self.extract_bulk_cargo_from_element(cargo_elem, type_code)
                        if bulk_uld:
                            data["ulds"].append(bulk_uld)
        else:
            # Fallback: Try different XPaths for ULD elements
            uld_xpaths = [
                ".//*[local-name()='UtilizedUnitLoadTransportEquipment']",
                ".//*[local-name()='UnitLoadTransportEquipment']",
                ".//*[local-name()='IncludedLogisticsTransportEquipment']",
            ]

            uld_elements = []
            for xpath in uld_xpaths:
                uld_elements = root.xpath(xpath)
                if uld_elements:
                    break

            if uld_elements:
                for uld_elem in uld_elements:
                    uld_data = self.extract_single_uld(uld_elem)
                    if uld_data:
                        data["ulds"].append(uld_data)
            else:
                # Handle BLK (bulk) cargo - create synthetic ULD
                bulk_uld = self.extract_bulk_cargo(root)
                if bulk_uld:
                    data["ulds"].append(bulk_uld)

        return data

    def extract_single_uld_from_cargo(self, cargo_elem, uld_elem):
        """Extract data for a single ULD from AssociatedTransportCargo structure."""
        uld_data = {
            "uld_id": "",
            "uld_type": "",
            "uld_owner": "",
            "loading_indicator": "",
            "weight": 0.0,
            "pieces": 0,
            "awbs": [],
        }

        # Extract ULD ID
        uld_id = self.extract_text(uld_elem, ".//*[local-name()='ID']")
        if uld_id:
            uld_data["uld_id"] = uld_id

        # Extract ULD type from CharacteristicCode
        uld_type = self.extract_text(
            uld_elem, ".//*[local-name()='CharacteristicCode']"
        )
        if uld_type:
            uld_data["uld_type"] = uld_type

        # Extract ULD owner from OperatingParty
        uld_owner = self.extract_text(
            uld_elem, ".//*[local-name()='OperatingParty']/*[local-name()='PrimaryID']"
        )
        if uld_owner:
            uld_data["uld_owner"] = uld_owner

        # Extract loading indicator from LoadingRemark
        loading_indicator = self.extract_text(
            uld_elem, ".//*[local-name()='LoadingRemark']"
        )
        if loading_indicator:
            uld_data["loading_indicator"] = loading_indicator

        # Extract associated consignments from the cargo element
        consignment_elements = cargo_elem.xpath(
            ".//*[local-name()='IncludedMasterConsignment']"
        )
        if not consignment_elements:
            consignment_elements = cargo_elem.xpath(
                ".//*[local-name()='IncludedConsignment']"
            )

        for consignment_elem in consignment_elements:
            awb_data = self.extract_waybill_from_consignment(consignment_elem)
            if awb_data:
                uld_data["awbs"].append(awb_data)
                uld_data["pieces"] += awb_data.get("pieces", 0)
                uld_data["weight"] += awb_data.get("weight", 0.0)

        return uld_data if uld_data["uld_id"] else None

    def extract_single_uld(self, uld_elem):
        """Extract data for a single ULD."""
        uld_data = {
            "uld_id": "",
            "uld_type": "",
            "uld_owner": "",
            "loading_indicator": "",
            "weight": 0.0,
            "pieces": 0,
            "awbs": [],
        }

        # Extract ULD ID
        uld_id = self.extract_text(uld_elem, ".//*[local-name()='ID']")
        if uld_id:
            uld_data["uld_id"] = uld_id

        # Extract ULD type
        uld_type = self.extract_text(uld_elem, ".//*[local-name()='TypeCode']")
        if uld_type:
            uld_data["uld_type"] = uld_type

        # Extract ULD owner
        uld_owner = self.extract_text(
            uld_elem, ".//*[local-name()='OwnerParty']/*[local-name()='PrimaryID']"
        )
        if uld_owner:
            uld_data["uld_owner"] = uld_owner

        # Extract loading indicator
        loading_indicator = self.extract_text(
            uld_elem, ".//*[local-name()='LoadingIndicator']"
        )
        if loading_indicator:
            uld_data["loading_indicator"] = loading_indicator

        # Extract associated cargo/waybills
        cargo_elements = uld_elem.xpath(".//*[local-name()='AssociatedTransportCargo']")
        for cargo_elem in cargo_elements:
            consignment_elements = cargo_elem.xpath(
                ".//*[local-name()='IncludedMasterConsignment']"
            )
            if not consignment_elements:
                consignment_elements = cargo_elem.xpath(
                    ".//*[local-name()='IncludedConsignment']"
                )

            for consignment_elem in consignment_elements:
                awb_data = self.extract_waybill_from_consignment(consignment_elem)
                if awb_data:
                    uld_data["awbs"].append(awb_data)
                    uld_data["pieces"] += awb_data.get("pieces", 0)
                    uld_data["weight"] += awb_data.get("weight", 0.0)

        return uld_data if uld_data["uld_id"] else None

    def extract_bulk_cargo(self, root):
        """Extract bulk cargo and create synthetic ULD."""
        # Look for cargo elements that might contain BLK shipments
        cargo_elements = root.xpath(".//*[local-name()='AssociatedTransportCargo']")

        if not cargo_elements:
            return None

        # Check for BLK cargo type
        for cargo_elem in cargo_elements:
            type_code = self.extract_text(cargo_elem, ".//*[local-name()='TypeCode']")
            if type_code and type_code.upper() in ["BLK", "BULK", "MAIL"]:
                # Create synthetic ULD for bulk cargo
                bulk_type = type_code.upper()
                bulk_id = f"{bulk_type}-{uuid.uuid4().hex[:6]}"

                uld_data = {
                    "uld_id": bulk_id,
                    "uld_type": bulk_type,
                    "uld_owner": "BLK",
                    "loading_indicator": "",
                    "weight": 0.0,
                    "pieces": 0,
                    "awbs": [],
                }

                # Extract waybills from this bulk cargo
                consignment_elements = cargo_elem.xpath(
                    ".//*[local-name()='IncludedMasterConsignment']"
                )
                if not consignment_elements:
                    consignment_elements = cargo_elem.xpath(
                        ".//*[local-name()='IncludedConsignment']"
                    )

                for consignment_elem in consignment_elements:
                    awb_data = self.extract_waybill_from_consignment(consignment_elem)
                    if awb_data:
                        uld_data["awbs"].append(awb_data)
                        uld_data["pieces"] += awb_data.get("pieces", 0)
                        uld_data["weight"] += awb_data.get("weight", 0.0)

                return uld_data if uld_data["awbs"] else None

        return None

    def extract_bulk_cargo_from_element(self, cargo_elem, type_code):
        """Extract bulk cargo from a specific cargo element and create synthetic ULD."""
        import uuid

        # Create synthetic ULD for bulk cargo
        bulk_type = type_code.upper()
        bulk_id = f"{bulk_type}-{uuid.uuid4().hex[:6]}"

        uld_data = {
            "uld_id": bulk_id,
            "uld_type": bulk_type,
            "uld_owner": "BLK",
            "loading_indicator": "",
            "weight": 0.0,
            "pieces": 0,
            "awbs": [],
        }

        # Extract waybills from this bulk cargo element
        consignment_elements = cargo_elem.xpath(
            ".//*[local-name()='IncludedMasterConsignment']"
        )
        if not consignment_elements:
            consignment_elements = cargo_elem.xpath(
                ".//*[local-name()='IncludedConsignment']"
            )

        for consignment_elem in consignment_elements:
            awb_data = self.extract_waybill_from_consignment(consignment_elem)
            if awb_data:
                uld_data["awbs"].append(awb_data)
                uld_data["pieces"] += awb_data.get("pieces", 0)
                uld_data["weight"] += awb_data.get("weight", 0.0)

        return uld_data if uld_data["awbs"] else None

    def extract_waybill_info(self, root):
        """Extract waybill information."""
        data = {"waybills": []}

        # Extract waybills from IncludedMasterConsignment
        waybill_elements = root.xpath(".//*[local-name()='IncludedMasterConsignment']")
        if not waybill_elements:
            waybill_elements = root.xpath(".//*[local-name()='IncludedConsignment']")

        for waybill_elem in waybill_elements:
            awb_data = self.extract_waybill_from_consignment(waybill_elem)
            if awb_data:
                data["waybills"].append(awb_data)

        return data

    def extract_waybill_from_consignment(self, consignment_elem):
        """Extract waybill data from a consignment element."""
        awb_data = {
            "awb_number": "",
            "pieces": 0,
            "weight": 0.0,
            "weight_unit": "KGM",
            "origin_airport": "",
            "destination_airport": "",
            "special_handling_code": "",
            "type_code": "AWB",
            "is_mail": False,
            "is_partial": False,
            "is_human_remains": False,
            "has_houses": False,
            "status": "PENDING",
            "gross_volume": None,
            "volume_unit": None,
            "summary_description": None,
        }

        # Extract AWB number - try TransportContractDocument/ID first (XFFM structure)
        awb_number = self.extract_text(
            consignment_elem,
            ".//*[local-name()='TransportContractDocument']/*[local-name()='ID']",
        )
        if not awb_number:
            # Fallback to direct ID element
            awb_number = self.extract_text(consignment_elem, ".//*[local-name()='ID']")

        if awb_number:
            awb_data["awb_number"] = awb_number

        # Extract pieces
        pieces = self.extract_number(
            consignment_elem, ".//*[local-name()='TotalPieceQuantity']", default=0
        )
        awb_data["pieces"] = pieces

        # Extract weight
        weight = self.extract_number(
            consignment_elem,
            ".//*[local-name()='GrossWeightMeasure']",
            default=0.0,
            number_type=float,
        )
        awb_data["weight"] = weight

        # Extract weight unit
        weight_elements = consignment_elem.xpath(
            ".//*[local-name()='GrossWeightMeasure']"
        )
        if weight_elements:
            weight_unit = self.extract_attribute(
                consignment_elem,
                ".//*[local-name()='GrossWeightMeasure']",
                "unitCode",
                "KGM",
            )
            awb_data["weight_unit"] = weight_unit

        # Extract volume
        volume = self.extract_number(
            consignment_elem,
            ".//*[local-name()='GrossVolumeMeasure']",
            default=None,
            number_type=float,
        )
        if volume is not None:
            awb_data["gross_volume"] = volume
            volume_unit = self.extract_attribute(
                consignment_elem,
                ".//*[local-name()='GrossVolumeMeasure']",
                "unitCode",
                "MC",
            )
            awb_data["volume_unit"] = volume_unit

        # Extract summary description
        summary_description = self.extract_text(
            consignment_elem, ".//*[local-name()='SummaryDescription']"
        )
        if summary_description:
            awb_data["summary_description"] = summary_description

        # Extract detailed description (following old parser pattern)
        description = self.extract_text(
            consignment_elem, ".//*[local-name()='GoodsDescription']"
        )
        if not description:
            description = self.extract_text(
                consignment_elem, ".//*[local-name()='Description']"
            )
        if description:
            awb_data["description"] = description

            # If no summary description found, use first part of detailed description
            if not summary_description and description:
                awb_data["summary_description"] = description[:100] + (
                    "..." if len(description) > 100 else ""
                )

        # Extract origin and destination airports
        origin_airport = self.extract_text(
            consignment_elem, ".//*[local-name()='OriginLocation']/*[local-name()='ID']"
        )
        if origin_airport:
            awb_data["origin_airport"] = origin_airport

        destination_airport = self.extract_text(
            consignment_elem,
            ".//*[local-name()='FinalDestinationLocation']/*[local-name()='ID']",
        )
        if destination_airport:
            awb_data["destination_airport"] = destination_airport

        # Extract special handling codes
        handling_codes = []
        handling_elements = consignment_elem.xpath(
            ".//*[local-name()='HandlingSPHInstructions']/*[local-name()='DescriptionCode']"
        )
        for elem in handling_elements:
            if elem.text:
                handling_codes.append(elem.text.strip())

        if handling_codes:
            # Store only the first code in special_handling_code field (varchar(3) limit)
            # The full list is stored in special_handling_codes for detailed processing
            awb_data["special_handling_code"] = handling_codes[0][:3]  # Limit to 3 chars
            awb_data["special_handling_codes"] = handling_codes  # Store full list

        # Extract TransportSplitDescription code (IATA Cargo-XML standard)
        split_code = self.extract_text(
            consignment_elem, ".//*[local-name()='TransportSplitDescription']"
        )
        if split_code:
            awb_data["transport_split_code"] = split_code.strip().upper()

            # Handle split codes according to IATA standards
            if split_code.strip().upper() == "P":
                awb_data["is_partial"] = True
                self.logger.info(
                    f"AWB {awb_data.get('awb_number', 'unknown')} marked as partial shipment (code: P)"
                )
            elif split_code.strip().upper() == "S":
                awb_data["is_split_across_ulds"] = True
                self.logger.info(
                    f"AWB {awb_data.get('awb_number', 'unknown')} split across ULDs (code: S)"
                )
            elif split_code.strip().upper() == "T":
                awb_data["is_complete_shipment"] = True
                self.logger.info(
                    f"AWB {awb_data.get('awb_number', 'unknown')} is complete shipment (code: T)"
                )
        else:
            # Default values when no split code is present
            awb_data["transport_split_code"] = None
            awb_data["is_split_across_ulds"] = False
            awb_data["is_complete_shipment"] = False

        # Determine cargo type flags (following old parser pattern)
        type_code = self.extract_text(consignment_elem, ".//*[local-name()='TypeCode']")
        if type_code and type_code.upper() == "MAIL":
            awb_data["is_mail"] = True
            awb_data["type_code"] = "MAL"  # Use 3-character code for mail
        elif "AO" in awb_data.get(
            "special_handling_code", ""
        ) or "MAIL" in awb_data.get("special_handling_code", ""):
            awb_data["is_mail"] = True
            awb_data["type_code"] = "MAL"  # Use 3-character code for mail

        # Check for human remains
        if any(
            code in awb_data.get("special_handling_code", "")
            for code in ["HRM", "HUM", "SCI"]
        ):
            awb_data["is_human_remains"] = True

        return awb_data if awb_data["awb_number"] else None

    def aggregate_awb_data(self, ulds, standalone_waybills):
        """
        Enhanced AWB aggregation with proper partial and split AWB handling.

        - Partial AWBs ('P', 'D'): Kept separate, not aggregated
        - Split-complete AWBs ('S'): Aggregated across ULDs within same manifest
        - Standard AWBs ('T' or no tag): Processed normally

        Args:
            ulds (list): List of ULD data containing AWBs
            standalone_waybills (list): List of standalone waybill data

        Returns:
            dict: Contains 'complete_awbs' and 'partial_awbs' lists
        """
        awb_aggregates = {}
        partial_awbs = []  # Store partial AWBs separately
        awb_split_info = {}  # Track split information for logging

        # Collect AWB data from ULDs
        for uld in ulds:
            uld_id = uld.get("uld_id", "unknown")
            for awb in uld.get("awbs", []):
                awb_number = awb.get("awb_number")
                if not awb_number:
                    continue

                split_code = awb.get("transport_split_code", "").strip().upper()

                # Track split information for audit logging
                if awb_number not in awb_split_info:
                    awb_split_info[awb_number] = {
                        "split_codes": [],
                        "ulds": [],
                        "split_type": split_code,
                    }

                awb_split_info[awb_number]["split_codes"].append(split_code or "None")
                awb_split_info[awb_number]["ulds"].append(uld_id)

                # Enhanced logic for different split codes
                if split_code in ["P", "D"]:
                    # Partial shipment - store separately, do NOT aggregate
                    awb_copy = awb.copy()
                    awb_copy["uld_id"] = uld_id  # Track which ULD this partial came from
                    partial_awbs.append(awb_copy)

                    self.logger.info(
                        f"Found partial AWB {awb_number} in ULD {uld_id} (split code: {split_code}) - storing separately"
                    )
                    continue

                # For split-complete ('S') and standard ('T' or no tag) AWBs
                if awb_number not in awb_aggregates:
                    # First occurrence - copy all data
                    awb_aggregates[awb_number] = awb.copy()
                    self.logger.debug(
                        f"First occurrence of AWB {awb_number} in ULD {uld_id} with split code: {split_code}"
                    )
                else:
                    # Subsequent occurrence - handle based on split code
                    if split_code == "S":
                        # Split-complete across ULDs - aggregate pieces and weights
                        awb_aggregates[awb_number]["pieces"] += awb.get("pieces", 0)
                        awb_aggregates[awb_number]["weight"] += awb.get("weight", 0.0)

                        # Update volume if this AWB has volume data
                        if awb.get("gross_volume") is not None:
                            current_volume = (
                                awb_aggregates[awb_number].get("gross_volume", 0.0)
                                or 0.0
                            )
                            awb_aggregates[awb_number]["gross_volume"] = (
                                current_volume + awb.get("gross_volume", 0.0)
                            )

                        self.logger.info(
                            f"Aggregating split-complete AWB {awb_number} from ULD {uld_id} (split code: S) - Total pieces: {awb_aggregates[awb_number]['pieces']}, Total weight: {awb_aggregates[awb_number]['weight']}"
                        )
                    else:
                        # No split code or "T" - this might be a data issue, but aggregate anyway
                        awb_aggregates[awb_number]["pieces"] += awb.get("pieces", 0)
                        awb_aggregates[awb_number]["weight"] += awb.get("weight", 0.0)

                        self.logger.warning(
                            f"AWB {awb_number} appears multiple times without proper split code (found: {split_code}) - aggregating anyway"
                        )

        # Add standalone waybills (handle partial vs complete separately)
        for awb in standalone_waybills:
            awb_number = awb.get("awb_number")
            if not awb_number:
                continue

            split_code = awb.get("transport_split_code", "").strip().upper()

            if split_code in ["P", "D"]:
                # Standalone partial AWB
                awb_copy = awb.copy()
                awb_copy["uld_id"] = None  # No ULD for standalone
                partial_awbs.append(awb_copy)
                self.logger.info(f"Found standalone partial AWB {awb_number} (split code: {split_code})")
                continue

            # Handle complete AWBs (S, T, or no tag)
            if awb_number not in awb_aggregates:
                # New AWB not in ULDs
                awb_aggregates[awb_number] = awb.copy()
            else:
                # AWB already exists from ULDs - add to totals
                self.logger.warning(
                    f"AWB {awb_number} found in both ULDs and standalone waybills - aggregating totals"
                )
                awb_aggregates[awb_number]["pieces"] += awb.get("pieces", 0)
                awb_aggregates[awb_number]["weight"] += awb.get("weight", 0.0)

                if awb.get("gross_volume") is not None:
                    current_volume = (
                        awb_aggregates[awb_number].get("gross_volume", 0.0) or 0.0
                    )
                    awb_aggregates[awb_number]["gross_volume"] = (
                        current_volume + awb.get("gross_volume", 0.0)
                    )

        # Convert aggregated AWBs to list
        complete_awbs = list(awb_aggregates.values())

        self.logger.info(
            f"Processed {len(complete_awbs)} complete AWBs and {len(partial_awbs)} partial AWBs"
        )

        # Audit logging for complete AWBs (split codes)
        for awb_number, split_info in awb_split_info.items():
            awb_data = awb_aggregates.get(awb_number)
            if awb_data:
                split_codes = set(split_info["split_codes"])
                ulds = split_info["ulds"]

                if len(ulds) > 1:
                    # AWB appears in multiple ULDs
                    if "S" in split_codes:
                        self.logger.info(
                            f"Complete AWB {awb_number}: Split across ULDs {ulds} (code: S) - Final totals: {awb_data['pieces']} pieces, {awb_data['weight']} {awb_data.get('weight_unit', 'KGM')}"
                        )
                    else:
                        self.logger.warning(
                            f"Complete AWB {awb_number}: Appears in multiple ULDs {ulds} without proper split codes {split_codes} - Final totals: {awb_data['pieces']} pieces, {awb_data['weight']} {awb_data.get('weight_unit', 'KGM')}"
                        )
                else:
                    # Single ULD
                    split_code = list(split_codes)[0] if split_codes else "None"
                    if split_code == "T":
                        self.logger.debug(
                            f"Complete AWB {awb_number}: Complete shipment in ULD {ulds[0]} (code: T)"
                        )
                    elif split_code == "S":
                        self.logger.debug(
                            f"Complete AWB {awb_number}: Split-complete in single ULD {ulds[0]} (code: S)"
                        )
                    else:
                        self.logger.debug(
                            f"Complete AWB {awb_number}: Single ULD {ulds[0]} with split code: {split_code}"
                        )

        # Audit logging for partial AWBs
        for partial_awb in partial_awbs:
            awb_number = partial_awb.get("awb_number")
            split_code = partial_awb.get("transport_split_code")
            uld_id = partial_awb.get("uld_id")
            self.logger.info(
                f"Partial AWB {awb_number}: Split code {split_code} in ULD {uld_id or 'standalone'} - {partial_awb.get('pieces', 0)} pieces, {partial_awb.get('weight', 0.0)} {partial_awb.get('weight_unit', 'KGM')}"
            )

        return {
            "complete_awbs": complete_awbs,
            "partial_awbs": partial_awbs
        }

    def safe_parse_datetime(self, text):
        """Safely parse a datetime string with error handling."""
        try:
            if text:
                from dateutil.parser import parse as parse_datetime

                dt = parse_datetime(text.strip(), fuzzy=True)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception:
            self.logger.warning(f"Could not parse datetime: {text}")
        return None
