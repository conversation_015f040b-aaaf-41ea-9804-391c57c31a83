<?php

namespace App\Domains\Checkin\Controllers;

use App\Http\Controllers\Controller;
use App\Domains\Checkin\Services\CheckinService;
use App\Domains\Checkin\Services\ReconciliationService;
use App\Domains\Checkin\Requests\ProcessCheckinRequest;
use App\Domains\Checkin\Requests\StoreExcessDocumentRequest;
use App\Domains\Checkin\Requests\ReverseCheckinRequest;
use App\Domains\Checkin\Requests\ReconcileAwbRequest;
use App\Domains\Checkin\Exceptions\CheckinException;
use App\Domains\Checkin\Exceptions\ReconciliationException;
use App\Models\Branch;
use App\Models\MasterWaybill;
use App\Models\CheckinRecord;
use App\Models\FlightManifest as FlightManifestModel;
use App\Models\ExcessDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CheckinController extends Controller
{
    /**
     * The CheckinService instance.
     */
    protected $checkinService;

    /**
     * The ReconciliationService instance.
     */
    protected $reconciliationService;

    /**
     * Create a new controller instance.
     */
    public function __construct(CheckinService $checkinService, ReconciliationService $reconciliationService)
    {
        $this->checkinService = $checkinService;
        $this->reconciliationService = $reconciliationService;
    }

    /**
     * Display a listing of the flight manifests available for check-in.
     */
    public function index()
    {
        $flights = $this->checkinService->getManifestsForCheckin();
        $pendingFlights = $this->checkinService->getPendingFlights();
        return view('checkin.index', compact('flights', 'pendingFlights'));
    }

    /**
     * Display the ULD list for a flight manifest.
     */
    public function uldList($id)
    {
        $manifest = $this->checkinService->getManifestById($id);

        if (!$manifest) {
            return redirect()->route('checkin.index')
                ->with('error', 'Flight manifest not found or not in ARRIVED status.');
        }

        $ulds = $this->checkinService->getManifestUlds($id);

        return view('checkin.uld-list', compact('manifest', 'ulds'));
    }

    /**
     * Display the AWB list for a ULD or BULK cargo.
     */
    public function awbList($id, $uldType, $uldNumber = null)
    {
        $manifest = $this->checkinService->getManifestById($id);

        if (!$manifest) {
            return redirect()->route('checkin.index')
                ->with('error', 'Flight manifest not found or not in ARRIVED status.');
        }

        // Clear cache to ensure fresh data
        $this->checkinService->clearCache($id);

        // Get ULD-specific AWBs or BULK AWBs
        if ($uldType === 'BULK') {
            $awbs = $this->checkinService->getBulkAwbs($id);
            $uldId = null;
        } else {
            $uldId = $uldNumber;

            // Fix ULD ID format if it has duplicate type prefix (e.g., AKEAKE34463)
            if ($uldId && strlen($uldId) > 7 && substr($uldId, 0, 3) === substr($uldId, 3, 3)) {
                $fixedUldId = substr($uldId, 3);
                Log::info("Fixed ULD ID format from {$uldId} to {$fixedUldId}");
                $uldId = $fixedUldId;
            }

            // Get AWBs for this specific ULD
            $awbs = $this->checkinService->getUldAwbs($uldId, $id);

            // If no AWBs found, log it but don't fall back to all AWBs
            if (count($awbs) === 0) {
                Log::warning("No AWBs found for ULD {$uldId} in manifest {$id}");
            }
        }

        // Ensure all AWBs have the required properties
        foreach ($awbs as $awb) {
            // Ensure waybill_id is set
            if (!isset($awb->waybill_id)) {
                $awb->waybill_id = $awb->awb_number;
            }

            // Ensure origin_location and destination_location are set
            if (!isset($awb->origin_location)) {
                $awb->origin_location = $awb->origin_airport ?? 'N/A';
            }
            if (!isset($awb->destination_location)) {
                $awb->destination_location = $awb->destination_airport ?? 'N/A';
            }

            // Get checkin status for each AWB
            $awb->is_checked_in = $this->checkinService->isAwbCheckedIn($awb->awb_number, $id);
            $awb->checkin_record = $this->checkinService->getCheckinRecord($awb->awb_number, $id);

            // Generate status badge if not set
            if (!isset($awb->status_badge)) {
                $statusClass = 'secondary';
                if ($awb->status == 'RECEIVED') $statusClass = 'success';
                elseif ($awb->status == 'PENDING') $statusClass = 'warning';
                elseif ($awb->status == 'MISSING') $statusClass = 'danger';

                $awb->status_badge = '<span class="badge bg-' . $statusClass . '">' . $awb->status . '</span>';
            }

            // Check for partial AWBs
            if (isset($awb->is_partial) && $awb->is_partial) {
                // Get partial waybill details if not already set
                if (!isset($awb->partial_id)) {
                    $partialWaybill = DB::table('partial_waybills')
                        ->where('master_awb_number', $awb->awb_number)
                        ->where('manifest_id', $id)
                        ->first();

                    if ($partialWaybill) {
                        $awb->partial_id = $partialWaybill->partial_id;
                        $awb->split_code = $partialWaybill->split_code;
                        $awb->split_sequence = $partialWaybill->split_sequence;
                        $awb->remaining_pieces = $partialWaybill->remaining_pieces;
                        $awb->remaining_weight = $partialWaybill->remaining_weight;
                        $awb->received_pieces = $partialWaybill->received_pieces;
                        $awb->received_weight = $partialWaybill->received_weight;

                        Log::info("Found partial waybill {$partialWaybill->partial_id} for AWB {$awb->awb_number}");
                    }
                }
            }

            // Get HAWBs for MAWBs and Direct AWBs
            if ($awb->type_code == '740' || $awb->type_code == '741') { // MAWB or Direct AWB
                $awb->hawbs = $this->checkinService->getHawbsForMawb($awb->awb_number);

                // Get checkin status for each HAWB and ensure required properties
                foreach ($awb->hawbs as $hawb) {
                    // Ensure waybill_id is set
                    if (!isset($hawb->waybill_id)) {
                        $hawb->waybill_id = $hawb->awb_number;
                    }

                    $hawb->is_checked_in = $this->checkinService->isAwbCheckedIn($hawb->awb_number, $id);
                    $hawb->checkin_record = $this->checkinService->getCheckinRecord($hawb->awb_number, $id);

                    // Check for partial HAWBs
                    if (isset($hawb->is_partial) && $hawb->is_partial) {
                        // Get partial waybill details if not already set
                        if (!isset($hawb->partial_id)) {
                            $partialWaybill = DB::table('partial_waybills')
                                ->where('house_awb_number', $hawb->awb_number)
                                ->where('manifest_id', $id)
                                ->first();

                            if ($partialWaybill) {
                                $hawb->partial_id = $partialWaybill->partial_id;
                                $hawb->split_code = $partialWaybill->split_code;
                                $hawb->split_sequence = $partialWaybill->split_sequence;
                                $hawb->remaining_pieces = $partialWaybill->remaining_pieces;
                                $hawb->remaining_weight = $partialWaybill->remaining_weight;
                                $hawb->received_pieces = $partialWaybill->received_pieces;
                                $hawb->received_weight = $partialWaybill->received_weight;

                                Log::info("Found partial waybill {$partialWaybill->partial_id} for HAWB {$hawb->awb_number}");
                            }
                        }
                    }
                }
            }
        }

        $branches = Branch::where('is_active', true)->get();

        // Add cargo states for the view
        $cargoStates = [
            'GOOD' => 'Good Condition',
            'DAMAGED' => 'Damaged',
            'MISSING' => 'Missing',
            'PARTIAL' => 'Partial Shipment',
            'DISC' => 'Discrepancy (damaged, torn, etc.)',
            'INTERLINE' => 'Transfer cargo',
            'NTH' => 'Not at Hand',
            'MSAW' => 'Missing AWB document, but cargo received',
            'MSCA' => 'Missing cargo, missing AWB'
        ];

        // Get ULD status from database if available
        $uldStatus = null;
        $hasPartialAwbs = false;
        if (!empty($uldId)) {
            $uldDetails = \Illuminate\Support\Facades\DB::table('uld_details')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $id)
                ->first();

            if ($uldDetails && isset($uldDetails->status)) {
                $uldStatus = $uldDetails->status;
            }

            // Check if ULD has partial AWBs
            $hasPartialAwbs = DB::table('partial_waybills')
                ->where('manifest_id', $id)
                ->where('uld_id', $uldId)
                ->exists();

            // If ULD has partial AWBs and status is not COMPLETE, set status to PARTIAL
            if ($hasPartialAwbs && (!$uldStatus || $uldStatus != 'COMPLETE')) {
                $uldStatus = 'PARTIAL';

                // Update the database if needed
                if (!$uldDetails || $uldDetails->status != 'PARTIAL') {
                    DB::table('uld_details')
                        ->where('uld_id', $uldId)
                        ->where('manifest_id', $id)
                        ->update(['status' => 'PARTIAL']);

                    Log::info("Updated ULD {$uldId} status to PARTIAL due to partial AWBs");
                }
            }
        }

        // Get ULD-specific AWBs using the unified view
        if ($uldType === 'BULK') {
            // For BULK, get all AWBs not assigned to any ULD
            $unifiedWaybills = \App\Models\UnifiedWaybill::forManifest($id)
                ->whereNotIn('waybill_id', function($query) use ($id) {
                    $query->select('awb_number')
                        ->from('uld_awb_allocations')
                        ->where('manifest_id', $id);
                })
                ->whereNotIn('waybill_id', function($query) use ($id) {
                    $query->select('awb_number')
                        ->from('uld_awbs')
                        ->where('manifest_id', $id);
                })
                ->get();

            Log::info("Using unified view: Found " . count($unifiedWaybills) . " BULK AWBs for manifest {$id}");
        } else {
            // Use manifest_id-based approach for better consistency
            // Get all waybills for this manifest first
            $allManifestWaybills = \App\Models\UnifiedWaybill::forManifest($id)->get();

            // Get AWB numbers allocated to this ULD from both allocation tables
            $uldAwbNumbers = [];

            $allocations = DB::table('uld_awb_allocations')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $id)
                ->pluck('awb_number')
                ->toArray();

            $uldAwbNumbers = array_merge($uldAwbNumbers, $allocations);

            // Also check legacy uld_awbs table
            $legacyAllocations = DB::table('uld_awbs')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $id)
                ->pluck('awb_number')
                ->toArray();

            $uldAwbNumbers = array_merge($uldAwbNumbers, $legacyAllocations);
            $uldAwbNumbers = array_unique($uldAwbNumbers);

            // Log the AWB numbers found for this ULD
            Log::info("Found " . count($uldAwbNumbers) . " AWB numbers for ULD {$uldId} in manifest {$id}: " . implode(', ', $uldAwbNumbers));

            // Filter waybills that belong to this ULD using manifest_id approach
            $unifiedWaybills = $allManifestWaybills->filter(function($waybill) use ($uldAwbNumbers) {
                // Include if waybill_id matches (for master AWBs)
                if (in_array($waybill->waybill_id, $uldAwbNumbers)) {
                    return true;
                }

                // Include if it's a partial AWB whose master AWB is in this ULD
                if ($waybill->waybill_type === 'PARTIAL' && in_array($waybill->awb_number, $uldAwbNumbers)) {
                    return true;
                }

                return false;
            });

            // Process filtered waybills to add required properties for template
            foreach ($unifiedWaybills as $awb) {
                // Ensure waybill_id is set
                if (!isset($awb->waybill_id)) {
                    $awb->waybill_id = $awb->awb_number;
                }

                // Ensure origin_location and destination_location are set
                if (!isset($awb->origin_location)) {
                    $awb->origin_location = $awb->origin_airport ?? 'N/A';
                }
                if (!isset($awb->destination_location)) {
                    $awb->destination_location = $awb->destination_airport ?? 'N/A';
                }

                // Get checkin status for each AWB (use waybill_id for partial AWBs)
                $checkAwbId = ($awb->waybill_type === 'PARTIAL') ? $awb->waybill_id : $awb->awb_number;
                $awb->is_checked_in = $this->checkinService->isAwbCheckedIn($checkAwbId, $id);
                $awb->checkin_record = $this->checkinService->getCheckinRecord($checkAwbId, $id);

                // Generate status badge if not set
                if (!isset($awb->status_badge)) {
                    $statusClass = 'secondary';
                    if ($awb->status == 'RECEIVED') $statusClass = 'success';
                    elseif ($awb->status == 'PENDING') $statusClass = 'warning';
                    elseif ($awb->status == 'MISSING') $statusClass = 'danger';

                    $awb->status_badge = '<span class="badge bg-' . $statusClass . '">' . $awb->status . '</span>';
                }
            }

            // Also get house waybills for master waybills in this ULD
            $masterAwbNumbers = $unifiedWaybills
                ->where('waybill_type', 'MASTER')
                ->pluck('awb_number')
                ->toArray();

            if (!empty($masterAwbNumbers)) {
                Log::info("Found " . count($masterAwbNumbers) . " master AWB numbers for ULD {$uldId}: " . implode(', ', $masterAwbNumbers));

                // Get all house waybills for these master AWBs from the unified view
                $houseWaybillsForMasters = \App\Models\UnifiedWaybill::forManifest($id)
                    ->where('waybill_type', 'HOUSE')
                    ->whereIn('master_awb_number', $masterAwbNumbers)
                    ->get();

                if ($houseWaybillsForMasters->count() > 0) {
                    Log::info("Found " . $houseWaybillsForMasters->count() . " house waybills for master AWBs in ULD {$uldId}");

                    // Get existing waybill IDs to avoid duplicates
                    $existingWaybillIds = $unifiedWaybills->pluck('waybill_id')->toArray();

                    // Filter out house waybills that are already in the collection
                    $uniqueHouseWaybills = $houseWaybillsForMasters->filter(function($hawb) use ($existingWaybillIds) {
                        return !in_array($hawb->waybill_id, $existingWaybillIds);
                    });

                    Log::info("Adding " . $uniqueHouseWaybills->count() . " unique house waybills to the collection");

                    // Add these house waybills to the unified waybills collection
                    $unifiedWaybills = $unifiedWaybills->concat($uniqueHouseWaybills);

                    // Also add these house waybills to the ULD-AWB allocations if they're not already there
                    foreach ($houseWaybillsForMasters as $hawb) {
                        // Check if this HAWB is already assigned to this ULD
                        $uldAssignment = DB::table('uld_awbs')
                            ->where('awb_number', $hawb->hawb_number)
                            ->where('manifest_id', $id)
                            ->where('uld_id', $uldId)
                            ->first();

                        if (!$uldAssignment) {
                            // Get the master waybill to determine pieces and weight
                            $masterWaybill = $unifiedWaybills
                                ->where('waybill_type', 'MASTER')
                                ->where('awb_number', $hawb->master_awb_number)
                                ->first();

                            if ($masterWaybill) {
                                // Calculate proportional pieces and weight based on the master waybill
                                $piecesRatio = $hawb->expected_pieces / $masterWaybill->expected_pieces;
                                $weightRatio = $hawb->expected_weight / $masterWaybill->expected_weight;

                                $hawbPieces = ceil($masterWaybill->uld_expected_pieces * $piecesRatio);
                                $hawbWeight = round($masterWaybill->uld_expected_weight * $weightRatio, 2);

                                // Insert the HAWB into the ULD-AWB allocations
                                DB::table('uld_awbs')->insert([
                                    'uld_id' => $uldId,
                                    'manifest_id' => $id,
                                    'awb_number' => $hawb->hawb_number,
                                    'pieces' => $hawbPieces,
                                    'weight' => $hawbWeight,
                                    'branch_id' => Auth::user()->branch_id ?? 1,
                                    'created_by' => Auth::id() ?? 1,
                                    'updated_by' => Auth::id() ?? 1,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                    'uld_type' => $masterWaybill->uld_type ?? substr($uldId, 0, 3),
                                    'volume' => 0,
                                    'split_code' => 'T',
                                    'description' => 'HOUSE WAYBILL'
                                ]);

                                Log::info("Added HAWB {$hawb->hawb_number} to ULD {$uldId} with {$hawbPieces} pieces and {$hawbWeight} kg");
                            }
                        }
                    }
                }
            }

            Log::info("Using unified view: Found " . count($unifiedWaybills) . " AWBs for ULD {$uldId} in manifest {$id}");
        }

        // Filter for Master and Partial AWBs and ensure uniqueness
        $masterAndPartialAwbs = $unifiedWaybills->whereIn('waybill_type', ['MASTER', 'PARTIAL']);
        $uniqueMasterAndPartialAwbs = collect();
        $processedAwbIds = [];

        foreach ($masterAndPartialAwbs as $awb) {
            if (!in_array($awb->waybill_id, $processedAwbIds)) {
                $uniqueMasterAndPartialAwbs->push($awb);
                $processedAwbIds[] = $awb->waybill_id;
            }
        }

        $awbs = $uniqueMasterAndPartialAwbs;

        // Get unique house waybills to avoid duplicates
        $houseAwbs = $unifiedWaybills->where('waybill_type', 'HOUSE');
        $uniqueHouseAwbs = collect();
        $processedHawbIds = [];

        foreach ($houseAwbs as $hawb) {
            if (!in_array($hawb->waybill_id, $processedHawbIds)) {
                $uniqueHouseAwbs->push($hawb);
                $processedHawbIds[] = $hawb->waybill_id;
            }
        }

        $houseAwbs = $uniqueHouseAwbs;

        // Process House AWBs to add required properties
        foreach ($houseAwbs as $hawb) {
            // Get checkin status
            $hawb->is_checked_in = $this->checkinService->isAwbCheckedIn($hawb->waybill_id, $id);
            $hawb->checkin_record = $this->checkinService->getCheckinRecord($hawb->waybill_id, $id);
        }

        Log::info("Using unified view: Found " . count($awbs) . " Master/Partial AWBs and " . count($houseAwbs) . " House AWBs");

        // Calculate AWB count for the view
        $awbCount = count($awbs) + count($houseAwbs);

        return view('checkin.awb-list', compact('manifest', 'awbs', 'houseAwbs', 'uldType', 'uldNumber', 'uldId', 'branches', 'cargoStates', 'uldStatus', 'hasPartialAwbs', 'awbCount'));
    }

    /**
     * Process the check-in for an AWB.
     *
     * @param ProcessCheckinRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processCheckin(ProcessCheckinRequest $request)
    {
        try {
            // Check if this AWB was previously marked as missing on another flight
            if (!isset($request->skip_reconciliation) || !$request->skip_reconciliation) {
                $missingRecord = $this->reconciliationService->checkForMissingAwb($request->waybill_id);

                if ($missingRecord && $missingRecord->manifest_id !== $request->manifest_id) {
                    // Return information about the missing record for the frontend to handle
                    return response()->json([
                        'success' => true,
                        'requires_reconciliation' => true,
                        'missing_record' => [
                            'id' => $missingRecord->id,
                            'flight_number' => $missingRecord->manifest->flight_number ?? 'Unknown',
                            'flight_date' => $missingRecord->manifest->flight_date ?? 'Unknown',
                            'manifest_id' => $missingRecord->manifest_id
                        ]
                    ]);
                }
            }

            // Check if the AWB has already been checked in
            if ($this->checkinService->isAwbCheckedIn($request->waybill_id, $request->manifest_id)) {
                $checkinRecord = $this->checkinService->getCheckinRecord($request->waybill_id, $request->manifest_id);

                // Update the existing record
                $checkinRecord = $this->checkinService->updateCheckinRecord($checkinRecord->id, $request->validated());

                return response()->json([
                    'success' => true,
                    'message' => 'Check-in record updated successfully',
                    'data' => $checkinRecord
                ]);
            }

            // Create a new check-in record
            $checkinRecord = $this->checkinService->createCheckinRecord($request->validated());

            // Dispatch event
            event(new \App\Domains\Checkin\Events\AwbCheckedIn($checkinRecord));

            return response()->json([
                'success' => true,
                'message' => 'AWB checked in successfully',
                'data' => $checkinRecord
            ]);
        } catch (CheckinException $e) {
            Log::error('Check-in error: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'manifest_id' => $request->manifest_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        } catch (ReconciliationException $e) {
            Log::error('Reconciliation error during check-in: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'manifest_id' => $request->manifest_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        } catch (\Exception $e) {
            Log::error('Unexpected error during check-in: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'manifest_id' => $request->manifest_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during check-in. Please try again.'
            ], 500);
        }
    }

    /**
     * Reconcile a missing AWB with a new flight.
     *
     * @param ReconcileAwbRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reconcileAwb(ReconcileAwbRequest $request)
    {
        try {
            // Get the missing checkin record
            $missingRecord = CheckinRecord::findOrFail($request->missing_record_id);

            // Reconcile the AWB
            $newRecord = $this->reconciliationService->reconcileAwb(
                $missingRecord,
                $request->new_flight_id,
                Auth::id(),
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'AWB successfully reconciled',
                'data' => $newRecord
            ]);
        } catch (ReconciliationException $e) {
            Log::error('Reconciliation error: ' . $e->getMessage(), [
                'missing_record_id' => $request->missing_record_id,
                'new_flight_id' => $request->new_flight_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        } catch (\Exception $e) {
            Log::error('Unexpected error during reconciliation: ' . $e->getMessage(), [
                'missing_record_id' => $request->missing_record_id,
                'new_flight_id' => $request->new_flight_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during reconciliation. Please try again.'
            ], 500);
        }
    }

    /**
     * Display the Inward Flight Report (IFR).
     */
    public function ifr($id)
    {
        $data = $this->checkinService->generateIFR($id);

        if (!$data) {
            return redirect()->route('checkin.index')
                ->with('error', 'Flight manifest not found.');
        }

        // Format the data for the IFR view
        $ifrData = [
            'flight' => [
                'flight_number' => $data['manifest']->flight_number,
                'departure_airport' => $data['manifest']->departure_airport,
                'arrival_airport' => $data['manifest']->arrival_airport,
                'flight_date' => $data['manifest']->flight_date,
            ],
            'summary' => [
                'total_expected_pieces' => $data['total_pieces'],
                'total_expected_weight' => $data['total_weight'],
                'total_received_pieces' => $data['total_pieces_received'],
                'total_received_weight' => $data['total_weight_received'],
                'total_missing_pieces' => $data['total_missing_pieces'],
                'total_missing_weight' => $data['total_missing_weight'],
                'total_damaged_pieces' => $data['total_damaged_pieces'],
                'total_damaged_weight' => $data['total_damaged_weight'],
                'total_excess_pieces' => $data['total_excess_pieces'],
                'total_excess_weight' => $data['total_excess_weight'],
            ],
            'waybills' => [],
            'excess_documents' => [],
        ];

        // Format waybills data
        foreach ($data['awbs'] as $awb) {
            $checkinRecord = $data['checkin_records'][$awb->awb_number] ?? null;

            // Initialize counters for this AWB
            $damagedPieces = 0;
            $damagedWeight = 0;
            $excessPieces = 0;
            $excessWeight = 0;

            // Calculate damaged and excess pieces from cargo states if available
            if ($checkinRecord && $checkinRecord->cargoStates) {
                foreach ($checkinRecord->cargoStates as $state) {
                    // Damaged cargo
                    if (in_array($state->state_id, ['DAMAGED', 'DISC', 'DMG', 'WET', 'TORN'])) {
                        $damagedPieces += $state->pieces;
                        $damagedWeight += $state->weight;
                    }

                    // Excess cargo
                    if (in_array($state->state_id, ['FDCA', 'FDAW'])) {
                        $excessPieces += $state->pieces;
                        $excessWeight += $state->weight;
                    }
                }
            } else if ($checkinRecord && $checkinRecord->is_damaged) {
                // If no cargo states but is_damaged is true
                $damagedPieces = $checkinRecord->pieces_received;
                $damagedWeight = $checkinRecord->weight_received;
            }

            // Calculate missing pieces and weight
            $missingPieces = $checkinRecord ? max(0, $awb->total_pieces - $checkinRecord->pieces_received) : $awb->total_pieces;
            $missingWeight = $checkinRecord ? max(0, $awb->total_weight - $checkinRecord->weight_received) : $awb->total_weight;

            $waybillData = [
                'waybill_id' => $awb->awb_number,
                'type_code' => $awb->type_code,
                'origin_location' => $awb->origin_airport,
                'destination_location' => $awb->destination_airport,
                'expected_pieces' => $awb->total_pieces,
                'expected_weight' => $awb->total_weight,
                'received_pieces' => $checkinRecord ? $checkinRecord->pieces_received : 0,
                'received_weight' => $checkinRecord ? $checkinRecord->weight_received : 0,
                'missing_pieces' => $missingPieces,
                'missing_weight' => $missingWeight,
                'damaged_pieces' => $damagedPieces,
                'damaged_weight' => $damagedWeight,
                'excess_pieces' => $excessPieces,
                'excess_weight' => $excessWeight,
                'status' => $checkinRecord ? 'RECEIVED' : 'PENDING',
                'cargo_states' => [],
            ];

            // Add cargo states if available
            if ($checkinRecord && $checkinRecord->cargoStates) {
                foreach ($checkinRecord->cargoStates as $state) {
                    $waybillData['cargo_states'][] = [
                        'state_code' => $state->state_id,
                        'pieces' => $state->pieces,
                        'weight' => $state->weight,
                        'notes' => $state->notes,
                    ];
                }
            }

            $ifrData['waybills'][] = $waybillData;
        }

        // Extract manifest from data for easier access in the view
        $manifest = $data['manifest'];

        return view('checkin.ifr-kq', compact('data', 'ifrData', 'manifest'));
    }

    /**
     * Display the KQ Airways Inward Flight Report (IFR).
     */
    public function ifrKq($id)
    {
        $data = $this->checkinService->generateIFR($id);

        if (!$data) {
            return redirect()->route('checkin.index')
                ->with('error', 'Flight manifest not found.');
        }

        // Format the data for the IFR view
        $ifrData = $this->formatIfrData($data);

        // Extract manifest from data for easier access in the view
        $manifest = $data['manifest'];

        return view('checkin.ifr-kq', compact('data', 'ifrData', 'manifest'));
    }

    /**
     * Display the new Inward Flight Report (IFR) template.
     */
    public function ifrNew($id)
    {
        $data = $this->checkinService->generateIFR($id);

        if (!$data) {
            return redirect()->route('checkin.index')
                ->with('error', 'Flight manifest not found.');
        }

        // Format the data for the IFR view
        $ifrData = $this->formatIfrData($data);

        // Ensure excess_documents is always set
        if (!isset($ifrData['excess_documents'])) {
            $ifrData['excess_documents'] = [];
        }

        // Extract manifest from data for easier access in the view
        $manifest = $data['manifest'];

        // Prepare data for the new template
        $expected_pieces = 0;
        $expected_weight = 0;
        $received_pieces = 0;
        $received_weight = 0;
        $missing_pieces = 0;
        $missing_weight = 0;
        $damaged_pieces = 0;
        $damaged_weight = 0;
        $excess_pieces = 0;
        $excess_weight = 0;

        // Use the summary data from ifrData if available
        if (isset($ifrData['summary'])) {
            $expected_pieces = $ifrData['summary']['total_expected_pieces'];
            $expected_weight = $ifrData['summary']['total_expected_weight'];
            $received_pieces = $ifrData['summary']['total_received_pieces'];
            $received_weight = $ifrData['summary']['total_received_weight'];
            $missing_pieces = $ifrData['summary']['total_missing_pieces'];
            $missing_weight = $ifrData['summary']['total_missing_weight'];
            $damaged_pieces = $ifrData['summary']['total_damaged_pieces'];
            $damaged_weight = $ifrData['summary']['total_damaged_weight'];
            $excess_pieces = $ifrData['summary']['total_excess_pieces'];
            $excess_weight = $ifrData['summary']['total_excess_weight'];
        } else {
            // Calculate totals from waybills if summary data is not available
            foreach ($ifrData['waybills'] as $waybill) {
                $expected_pieces += $waybill['expected_pieces'] ?? 0;
                $expected_weight += $waybill['expected_weight'] ?? 0;
                $received_pieces += $waybill['received_pieces'] ?? 0;
                $received_weight += $waybill['received_weight'] ?? 0;
                $missing_pieces += $waybill['missing_pieces'] ?? 0;
                $missing_weight += $waybill['missing_weight'] ?? 0;
                $damaged_pieces += $waybill['damaged_pieces'] ?? 0;
                $damaged_weight += $waybill['damaged_weight'] ?? 0;
                $excess_pieces += $waybill['excess_pieces'] ?? 0;
                $excess_weight += $waybill['excess_weight'] ?? 0;
            }
        }

        // Prepare irregularity data
        $missing_cargo = [];
        $missing_awb = [];
        $damaged_cargo = [];
        $excess_cargo = [];
        $excess_docs = [];

        // Extract irregularity data from waybills
        foreach ($ifrData['waybills'] as $waybill) {
            // Get the actual waybill record to check for is_completely_missing flag
            $waybillRecord = MasterWaybill::where('awb_number', $waybill['waybill_id'])->first();

            // Add to missing AWB list if the waybill has no received pieces
            if ($waybill['received_pieces'] == 0 && $waybill['expected_pieces'] > 0) {
                $missing_awb[] = [
                    'awb_number' => $waybill['waybill_id'],
                    'remarks' => 'AWB not received'
                ];
            }

            // Add to missing cargo list if the waybill is marked as completely missing
            if ($waybillRecord && $waybillRecord->is_completely_missing) {
                $uldInfo = DB::table('uld_awbs')
                    ->where('awb_number', $waybill['waybill_id'])
                    ->where('manifest_id', $manifest->manifest_id)
                    ->first();
                $uldId = $uldInfo->uld_id ?? 'BULK';

                // Use the actual pieces and weight from the waybill record
                $missing_cargo[] = [
                    'awb_number' => $waybill['waybill_id'],
                    'uld_id' => $uldId,
                    'pieces' => $waybillRecord->total_pieces,
                    'weight' => $waybillRecord->total_weight,
                    'remarks' => 'Completely missing cargo'
                ];
            }

            // Get ULD information for this AWB
            $uldInfo = DB::table('uld_awbs')
                ->join('uld_details', 'uld_awbs.uld_id', '=', 'uld_details.uld_id')
                ->where('uld_awbs.awb_number', $waybill['waybill_id'])
                ->where('uld_awbs.manifest_id', $manifest->manifest_id)
                ->select('uld_details.uld_id', 'uld_details.uld_type')
                ->first();

            // Format ULD as TYPE-CODE (e.g., PWC-1234)
            if ($uldInfo && $uldInfo->uld_type) {
                $uldId = $uldInfo->uld_type . '-' . $uldInfo->uld_id;
            } else {
                $uldId = $uldInfo ? $uldInfo->uld_id : 'BULK';
            }

            // Add to missing cargo list if there are missing pieces
            if ($waybill['missing_pieces'] > 0 && $waybill['received_pieces'] > 0) {
                $missing_cargo[] = [
                    'awb_number' => $waybill['waybill_id'],
                    'uld_id' => $uldId,
                    'pieces' => $waybill['missing_pieces'],
                    'weight' => $waybill['missing_weight'],
                    'remarks' => 'Partial shipment received'
                ];
            }

            // Add to damaged cargo list if there are damaged pieces
            if ($waybill['damaged_pieces'] > 0) {
                $damaged_cargo[] = [
                    'awb_number' => $waybill['waybill_id'],
                    'uld_id' => $uldId,
                    'pieces' => $waybill['damaged_pieces'],
                    'weight' => $waybill['damaged_weight'],
                    'remarks' => 'Damaged cargo'
                ];
            }

            // Add to excess cargo list if there are excess pieces
            if ($waybill['excess_pieces'] > 0) {
                $excess_cargo[] = [
                    'awb_number' => $waybill['waybill_id'],
                    'uld_id' => $uldId,
                    'pieces' => $waybill['excess_pieces'],
                    'weight' => $waybill['excess_weight'],
                    'remarks' => 'Excess cargo'
                ];
            }

            // Process cargo states if available
            if (isset($waybill['cargo_states'])) {
                foreach ($waybill['cargo_states'] as $state) {
                    // Update existing entries or add new ones
                    switch ($state['state_code']) {
                        case 'MSCA':
                            // Update existing entry or add new one
                            $found = false;
                            foreach ($missing_cargo as &$entry) {
                                if ($entry['awb_number'] === $waybill['waybill_id']) {
                                    $entry['remarks'] = $state['notes'] ?? $entry['remarks'];
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $missing_cargo[] = [
                                    'awb_number' => $waybill['waybill_id'],
                                    'uld_id' => $uldId,
                                    'pieces' => $state['pieces'],
                                    'weight' => $state['weight'],
                                    'remarks' => $state['notes'] ?? 'Missing cargo'
                                ];
                            }
                            break;

                        case 'MSAW':
                            // Update existing entry or add new one
                            $found = false;
                            foreach ($missing_awb as &$entry) {
                                if ($entry['awb_number'] === $waybill['waybill_id']) {
                                    $entry['remarks'] = $state['notes'] ?? $entry['remarks'];
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $missing_awb[] = [
                                    'awb_number' => $waybill['waybill_id'],
                                    'remarks' => $state['notes'] ?? 'Missing AWB'
                                ];
                            }
                            break;

                        case 'DISC':
                        case 'DMG':
                        case 'WET':
                        case 'TORN':
                            // Update existing entry or add new one
                            $found = false;
                            foreach ($damaged_cargo as &$entry) {
                                if ($entry['awb_number'] === $waybill['waybill_id']) {
                                    $entry['remarks'] = $state['notes'] ?? $entry['remarks'];
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $damaged_cargo[] = [
                                    'awb_number' => $waybill['waybill_id'],
                                    'uld_id' => $uldId,
                                    'pieces' => $state['pieces'],
                                    'weight' => $state['weight'],
                                    'remarks' => $state['notes'] ?? 'Damaged cargo'
                                ];
                            }
                            break;

                        case 'FDCA':
                            // Update existing entry or add new one
                            $found = false;
                            foreach ($excess_cargo as &$entry) {
                                if ($entry['awb_number'] === $waybill['waybill_id']) {
                                    $entry['remarks'] = $state['notes'] ?? $entry['remarks'];
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $excess_cargo[] = [
                                    'awb_number' => $waybill['waybill_id'],
                                    'uld_id' => $uldId,
                                    'pieces' => $state['pieces'],
                                    'weight' => $state['weight'],
                                    'remarks' => $state['notes'] ?? 'Excess cargo'
                                ];
                            }
                            break;

                        case 'FDAW':
                            // Update existing entry or add new one
                            $found = false;
                            foreach ($excess_docs as &$entry) {
                                if ($entry['awb_number'] === $waybill['waybill_id']) {
                                    $entry['remarks'] = $state['notes'] ?? $entry['remarks'];
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $excess_docs[] = [
                                    'awb_number' => $waybill['waybill_id'],
                                    'uld_id' => $uldId,
                                    'pieces' => $state['pieces'],
                                    'weight' => $state['weight'],
                                    'remarks' => $state['notes'] ?? 'Excess documents'
                                ];
                            }
                            break;
                    }
                }
            }
        }

        // Format weights to 2 decimal places
        $expected_weight = number_format($expected_weight, 2);
        $received_weight = number_format($received_weight, 2);
        $missing_weight = number_format($missing_weight, 2);
        $damaged_weight = number_format($damaged_weight, 2);
        $excess_weight = number_format($excess_weight, 2);

        // Set waybills variable for the view
        $waybills = $ifrData['waybills'];

        return view('checkin.ifr-std', compact(
            'data',
            'ifrData',
            'manifest',
            'expected_pieces',
            'expected_weight',
            'received_pieces',
            'received_weight',
            'missing_pieces',
            'missing_weight',
            'damaged_pieces',
            'damaged_weight',
            'excess_pieces',
            'excess_weight',
            'missing_cargo',
            'missing_awb',
            'damaged_cargo',
            'excess_cargo',
            'excess_docs',
            'waybills'
        ));
    }

    /**
     * Helper method to format IFR data
     */
    private function formatIfrData($data)
    {
        $ifrData = [
            'flight' => [
                'flight_number' => $data['manifest']->flight_number,
                'departure_airport' => $data['manifest']->departure_airport,
                'arrival_airport' => $data['manifest']->arrival_airport,
                'flight_date' => $data['manifest']->flight_date,
            ],
            'summary' => [
                'total_expected_pieces' => $data['total_pieces'],
                'total_expected_weight' => $data['total_weight'],
                'total_received_pieces' => $data['total_pieces_received'],
                'total_received_weight' => $data['total_weight_received'],
                'total_missing_pieces' => $data['total_missing_pieces'],
                'total_missing_weight' => $data['total_missing_weight'],
                'total_damaged_pieces' => $data['total_damaged_pieces'],
                'total_damaged_weight' => $data['total_damaged_weight'],
                'total_excess_pieces' => $data['total_excess_pieces'],
                'total_excess_weight' => $data['total_excess_weight'],
            ],
            'waybills' => [],
        ];

        // Format waybills data
        foreach ($data['awbs'] as $awb) {
            $checkinRecord = $data['checkin_records'][$awb->awb_number] ?? null;

            // Determine waybill type
            $typeCode = '740'; // Default to MAWB
            if (isset($awb->is_house) && $awb->is_house) {
                $typeCode = '703'; // HAWB
            } elseif (isset($awb->has_houses) && !$awb->has_houses) {
                $typeCode = '741'; // Direct
            }

            // Calculate damaged pieces and weight
            $damagedPieces = 0;
            $damagedWeight = 0;

            if ($checkinRecord && isset($checkinRecord->is_damaged) && $checkinRecord->is_damaged) {
                // If the cargo is damaged, count all received pieces as damaged
                $damagedPieces = $checkinRecord->pieces_received;
                $damagedWeight = $checkinRecord->weight_received;
            }

            // Initialize waybill data
            $waybillData = [
                'waybill_id' => $awb->awb_number,
                'type_code' => $typeCode,
                'origin' => $awb->origin ?? 'DXB',
                'destination' => $awb->destination ?? 'LLW',
                'origin_location' => $awb->origin_airport ?? $awb->origin ?? 'DXB',
                'destination_location' => $awb->destination_airport ?? $awb->destination ?? 'LLW',
                'expected_pieces' => $awb->total_pieces ?? $awb->pieces ?? 0,
                'expected_weight' => $awb->total_weight ?? $awb->weight ?? 0,
                'received_pieces' => $checkinRecord ? $checkinRecord->pieces_received : 0,
                'received_weight' => $checkinRecord ? $checkinRecord->weight_received : 0,
                'missing_pieces' => $checkinRecord ? max(0, ($awb->total_pieces ?? $awb->pieces ?? 0) - $checkinRecord->pieces_received) : ($awb->total_pieces ?? $awb->pieces ?? 0),
                'missing_weight' => $checkinRecord ? max(0, ($awb->total_weight ?? $awb->weight ?? 0) - $checkinRecord->weight_received) : ($awb->total_weight ?? $awb->weight ?? 0),
                'damaged_pieces' => $damagedPieces,
                'damaged_weight' => $damagedWeight,
                'excess_pieces' => 0, // Default to 0 as we don't have excess data
                'excess_weight' => 0, // Default to 0 as we don't have excess data
                'status' => $checkinRecord ? ($checkinRecord->is_completely_missing ? 'MISSING' : 'RECEIVED') : 'PENDING',
                'is_completely_missing' => $awb->is_completely_missing ?? false,
                'cargo_states' => [],
            ];

            // Add cargo states if available
            if ($checkinRecord) {
                // Add damaged cargo state if the cargo is damaged
                if (isset($checkinRecord->is_damaged) && $checkinRecord->is_damaged) {
                    $waybillData['cargo_states'][] = [
                        'state_code' => 'DISC', // Damaged cargo
                        'pieces' => $damagedPieces,
                        'weight' => $damagedWeight,
                        'notes' => $checkinRecord->damage_notes ?? 'Damaged cargo',
                    ];
                }

                // Add missing cargo state if there are missing pieces
                if ($waybillData['missing_pieces'] > 0) {
                    $waybillData['cargo_states'][] = [
                        'state_code' => 'MSCA', // Missing cargo
                        'pieces' => $waybillData['missing_pieces'],
                        'weight' => $waybillData['missing_weight'],
                        'notes' => 'Missing cargo',
                    ];
                }

                // Add completely missing cargo state if the waybill is marked as completely missing
                if (isset($checkinRecord->is_completely_missing) && $checkinRecord->is_completely_missing) {
                    $waybillData['cargo_states'][] = [
                        'state_code' => 'MSCA', // Missing cargo
                        'pieces' => $awb->pieces ?? 0,
                        'weight' => $awb->weight ?? 0,
                        'notes' => 'Completely missing cargo',
                    ];
                }

                // Add cargo states from the database
                if ($checkinRecord->cargoStates && $checkinRecord->cargoStates->count() > 0) {
                    foreach ($checkinRecord->cargoStates as $state) {
                        $waybillData['cargo_states'][] = [
                            'state_code' => $state->state_id,
                            'pieces' => $state->pieces,
                            'weight' => $state->weight,
                            'notes' => $state->notes ?? 'Cargo state: ' . $state->state_id,
                        ];
                    }
                }
            }

            // If the waybill is completely missing but no check-in record exists
            if (($awb->is_completely_missing ?? false) && !$checkinRecord) {
                $waybillData['cargo_states'][] = [
                    'state_code' => 'MSCA', // Missing cargo
                    'pieces' => $awb->pieces ?? 0,
                    'weight' => $awb->weight ?? 0,
                    'notes' => 'Completely missing cargo',
                ];
                $waybillData['status'] = 'MISSING';
            }

            $ifrData['waybills'][] = $waybillData;
        }

        return $ifrData;
    }

    /**
     * Store a new check-in record.
     * This is an alias for processCheckin to maintain compatibility with the view.
     */
    public function store(Request $request)
    {
        try {
            // Validate the request manually since we can't use the form request
            $validator = Validator::make($request->all(), [
                'waybill_id' => 'required|string',
                'manifest_id' => 'required|string|exists:flight_manifests,manifest_id',
                'uld_id' => 'nullable|string',
                'pieces_received' => 'required|integer|min:0',
                'weight_received' => 'required|numeric|min:0',
                'cargo_condition' => 'required|string|in:GOOD,PARTIAL,DAMAGED',
                'is_damaged' => 'nullable|boolean',
                'is_missing' => 'nullable|boolean',
                'damage_notes' => 'nullable|string',
                'storage_location' => 'nullable|string',
                'branch_id' => 'required|integer|exists:branches,id',
                'cargo_states' => 'nullable|array',
                'cargo_states.*.state_id' => 'required|string',
                'cargo_states.*.pieces' => 'required|integer|min:0',
                'cargo_states.*.weight' => 'required|numeric|min:0',
                'cargo_states.*.notes' => 'nullable|string',
                'has_excess' => 'nullable|boolean',
                'excess_pieces' => 'nullable|integer|min:0',
                'excess_weight' => 'nullable|numeric|min:0',
                'excess_type' => 'nullable|string',
                'excess_remarks' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Create a validated data array
            $validatedData = $validator->validated();

            // Fix ULD ID format if it has duplicate type prefix (e.g., AKEAKE34463)
            if (!empty($validatedData['uld_id']) && strlen($validatedData['uld_id']) > 7) {
                $uldType = $validatedData['uld_type'] ?? '';
                if (!empty($uldType) && substr($validatedData['uld_id'], 0, 3) === $uldType && substr($validatedData['uld_id'], 0, 3) === substr($validatedData['uld_id'], 3, 3)) {
                    $fixedUldId = substr($validatedData['uld_id'], 3);
                    Log::info("Fixed ULD ID format from {$validatedData['uld_id']} to {$fixedUldId}");
                    $validatedData['uld_id'] = $fixedUldId;
                }
            }

            // Process the check-in
            $checkinRecord = $this->checkinService->createCheckinRecord($validatedData);

            // Dispatch event
            event(new \App\Domains\Checkin\Events\AwbCheckedIn($checkinRecord));

            // Redirect with success message
            return redirect()->route('checkin.manifest', ['id' => $validatedData['manifest_id']])
                ->with('success', 'AWB ' . $checkinRecord->waybill_id . ' checked in successfully');
        } catch (\Exception $e) {
            Log::error('Error processing check-in: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'manifest_id' => $request->manifest_id,
                'exception' => $e
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while processing the check-in: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Store an excess document (AWB documents only).
     *
     * @param StoreExcessDocumentRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeExcessDocument(StoreExcessDocumentRequest $request)
    {
        try {
            $validated = $request->validated();

            // Get the branch ID from the request or use the default branch (1)
            $branchId = $validated['branch_id'] ?? Auth::user()->branch_id ?? 1;

            // Create an excess document record
            $excessDocument = ExcessDocument::create([
                'awb_number' => $validated['awb_number'],
                'manifest_id' => $validated['manifest_id'],
                'pieces' => $validated['pieces'],
                'weight' => $validated['weight'] ?? 0,
                'notes' => $validated['notes'] ?? 'Excess AWB document',
                'branch_id' => $branchId,
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            Log::info("Excess document created with ID: {$excessDocument->id} for AWB: {$validated['awb_number']}");

            // Dispatch event
            event(new \App\Domains\Checkin\Events\ExcessDocumentAdded($excessDocument));

            return response()->json([
                'success' => true,
                'message' => 'Excess document added successfully',
                'data' => $excessDocument
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding excess document: ' . $e->getMessage(), [
                'awb_number' => $request->awb_number,
                'manifest_id' => $request->manifest_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the excess document. Please try again.'
            ], 500);
        }
    }



    /**
     * Reverse a check-in.
     *
     * @param ReverseCheckinRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reverseCheckin(ReverseCheckinRequest $request)
    {
        try {
            $validated = $request->validated();

            // Find the check-in record
            $checkinRecord = CheckinRecord::where('waybill_id', $validated['waybill_id'])->first();

            if (!$checkinRecord) {
                throw new \App\Domains\Checkin\Exceptions\CheckinRecordNotFoundException($validated['waybill_id']);
            }

            // Store the manifest ID and a copy of the record before deleting it
            $manifestId = $checkinRecord->manifest_id;
            $recordCopy = clone $checkinRecord;

            // Delete the check-in record
            $checkinRecord->delete();

            // Update the AWB status based on whether it's a house or master waybill
            if ($checkinRecord->is_house) {
                $awb = \App\Models\HouseWaybill::where('hawb_number', $validated['waybill_id'])->first();
            } else {
                $awb = MasterWaybill::where('awb_number', $validated['waybill_id'])->first();
            }

            if ($awb) {
                $awb->status = 'PENDING';
                $awb->save();
            }

            // Clear cache
            $this->checkinService->clearCache($manifestId);

            // Dispatch event
            event(new \App\Domains\Checkin\Events\CheckinReversed($recordCopy, $validated['reason']));

            return response()->json([
                'success' => true,
                'message' => 'Check-in reversed successfully'
            ]);
        } catch (CheckinException $e) {
            Log::error('Error reversing check-in: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 404);
        } catch (\Exception $e) {
            Log::error('Unexpected error reversing check-in: ' . $e->getMessage(), [
                'waybill_id' => $request->waybill_id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while reversing the check-in. Please try again.'
            ], 500);
        }
    }

    /**
     * Update ULD status manually.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUldStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'uld_id' => 'required|string',
                'manifest_id' => 'required|string|exists:flight_manifests,manifest_id',
                'status' => 'required|string|in:COMPLETE,PARTIAL,PENDING',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Update ULD status directly in the database
            $updated = DB::table('uld_details')
                ->where('uld_id', $request->uld_id)
                ->where('manifest_id', $request->manifest_id)
                ->update(['status' => $request->status]);

            if (!$updated) {
                return response()->json([
                    'success' => false,
                    'message' => 'ULD not found or status could not be updated'
                ], 404);
            }

            // Clear cache
            $this->checkinService->clearCache($request->manifest_id);

            return response()->json([
                'success' => true,
                'message' => 'ULD status updated to ' . $request->status . ' successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating ULD status: ' . $e->getMessage(), [
                'uld_id' => $request->uld_id,
                'manifest_id' => $request->manifest_id,
                'status' => $request->status,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the ULD status. Please try again.'
            ], 500);
        }
    }
}
