<?php

namespace App\Domains\Checkin\Services;

use App\Models\CheckinRecord;
use App\Models\MasterWaybill;
use App\Models\HouseWaybill;
use App\Models\FlightManifest as FlightManifestModel;
use App\Models\ExcessDocument;
use App\Domains\Finance\Helpers\MailHelper;
use App\Domains\Finance\Helpers\RoyalFlightHelper;
use App\Domains\Finance\Helpers\HumanRemainsHelper;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Services\PartialAwbService;

class CheckinService
{
    /**
     * Cache prefix for this domain
     */
    protected static $cachePrefix = 'checkin';

    /**
     * Flag to determine if HAWBs should be fetched for MAWBs
     */
    protected $getHawbsForMawbs = false;

    /**
     * Partial AWB service instance
     */
    protected $partialAwbService;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->partialAwbService = new PartialAwbService();
    }

    /**
     * Get all flight manifests available for check-in (ARRIVED status)
     * Now includes royal flights for dual-path processing
     */
    public function getManifestsForCheckin()
    {
        return FlightManifestModel::where('status', 'ARRIVED')
            ->orderBy('scheduled_arrival', 'desc')
            ->get();
    }

    /**
     * Get all flight manifests pending reception (DEPARTED status)
     * Now includes royal flights for dual-path processing
     */
    public function getPendingFlights()
    {
        return FlightManifestModel::where('status', 'DEPARTED')
            ->orderBy('scheduled_arrival', 'desc')
            ->get();
    }

    /**
     * Get a flight manifest by ID
     */
    public function getManifestById($id)
    {
        return FlightManifestModel::where('manifest_id', $id)
            ->where('status', 'ARRIVED')
            ->first();
    }

    /**
     * Get all ULDs for a flight manifest
     */
    public function getManifestUlds($manifestId)
    {
        $cacheKey = static::$cachePrefix . '.ulds.' . $manifestId;

        // Clear the cache to ensure we get fresh data
        Cache::forget($cacheKey);

        return Cache::remember($cacheKey, 3600, function () use ($manifestId) {
            $ulds = DB::table('uld_details')
                ->where('manifest_id', $manifestId)
                ->get();

            // Process ULDs to extract number from ID
            foreach ($ulds as $uld) {
                // If uld_type is already set, use it
                if (!empty($uld->uld_type)) {
                    // Extract the number part from the ULD ID
                    // Assuming format like "AKE12345" where "AKE" is the type
                    if (preg_match('/^([A-Z]{2,3})(\d+)$/', $uld->uld_id, $matches)) {
                        $uld->uld_number = $matches[2];
                    } else {
                        $uld->uld_number = $uld->uld_id;
                    }
                } else {
                    // If uld_type is not set, try to extract it from uld_id
                    if (preg_match('/^([A-Z]{2,3})(\d+)$/', $uld->uld_id, $matches)) {
                        $uld->uld_type = $matches[1];
                        $uld->uld_number = $matches[2];
                    } else {
                        // Default values if we can't parse
                        $uld->uld_type = 'ULD';
                        $uld->uld_number = $uld->uld_id;
                    }
                }

                // Set loaded pieces and weight
                $uld->loaded_pieces = $uld->pieces ?? 0;
                $uld->loaded_weight = $uld->weight ?? 0;

                // Set default status if not set
                if (!isset($uld->status)) {
                    $uld->status = 'PENDING';
                }

                // Check if this ULD has partial AWBs
                $partialCount = DB::table('partial_waybills')
                    ->where('manifest_id', $manifestId)
                    ->where('uld_id', $uld->uld_id)
                    ->count();

                $uld->has_partial_awbs = $partialCount > 0;

                // If ULD has partial AWBs, update its status to PARTIAL if not already COMPLETE
                if ($uld->has_partial_awbs && (!isset($uld->status) || $uld->status != 'COMPLETE')) {
                    $uld->status = 'PARTIAL';

                    // Also update the database
                    DB::table('uld_details')
                        ->where('uld_id', $uld->uld_id)
                        ->where('manifest_id', $manifestId)
                        ->update(['status' => 'PARTIAL']);

                    Log::info("ULD {$uld->uld_id} has {$partialCount} partial AWBs - status updated to PARTIAL");
                } elseif ($uld->has_partial_awbs) {
                    Log::info("ULD {$uld->uld_id} has {$partialCount} partial AWBs but status remains {$uld->status}");
                }
            }

            return $ulds;
        });
    }

    /**
     * Get all AWBs for a ULD
     */
    public function getUldAwbs($uldId, $manifestId)
    {
        // Get HAWBs for each MAWB
        $this->getHawbsForMawbs = true;
        $cacheKey = static::$cachePrefix . '.uld_awbs.' . $uldId . '.' . $manifestId;

        // Clear the cache to ensure we get fresh data
        Cache::forget($cacheKey);

        return Cache::remember($cacheKey, 3600, function () use ($uldId, $manifestId) {
            // Get regular AWBs assigned to this ULD using the ULD-AWB allocations table
            $awbs = DB::table('uld_awb_allocations')
                ->join('master_waybills', 'uld_awb_allocations.awb_number', '=', 'master_waybills.awb_number')
                ->where('uld_awb_allocations.uld_id', $uldId)
                ->where('uld_awb_allocations.manifest_id', $manifestId)
                ->select(
                    'master_waybills.*',
                    'uld_awb_allocations.assigned_pieces as pieces',
                    'uld_awb_allocations.assigned_weight as weight'
                )
                ->get();

            // If no AWBs found in uld_awb_allocations, try the legacy uld_awbs table
            if (count($awbs) === 0) {
                $awbs = DB::table('uld_awbs')
                    ->join('master_waybills', 'uld_awbs.awb_number', '=', 'master_waybills.awb_number')
                    ->where('uld_awbs.uld_id', $uldId)
                    ->where('uld_awbs.manifest_id', $manifestId)
                    ->select('master_waybills.*', 'uld_awbs.pieces', 'uld_awbs.weight')
                    ->get();
            }

            // Log the number of AWBs found
            Log::info("Found " . count($awbs) . " AWBs for ULD {$uldId} in manifest {$manifestId}");

            // Get partial AWBs assigned to this ULD
            $partialAwbs = DB::table('partial_waybills')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $manifestId)
                ->get();

            // Log the number of partial AWBs found
            Log::info("Found " . count($partialAwbs) . " partial AWBs for ULD {$uldId} in manifest {$manifestId}");

            // Create a collection to store the processed AWBs
            $processedAwbs = collect();

            // Keep track of AWB numbers that have been processed
            $processedAwbNumbers = [];

            // Process regular AWBs to add waybill_id property
            foreach ($awbs as $awb) {
                // Map awb_number to waybill_id for compatibility with the view
                $awb->waybill_id = $awb->awb_number;

                // Set origin and destination location properties
                $awb->origin_location = $awb->origin_airport;
                $awb->destination_location = $awb->destination_airport;

                // Set ULD expected pieces and weight
                $awb->uld_expected_pieces = $awb->pieces;
                $awb->uld_expected_weight = $awb->weight;

                // Set total AWB pieces and weight for the check-in modal
                $awb->total_awb_pieces = $awb->total_pieces;
                $awb->total_awb_weight = $awb->total_weight;

                // Set default values for other properties
                $awb->uld_received_pieces = 0;
                $awb->uld_checkin_status = 'PENDING';

                // Generate status badge
                $statusClass = 'secondary';
                if ($awb->status == 'RECEIVED') $statusClass = 'success';
                elseif ($awb->status == 'PENDING') $statusClass = 'warning';
                elseif ($awb->status == 'MISSING') $statusClass = 'danger';

                $awb->status_badge = '<span class="badge bg-' . $statusClass . '">' . $awb->status . '</span>';

                // Add Handling Instructions for mail cargo
                if (MailHelper::isMailCargo($awb->special_handling_code)) {
                    $awb->is_mail = true;
                    Log::info("Mail cargo detected: {$awb->awb_number}");
                }

                // Add Handling Instructions for partial shipments
                if ($awb->is_partial) {
                    Log::info("Partial shipment detected: {$awb->awb_number}");
                }

                // Check if this AWB has a partial waybill
                $hasPartial = false;
                foreach ($partialAwbs as $partial) {
                    if ($partial->master_awb_number === $awb->awb_number) {
                        $hasPartial = true;
                        break;
                    }
                }

                // Only add the AWB if it doesn't have a partial waybill
                if (!$hasPartial) {
                    $processedAwbs->push($awb);
                    $processedAwbNumbers[] = $awb->awb_number;
                }
            }

            // Process partial AWBs and add them to the result
            foreach ($partialAwbs as $partial) {
                $awbNumber = $partial->master_awb_number ?? $partial->house_awb_number;

                // Skip if we've already processed this AWB number
                if (in_array($awbNumber, $processedAwbNumbers)) {
                    continue;
                }

                $partialAwb = (object)[
                    'waybill_id' => $awbNumber,
                    'awb_number' => $awbNumber,
                    'type_code' => $partial->house_awb_number ? '740' : '741',
                    'origin_location' => $partial->origin_airport,
                    'destination_location' => $partial->destination_airport,
                    'origin_airport' => $partial->origin_airport,
                    'destination_airport' => $partial->destination_airport,
                    'uld_expected_pieces' => $partial->received_pieces,
                    'uld_expected_weight' => $partial->received_weight,
                    'total_pieces' => $partial->expected_pieces,
                    'total_weight' => $partial->expected_weight,
                    'total_awb_pieces' => $partial->expected_pieces,
                    'total_awb_weight' => $partial->expected_weight,
                    'uld_received_pieces' => $partial->received_pieces,
                    'uld_checkin_status' => 'RECEIVED',
                    'status' => 'RECEIVED',
                    'is_partial' => true,
                    'partial_id' => $partial->partial_id,
                    'split_code' => $partial->split_code,
                    'split_sequence' => $partial->split_sequence,
                    'remaining_pieces' => $partial->remaining_pieces,
                    'remaining_weight' => $partial->remaining_weight,
                    'special_handling_code' => $partial->special_handling_code,
                ];

                // Generate status badge
                $partialAwb->status_badge = '<span class="badge bg-success">RECEIVED</span>';

                // Add Handling Instructions for mail cargo
                if (MailHelper::isMailCargo($partial->special_handling_code)) {
                    $partialAwb->is_mail = true;
                    Log::info("Mail cargo detected in partial: {$partialAwb->awb_number}");
                }

                // Add the partial AWB to the result
                $processedAwbs->push($partialAwb);
                $processedAwbNumbers[] = $awbNumber;
            }

            return $processedAwbs;
        });
    }

    /**
     * Get HAWBs for a MAWB
     */
    public function getHawbsForMawb($mawbNumber, $manifestId = null)
    {
        try {
            // Check if the house_waybills table exists and has the mawb_number column
            $hasTable = Schema::hasTable('house_waybills');
            $hasColumn = $hasTable ? Schema::hasColumn('house_waybills', 'mawb_number') : false;

            if (!$hasTable || !$hasColumn) {
                Log::warning("house_waybills table or mawb_number column does not exist. Returning empty array.");
                return collect([]);
            }

            $hawbs = DB::table('house_waybills')
                ->where('mawb_number', $mawbNumber)
                ->get();

            // Log the number of HAWBs found
            Log::info("Found " . count($hawbs) . " HAWBs for MAWB {$mawbNumber}");

            // Process HAWBs to add necessary properties
            foreach ($hawbs as $hawb) {
                // Map hawb_number to waybill_id for compatibility with the view
                $hawb->waybill_id = $hawb->hawb_number;
                $hawb->awb_number = $hawb->hawb_number;

                // Set type_code if not present
                if (!isset($hawb->type_code)) {
                    $hawb->type_code = '703'; // HAWB type code
                }

                // Set total AWB pieces and weight for the check-in modal
                $hawb->total_awb_pieces = $hawb->total_pieces ?? 0;
                $hawb->total_awb_weight = $hawb->total_weight ?? 0;

                // Check if this HAWB is assigned to a ULD
                if ($manifestId) {
                    $uldAssignment = DB::table('uld_awbs')
                        ->where('awb_number', $hawb->hawb_number)
                        ->where('manifest_id', $manifestId)
                        ->first();

                    if ($uldAssignment) {
                        $hawb->uld_id = $uldAssignment->uld_id;
                        $hawb->uld_type = $uldAssignment->uld_type;
                        $hawb->uld_expected_pieces = $uldAssignment->pieces;
                        $hawb->uld_expected_weight = $uldAssignment->weight;
                        Log::info("HAWB {$hawb->hawb_number} is assigned to ULD {$uldAssignment->uld_id}");
                    }
                }

                // Generate status badge
                $statusClass = 'secondary';
                if (isset($hawb->status)) {
                    if ($hawb->status == 'RECEIVED') $statusClass = 'success';
                    elseif ($hawb->status == 'PENDING') $statusClass = 'warning';
                    elseif ($hawb->status == 'MISSING') $statusClass = 'danger';
                }

                $hawb->status_badge = '<span class="badge bg-' . $statusClass . '">' . ($hawb->status ?? 'PENDING') . '</span>';

                // Add Handling Instructions for mail cargo
                if (isset($hawb->special_handling_code) && MailHelper::isMailCargo($hawb->special_handling_code)) {
                    $hawb->is_mail = true;
                    Log::info("Mail cargo detected in HAWB: {$hawb->hawb_number}");
                }

                // Add Handling Instructions for partial shipments
                if (isset($hawb->is_partial) && $hawb->is_partial) {
                    Log::info("Partial shipment detected in HAWB: {$hawb->hawb_number}");
                }
            }

            return $hawbs;
        } catch (\Exception $e) {
            Log::error("Error getting HAWBs for MAWB {$mawbNumber}: " . $e->getMessage());
            return collect([]);
        }
    }

    /**
     * Get all AWBs for a flight manifest that are not assigned to a ULD (BULK)
     */
    public function getBulkAwbs($manifestId)
    {
        $cacheKey = static::$cachePrefix . '.bulk_awbs.' . $manifestId;

        // Clear the cache to ensure we get fresh data
        Cache::forget($cacheKey);

        return Cache::remember($cacheKey, 3600, function () use ($manifestId) {
            // Get all AWBs assigned to ULDs
            $uldAwbNumbers = DB::table('uld_awbs')
                ->where('manifest_id', $manifestId)
                ->pluck('awb_number')
                ->toArray();

            // Get all AWBs for the manifest that are not in the ULD AWBs
            $awbs = MasterWaybill::where('manifest_id', $manifestId)
                ->whereNotIn('awb_number', $uldAwbNumbers)
                ->get();

            // Log the number of AWBs found
            Log::info("Found " . count($awbs) . " BULK AWBs in manifest {$manifestId}");

            // Get partial AWBs for this manifest that are not assigned to a ULD
            $partialAwbs = DB::table('partial_waybills')
                ->whereNull('uld_id')
                ->where('manifest_id', $manifestId)
                ->get();

            // Log the number of partial AWBs found
            Log::info("Found " . count($partialAwbs) . " partial BULK AWBs in manifest {$manifestId}");

            // Create a collection to store the processed AWBs
            $processedAwbs = collect();

            // Keep track of AWB numbers that have been processed
            $processedAwbNumbers = [];

            // Process AWBs to add waybill_id property
            foreach ($awbs as $awb) {
                // Map awb_number to waybill_id for compatibility with the view
                $awb->waybill_id = $awb->awb_number;

                // Set origin and destination location properties
                $awb->origin_location = $awb->origin_airport;
                $awb->destination_location = $awb->destination_airport;

                // Set ULD expected pieces and weight
                $awb->uld_expected_pieces = $awb->total_pieces;
                $awb->uld_expected_weight = $awb->total_weight;

                // Set total AWB pieces and weight for the check-in modal
                $awb->total_awb_pieces = $awb->total_pieces;
                $awb->total_awb_weight = $awb->total_weight;

                // Set default values for other properties
                $awb->uld_received_pieces = 0;
                $awb->uld_checkin_status = 'PENDING';

                // Generate status badge
                $statusClass = 'secondary';
                if ($awb->status == 'RECEIVED') $statusClass = 'success';
                elseif ($awb->status == 'PENDING') $statusClass = 'warning';
                elseif ($awb->status == 'MISSING') $statusClass = 'danger';

                $awb->status_badge = '<span class="badge bg-' . $statusClass . '">' . $awb->status . '</span>';

                // Add Handling Instructions for mail cargo
                if (MailHelper::isMailCargo($awb->special_handling_code)) {
                    $awb->is_mail = true;
                    Log::info("Mail cargo detected: {$awb->awb_number}");
                }

                // Add Handling Instructions for partial shipments
                if ($awb->is_partial) {
                    Log::info("Partial shipment detected: {$awb->awb_number}");
                }

                // Check if this AWB has a partial waybill
                $hasPartial = false;
                foreach ($partialAwbs as $partial) {
                    if ($partial->master_awb_number === $awb->awb_number) {
                        $hasPartial = true;
                        break;
                    }
                }

                // Only add the AWB if it doesn't have a partial waybill
                if (!$hasPartial) {
                    $processedAwbs->push($awb);
                    $processedAwbNumbers[] = $awb->awb_number;
                }
            }

            // Process partial AWBs and add them to the result
            foreach ($partialAwbs as $partial) {
                $awbNumber = $partial->master_awb_number ?? $partial->house_awb_number;

                // Skip if we've already processed this AWB number
                if (in_array($awbNumber, $processedAwbNumbers)) {
                    continue;
                }

                $partialAwb = (object)[
                    'waybill_id' => $awbNumber,
                    'awb_number' => $awbNumber,
                    'type_code' => $partial->house_awb_number ? '740' : '741',
                    'origin_location' => $partial->origin_airport,
                    'destination_location' => $partial->destination_airport,
                    'origin_airport' => $partial->origin_airport,
                    'destination_airport' => $partial->destination_airport,
                    'uld_expected_pieces' => $partial->received_pieces,
                    'uld_expected_weight' => $partial->received_weight,
                    'total_pieces' => $partial->expected_pieces,
                    'total_weight' => $partial->expected_weight,
                    'total_awb_pieces' => $partial->expected_pieces,
                    'total_awb_weight' => $partial->expected_weight,
                    'uld_received_pieces' => $partial->received_pieces,
                    'uld_checkin_status' => 'RECEIVED',
                    'status' => 'RECEIVED',
                    'is_partial' => true,
                    'partial_id' => $partial->partial_id,
                    'split_code' => $partial->split_code,
                    'split_sequence' => $partial->split_sequence,
                    'remaining_pieces' => $partial->remaining_pieces,
                    'remaining_weight' => $partial->remaining_weight,
                    'special_handling_code' => $partial->special_handling_code,
                ];

                // Generate status badge
                $partialAwb->status_badge = '<span class="badge bg-success">RECEIVED</span>';

                // Add Handling Instructions for mail cargo
                if (MailHelper::isMailCargo($partial->special_handling_code)) {
                    $partialAwb->is_mail = true;
                    Log::info("Mail cargo detected in partial: {$partialAwb->awb_number}");
                }

                // Add the partial AWB to the result
                $processedAwbs->push($partialAwb);
                $processedAwbNumbers[] = $awbNumber;
            }

            return $processedAwbs;
        });
    }

    /**
     * Check if an AWB has been checked in
     */
    public function isAwbCheckedIn($waybillId, $manifestId)
    {
        return CheckinRecord::where('waybill_id', $waybillId)
            ->where('manifest_id', $manifestId)
            ->exists();
    }

    /**
     * Get checkin record for an AWB
     */
    public function getCheckinRecord($waybillId, $manifestId)
    {
        return CheckinRecord::where('waybill_id', $waybillId)
            ->where('manifest_id', $manifestId)
            ->first();
    }

    /**
     * Create a new checkin record
     */
    public function createCheckinRecord($data)
    {
        DB::beginTransaction();

        try {
            // Check if this is a master or house waybill
            $waybill = MasterWaybill::where('awb_number', $data['waybill_id'])->first();
            $isHouse = false;

            if (!$waybill) {
                // Try to find it as a house waybill
                $waybill = HouseWaybill::where('hawb_number', $data['waybill_id'])->first();
                $isHouse = true;

                if (!$waybill) {
                    throw new \Exception('Waybill not found');
                }

                // For house waybills, check if the MAWB exists
                $masterWaybill = MasterWaybill::where('awb_number', $waybill->mawb_number)->first();
                if (!$masterWaybill) {
                    Log::warning("Master waybill {$waybill->mawb_number} not found for house waybill {$waybill->hawb_number}");
                }
            }

            // Get the manifest
            $manifest = FlightManifestModel::where('manifest_id', $data['manifest_id'])->first();

            if (!$manifest) {
                throw new \Exception('Manifest not found');
            }

            // Check if this is a mail waybill
            $isMail = MailHelper::isMailCargo($waybill->special_handling_code);

            // Check if this is a human remains waybill
            $isHumanRemains = HumanRemainsHelper::isHumanRemainsWaybill($waybill);

            // Check if this is a royal flight (for future use)
            $isRoyalFlight = RoyalFlightHelper::isRoyalFlight($manifest);
            if ($isRoyalFlight) {
                Log::info("Royal flight detected for manifest: {$manifest->manifest_id}");
            }

            // Create the checkin record
            try {
                $checkinRecord = CheckinRecord::create([
                    'waybill_id' => $data['waybill_id'],
                    'manifest_id' => $data['manifest_id'],
                    'uld_id' => $data['uld_id'] ?? null,
                    'pieces_received' => $data['pieces_received'],
                    'weight_received' => $data['weight_received'],
                    'cargo_condition' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : $data['cargo_condition'],
                    'is_damaged' => $data['is_damaged'] ?? false,
                    'is_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : ($data['is_missing'] ?? false),
                    'is_completely_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : false,
                    'damage_notes' => $data['damage_notes'] ?? null,
                    'storage_location' => $data['storage_location'] ?? null,
                    'branch_id' => $data['branch_id'],
                    'created_by' => Auth::id(),
                    'updated_by' => Auth::id(),
                    'is_house' => $isHouse,
                    'status' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : 'CHECKED_IN',
                ]);
            } catch (\Exception $e) {
                // If there's a foreign key constraint error, try to insert directly using a raw query
                if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                    Log::warning("Foreign key constraint error when creating checkin record. Trying raw insert: " . $e->getMessage());

                    // Insert using raw query to bypass foreign key constraints
                    $checkinId = DB::table('checkin_records')->insertGetId([
                        'waybill_id' => $data['waybill_id'],
                        'is_house' => $isHouse,
                        'manifest_id' => $data['manifest_id'],
                        'uld_id' => $data['uld_id'] ?? null,
                        'pieces_received' => $data['pieces_received'],
                        'weight_received' => $data['weight_received'],
                        'cargo_condition' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : $data['cargo_condition'],
                        'is_damaged' => $data['is_damaged'] ?? false,
                        'is_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : ($data['is_missing'] ?? false),
                        'is_completely_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : false,
                        'damage_notes' => $data['damage_notes'] ?? null,
                        'storage_location' => $data['storage_location'] ?? null,
                        'branch_id' => $data['branch_id'],
                        'created_by' => Auth::id(),
                        'updated_by' => Auth::id(),
                        'created_at' => now(),
                        'updated_at' => now(),
                        'status' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : 'CHECKED_IN',
                    ]);

                    $checkinRecord = CheckinRecord::find($checkinId);
                    if (!$checkinRecord) {
                        throw new \Exception("Failed to create checkin record using raw insert");
                    }

                    Log::info("Successfully created checkin record using raw insert: ID {$checkinId}");
                } else {
                    // If it's not a foreign key constraint error, rethrow the exception
                    throw $e;
                }
            }

            // Save cargo states if provided
            if (isset($data['cargo_states']) && is_array($data['cargo_states'])) {
                foreach ($data['cargo_states'] as $state) {
                    $checkinRecord->cargoStates()->create([
                        'state_id' => $state['state_id'],
                        'pieces' => $state['pieces'] ?? 0,
                        'weight' => $state['weight'] ?? 0,
                        'notes' => $state['notes'] ?? null,
                    ]);
                }
            }

            // Create a cargo state record for completely missing AWBs
            if (isset($data['is_completely_missing']) && $data['is_completely_missing']) {
                $checkinRecord->cargoStates()->create([
                    'state_id' => 'MSCA',
                    'pieces' => $waybill->total_pieces,
                    'weight' => $waybill->total_weight,
                    'notes' => 'Completely missing cargo',
                ]);

                // Update the waybill record with the missing flags
                if ($isHouse) {
                    // Update house waybill
                    $waybill->is_missing = true;
                    $waybill->is_completely_missing = true;
                    $waybill->save();

                    Log::info("Updated house waybill {$waybill->hawb_number} with missing flags");
                } else {
                    // Update master waybill
                    $waybill->is_missing = true;
                    $waybill->is_completely_missing = true;
                    $waybill->save();

                    Log::info("Updated master waybill {$waybill->awb_number} with missing flags");
                }
            }

            // Save excess cargo as a cargo state if provided
            if (isset($data['has_excess']) && $data['has_excess'] && isset($data['excess_pieces']) && $data['excess_pieces'] > 0) {
                $checkinRecord->cargoStates()->create([
                    'state_id' => $data['excess_type'] ?? 'FDCA',
                    'pieces' => $data['excess_pieces'],
                    'weight' => $data['excess_weight'] ?? 0,
                    'notes' => $data['excess_remarks'] ?? 'Excess cargo',
                ]);
            }

            // Update the waybill status
            if ($waybill) {
                $waybill->status = 'RECEIVED';

                // For mail cargo, assign Malawi Post Corporation as consignee
                if ($isMail) {
                    if ($isHouse) {
                        Log::info("Processing mail cargo (HAWB): {$waybill->hawb_number}");
                    } else {
                        Log::info("Processing mail cargo (MAWB): {$waybill->awb_number}");
                        MailHelper::assignMailConsignee($waybill);
                    }

                    // Set status to skip charges stage using the stored procedure
                    if ($isHouse) {
                        DB::statement("CALL update_house_waybill_status(?, ?)", [
                            $waybill->hawb_id,
                            'CHARGED'
                        ]);
                    } else {
                        DB::statement("CALL update_waybill_status(?, ?)", [
                            $waybill->awb_id,
                            'CHARGED'
                        ]);
                    }

                    // Refresh the model to get the updated values
                    $waybill->refresh();

                    if ($isHouse) {
                        Log::info("Mail cargo (HAWB) {$waybill->hawb_number} marked as CHARGED to skip charges stage");
                    } else {
                        Log::info("Mail cargo (MAWB) {$waybill->awb_number} marked as CHARGED to skip charges stage");
                    }
                }

                // For human remains, apply zero charge
                if ($isHumanRemains) {
                    if ($isHouse) {
                        Log::info("Processing human remains (HAWB): {$waybill->hawb_number}");
                    } else {
                        Log::info("Processing human remains (MAWB): {$waybill->awb_number}");
                        try {
                            HumanRemainsHelper::applyZeroCharge($waybill, Auth::id());
                            Log::info("Successfully applied zero charge for human remains: {$waybill->awb_number}");
                        } catch (\Exception $e) {
                            Log::error("Error applying zero charge for human remains: {$e->getMessage()}");
                            // Continue with check-in even if charge application fails
                        }
                    }
                }

                // Handle partial AWB detection and creation using PartialAwbService
                $partialResult = $this->partialAwbService->handleCheckinPartialDetection(
                    $data['waybill_id'],
                    $data['pieces_received'],
                    $data['weight_received'],
                    Auth::id(),
                    $isHouse
                );

                // Log partial detection results
                if ($partialResult['partial_created']) {
                    Log::info("Created partial waybill {$partialResult['partial_id']} for AWB {$data['waybill_id']} during check-in");
                } elseif ($partialResult['partial_updated']) {
                    Log::info("Updated partial waybill {$partialResult['partial_id']} for AWB {$data['waybill_id']} during check-in");
                }

                // Log any errors from partial detection
                if (!empty($partialResult['errors'])) {
                    foreach ($partialResult['errors'] as $error) {
                        Log::error("Partial AWB detection error for {$data['waybill_id']}: {$error}");
                    }
                }

                // Legacy partial handling for backward compatibility
                if ($data['cargo_condition'] === 'PARTIAL' || ($waybill->is_partial && $data['pieces_received'] < $waybill->total_pieces)) {
                    if ($isHouse) {
                        Log::info("Processing legacy partial waybill for HAWB: {$waybill->hawb_number}");
                    } else {
                        Log::info("Processing legacy partial waybill for MAWB: {$waybill->awb_number}");
                    }

                    // Set the is_partial flag on the waybill
                    $waybill->is_partial = true;
                    if ($isHouse) {
                        Log::info("Setting is_partial flag for HAWB: {$waybill->hawb_number}");
                    } else {
                        Log::info("Setting is_partial flag for MAWB: {$waybill->awb_number}");
                    }

                    // Calculate remaining pieces and weight
                    $remainingPieces = $waybill->total_pieces - $data['pieces_received'];
                    $remainingWeight = $waybill->total_weight - $data['weight_received'];

                    // Check if there are existing partial waybills for this AWB in this manifest
                    if ($isHouse) {
                        $existingPartials = DB::table('partial_waybills')
                            ->where('house_awb_number', $waybill->hawb_number)
                            ->where('manifest_id', $data['manifest_id'])
                            ->orderBy('split_sequence', 'desc')
                            ->get();
                    } else {
                        $existingPartials = DB::table('partial_waybills')
                            ->where('master_awb_number', $waybill->awb_number)
                            ->where('manifest_id', $data['manifest_id'])
                            ->orderBy('split_sequence', 'desc')
                            ->get();
                    }

                    if ($existingPartials->count() > 0) {
                        // Update the existing partial waybill instead of creating a new one
                        $partialWaybill = $existingPartials->first();

                        // Update the existing partial waybill record
                        DB::table('partial_waybills')
                            ->where('partial_id', $partialWaybill->partial_id)
                            ->update([
                                'received_pieces' => $data['pieces_received'],
                                'received_weight' => $data['weight_received'],
                                'remaining_pieces' => $remainingPieces,
                                'remaining_weight' => $remainingWeight,
                                'storage_location' => $data['storage_location'] ?? $partialWaybill->storage_location,
                                'updated_by' => Auth::id(),
                                'updated_at' => now()
                            ]);

                        Log::info("Partial waybill record updated with ID: {$partialWaybill->partial_id}");
                    } else {
                        // No existing partial waybill, create a new one
                        // Generate sequential partial ID consistent with XML parser
                        if ($isHouse) {
                            $partialId = $this->generateSequentialPartialId($waybill->hawb_number, true);
                            Log::info("Created partial ID: {$partialId} for house AWB: {$waybill->hawb_number}");
                        } else {
                            $partialId = $this->generateSequentialPartialId($waybill->awb_number, false);
                            Log::info("Created partial ID: {$partialId} for master AWB: {$waybill->awb_number}");
                        }

                        // Extract sequence number for database operations
                        $splitSequence = (int) substr($partialId, strrpos($partialId, '-') + 1);

                        // Check if this partial ID already exists
                        $existingPartial = DB::table('partial_waybills')
                            ->where('partial_id', $partialId)
                            ->first();

                        if (!$existingPartial) {
                            if ($isHouse) {
                                // Create the partial waybill record for house waybill
                                DB::statement("CALL create_partial_waybill(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                                    $partialId,
                                    null, // master_awb_number (null for house waybills)
                                    $data['manifest_id'],
                                    $waybill->origin_airport,
                                    $waybill->destination_airport,
                                    $waybill->total_pieces,
                                    $waybill->total_weight,
                                    $data['pieces_received'],
                                    $data['weight_received'],
                                    $remainingPieces,
                                    $remainingWeight,
                                    'P',
                                    $splitSequence,
                                    'RECEIVED',
                                    $data['storage_location'] ?? null,
                                    $data['branch_id'],
                                    Auth::id(),
                                    Auth::id()
                                ]);

                                // Update the house_awb_number field
                                DB::table('partial_waybills')
                                    ->where('partial_id', $partialId)
                                    ->update([
                                        'house_awb_number' => $waybill->hawb_number,
                                        'master_awb_number' => $waybill->mawb_number
                                    ]);
                            } else {
                                // Create the partial waybill record for master waybill
                                DB::statement("CALL create_partial_waybill(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                                    $partialId,
                                    $waybill->awb_number,
                                    $data['manifest_id'],
                                    $waybill->origin_airport,
                                    $waybill->destination_airport,
                                    $waybill->total_pieces,
                                    $waybill->total_weight,
                                    $data['pieces_received'],
                                    $data['weight_received'],
                                    $remainingPieces,
                                    $remainingWeight,
                                    'P',
                                    $splitSequence,
                                    'RECEIVED',
                                    $data['storage_location'] ?? null,
                                    $data['branch_id'],
                                    Auth::id(),
                                    Auth::id()
                                ]);
                            }

                            Log::info("Partial waybill record created with ID: {$partialId}");
                        } else {
                            Log::info("Partial waybill with ID {$partialId} already exists, skipping creation");
                        }
                    }

                    // Check if all pieces have been received
                    if ($isHouse) {
                        $totalReceivedPieces = DB::table('partial_waybills')
                            ->where('house_awb_number', $waybill->hawb_number)
                            ->where('manifest_id', $data['manifest_id'])
                            ->sum('received_pieces');

                        if ($totalReceivedPieces >= $waybill->total_pieces) {
                            // Update all partial waybills for this HAWB to mark them as reconciled
                            DB::table('partial_waybills')
                                ->where('house_awb_number', $waybill->hawb_number)
                                ->where('manifest_id', $data['manifest_id'])
                                ->update([
                                    'status' => 'RECONCILED',
                                    'reconciled_at' => now(),
                                    'updated_at' => now(),
                                    'updated_by' => Auth::id()
                                ]);

                            Log::info("All partial waybills for HAWB {$waybill->hawb_number} marked as RECONCILED");
                        }
                    } else {
                        $totalReceivedPieces = DB::table('partial_waybills')
                            ->where('master_awb_number', $waybill->awb_number)
                            ->where('manifest_id', $data['manifest_id'])
                            ->sum('received_pieces');

                        if ($totalReceivedPieces >= $waybill->total_pieces) {
                            // Update all partial waybills for this MAWB to mark them as reconciled
                            DB::table('partial_waybills')
                                ->where('master_awb_number', $waybill->awb_number)
                                ->where('manifest_id', $data['manifest_id'])
                                ->update([
                                    'status' => 'RECONCILED',
                                    'reconciled_at' => now(),
                                    'updated_at' => now(),
                                    'updated_by' => Auth::id()
                                ]);

                            Log::info("All partial waybills for MAWB {$waybill->awb_number} marked as RECONCILED");
                        }
                    }

                    // If this is a master waybill, also update any associated house waybills
                    if ($waybill->type_code === '741') {
                        // Check if the house_waybills table exists and has the master_awb_number column
                        if (Schema::hasTable('house_waybills') && Schema::hasColumn('house_waybills', 'master_awb_number')) {
                            DB::table('house_waybills')
                                ->where('master_awb_number', $waybill->awb_number)
                                ->update([
                                    'is_partial' => true,
                                    'updated_at' => now(),
                                    'updated_by' => Auth::id()
                                ]);
                            Log::info("Updated is_partial flag for house waybills under master {$waybill->awb_number}");
                        } else if (Schema::hasTable('house_waybills') && Schema::hasColumn('house_waybills', 'mawb_number')) {
                            // Try with mawb_number column instead
                            DB::table('house_waybills')
                                ->where('mawb_number', $waybill->awb_number)
                                ->update([
                                    'is_partial' => true,
                                    'updated_at' => now(),
                                    'updated_by' => Auth::id()
                                ]);
                            Log::info("Updated is_partial flag for house waybills under master {$waybill->awb_number} using mawb_number column");
                        } else {
                            Log::warning("house_waybills table or master_awb_number/mawb_number column does not exist. Skipping update for house waybills.");
                        }
                    }
                }

                $waybill->save();
            }

            // Update ULD status if applicable
            if (!empty($data['uld_type']) && !empty($data['uld_number'])) {
                $uldId = $data['uld_type'] . $data['uld_number'];
                $this->updateUldStatus($uldId, $data['manifest_id']);
            } elseif (!empty($data['uld_type']) && $data['uld_type'] === 'BULK') {
                // For BULK cargo, update the BULK status
                $this->updateBulkStatus($data['manifest_id']);
            }

            // Clear cache
            $this->clearCache($data['manifest_id']);

            DB::commit();

            return $checkinRecord;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing checkin record
     */
    public function updateCheckinRecord($id, $data)
    {
        DB::beginTransaction();

        try {
            $checkinRecord = CheckinRecord::findOrFail($id);

            $checkinRecord->update([
                'pieces_received' => $data['pieces_received'],
                'weight_received' => $data['weight_received'],
                'cargo_condition' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : $data['cargo_condition'],
                'is_damaged' => $data['is_damaged'] ?? false,
                'is_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : ($data['is_missing'] ?? false),
                'is_completely_missing' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? true : false,
                'damage_notes' => $data['damage_notes'] ?? null,
                'storage_location' => $data['storage_location'] ?? null,
                'updated_by' => Auth::id(),
                'status' => isset($data['is_completely_missing']) && $data['is_completely_missing'] ? 'MISSING' : 'CHECKED_IN',
            ]);

            // Update cargo states if provided
            if (isset($data['cargo_states']) && is_array($data['cargo_states'])) {
                // Delete existing cargo states
                $checkinRecord->cargoStates()->delete();

                // Create new cargo states
                foreach ($data['cargo_states'] as $state) {
                    $checkinRecord->cargoStates()->create([
                        'state_id' => $state['state_id'],
                        'pieces' => $state['pieces'] ?? 0,
                        'weight' => $state['weight'] ?? 0,
                        'notes' => $state['notes'] ?? null,
                    ]);
                }

                // Save excess cargo as a cargo state if provided
                if (isset($data['has_excess']) && $data['has_excess'] && isset($data['excess_pieces']) && $data['excess_pieces'] > 0) {
                    $checkinRecord->cargoStates()->create([
                        'state_id' => $data['excess_type'] ?? 'FDCA',
                        'pieces' => $data['excess_pieces'],
                        'weight' => $data['excess_weight'] ?? 0,
                        'notes' => $data['excess_remarks'] ?? 'Excess cargo',
                    ]);
                }
            }

            // Get the waybill
            $waybill = null;
            $isHouse = $checkinRecord->is_house ?? false;

            if ($isHouse) {
                $waybill = HouseWaybill::where('hawb_number', $checkinRecord->waybill_id)->first();
            } else {
                $waybill = MasterWaybill::where('awb_number', $checkinRecord->waybill_id)->first();
            }

            // Create a cargo state record for completely missing AWBs
            if (isset($data['is_completely_missing']) && $data['is_completely_missing'] && $waybill) {
                // Delete existing MSCA cargo states
                $checkinRecord->cargoStates()->where('state_id', 'MSCA')->delete();

                // Create new MSCA cargo state
                $checkinRecord->cargoStates()->create([
                    'state_id' => 'MSCA',
                    'pieces' => $waybill->total_pieces,
                    'weight' => $waybill->total_weight,
                    'notes' => 'Completely missing cargo',
                ]);

                // Update the waybill record with the missing flags
                if ($isHouse) {
                    // Update house waybill
                    $waybill->is_missing = true;
                    $waybill->is_completely_missing = true;
                    $waybill->save();

                    Log::info("Updated house waybill {$waybill->hawb_number} with missing flags");
                } else {
                    // Update master waybill
                    $waybill->is_missing = true;
                    $waybill->is_completely_missing = true;
                    $waybill->save();

                    Log::info("Updated master waybill {$waybill->awb_number} with missing flags");
                }
            } else if ($waybill && (!isset($data['is_completely_missing']) || !$data['is_completely_missing'])) {
                // If the AWB is no longer completely missing, update the flags
                if ($waybill->is_completely_missing) {
                    if ($isHouse) {
                        // Update house waybill
                        $waybill->is_missing = false;
                        $waybill->is_completely_missing = false;
                        $waybill->save();

                        Log::info("Cleared missing flags for house waybill {$waybill->hawb_number}");
                    } else {
                        // Update master waybill
                        $waybill->is_missing = false;
                        $waybill->is_completely_missing = false;
                        $waybill->save();

                        Log::info("Cleared missing flags for master waybill {$waybill->awb_number}");
                    }

                    // Delete any MSCA cargo states
                    $checkinRecord->cargoStates()->where('state_id', 'MSCA')->delete();
                }
            }

            // Get the waybill
            $waybill = null;
            $isHouse = $checkinRecord->is_house ?? false;

            // Update partial waybill record if this is a partial check-in
            if ($waybill && ($data['cargo_condition'] === 'PARTIAL' || ($waybill->is_partial && $data['pieces_received'] < $waybill->total_pieces))) {
                if ($isHouse) {
                    Log::info("Processing partial waybill for HAWB: {$waybill->hawb_number} (update)");
                } else {
                    Log::info("Processing partial waybill for MAWB: {$waybill->awb_number} (update)");
                }

                // Set the is_partial flag on the waybill
                $waybill->is_partial = true;
                $waybill->save();

                if ($isHouse) {
                    Log::info("Setting is_partial flag for HAWB: {$waybill->hawb_number} (update)");
                } else {
                    Log::info("Setting is_partial flag for MAWB: {$waybill->awb_number} (update)");
                }

                // Calculate remaining pieces and weight
                $remainingPieces = $waybill->total_pieces - $data['pieces_received'];
                $remainingWeight = $waybill->total_weight - $data['weight_received'];

                // Check if there are existing partial waybills for this AWB
                if ($isHouse) {
                    $existingPartials = DB::table('partial_waybills')
                        ->where('house_awb_number', $waybill->hawb_number)
                        ->where('manifest_id', $checkinRecord->manifest_id)
                        ->orderBy('split_sequence', 'desc')
                        ->get();
                } else {
                    $existingPartials = DB::table('partial_waybills')
                        ->where('master_awb_number', $waybill->awb_number)
                        ->where('manifest_id', $checkinRecord->manifest_id)
                        ->orderBy('split_sequence', 'desc')
                        ->get();
                }

                if ($existingPartials->count() > 0) {
                    // Get the partial waybill associated with this check-in record
                    // We'll use the first one if we can't determine which one is associated
                    $partialWaybill = $existingPartials->first();

                    // Update the existing partial waybill record
                    DB::table('partial_waybills')
                        ->where('partial_id', $partialWaybill->partial_id)
                        ->update([
                            'received_pieces' => $data['pieces_received'],
                            'received_weight' => $data['weight_received'],
                            'remaining_pieces' => $remainingPieces,
                            'remaining_weight' => $remainingWeight,
                            'storage_location' => $data['storage_location'] ?? $partialWaybill->storage_location,
                            'updated_by' => Auth::id(),
                            'updated_at' => now()
                        ]);

                    Log::info("Partial waybill record updated with ID: {$partialWaybill->partial_id}");
                } else {
                    // Create a new partial waybill record
                    // Create a deterministic partial ID based on AWB/HAWB number and sequence
                    // Generate sequential partial ID consistent with XML parser
                    if ($isHouse) {
                        $partialId = $this->generateSequentialPartialId($waybill->hawb_number, true);
                        Log::info("Created partial ID: {$partialId} for house AWB: {$waybill->hawb_number} (update)");
                    } else {
                        $partialId = $this->generateSequentialPartialId($waybill->awb_number, false);
                        Log::info("Created partial ID: {$partialId} for master AWB: {$waybill->awb_number} (update)");
                    }

                    // Extract sequence number for database operations
                    $splitSequence = (int) substr($partialId, strrpos($partialId, '-') + 1);

                    // Check if this partial ID already exists
                    $existingPartial = DB::table('partial_waybills')
                        ->where('partial_id', $partialId)
                        ->first();

                    if (!$existingPartial) {
                        if ($isHouse) {
                            // Create the partial waybill record for house waybill
                            DB::statement("CALL create_partial_waybill(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                                $partialId,
                                null, // master_awb_number (null for house waybills)
                                $checkinRecord->manifest_id,
                                $waybill->origin_airport,
                                $waybill->destination_airport,
                                $waybill->total_pieces,
                                $waybill->total_weight,
                                $data['pieces_received'],
                                $data['weight_received'],
                                $remainingPieces,
                                $remainingWeight,
                                'P',
                                1, // split_sequence
                                'RECEIVED',
                                $data['storage_location'] ?? null,
                                $data['branch_id'],
                                Auth::id(),
                                Auth::id()
                            ]);

                            // Update the house_awb_number field
                            DB::table('partial_waybills')
                                ->where('partial_id', $partialId)
                                ->update([
                                    'house_awb_number' => $waybill->hawb_number,
                                    'master_awb_number' => $waybill->mawb_number
                                ]);
                        } else {
                            // Create the partial waybill record for master waybill
                            DB::statement("CALL create_partial_waybill(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                                $partialId,
                                $waybill->awb_number,
                                $checkinRecord->manifest_id,
                                $waybill->origin_airport,
                                $waybill->destination_airport,
                                $waybill->total_pieces,
                                $waybill->total_weight,
                                $data['pieces_received'],
                                $data['weight_received'],
                                $remainingPieces,
                                $remainingWeight,
                                'P',
                                1, // split_sequence
                                'RECEIVED',
                                $data['storage_location'] ?? null,
                                $data['branch_id'],
                                Auth::id(),
                                Auth::id()
                            ]);
                        }

                        Log::info("Partial waybill record created with ID: {$partialId}");
                    } else {
                        // Update the existing partial waybill record
                        DB::table('partial_waybills')
                            ->where('partial_id', $partialId)
                            ->update([
                                'received_pieces' => $data['pieces_received'],
                                'received_weight' => $data['weight_received'],
                                'remaining_pieces' => $remainingPieces,
                                'remaining_weight' => $remainingWeight,
                                'storage_location' => $data['storage_location'] ?? $existingPartial->storage_location,
                                'updated_by' => Auth::id(),
                                'updated_at' => now()
                            ]);

                        Log::info("Partial waybill record updated with ID: {$partialId}");
                    }
                }

                // Check if all pieces have been received
                if ($isHouse) {
                    $totalReceivedPieces = DB::table('partial_waybills')
                        ->where('house_awb_number', $waybill->hawb_number)
                        ->where('manifest_id', $checkinRecord->manifest_id)
                        ->sum('received_pieces');

                    if ($totalReceivedPieces >= $waybill->total_pieces) {
                        // Update all partial waybills for this HAWB to mark them as reconciled
                        DB::table('partial_waybills')
                            ->where('house_awb_number', $waybill->hawb_number)
                            ->where('manifest_id', $checkinRecord->manifest_id)
                            ->update([
                                'status' => 'RECONCILED',
                                'reconciled_at' => now(),
                                'updated_at' => now(),
                                'updated_by' => Auth::id()
                            ]);

                        Log::info("All partial waybills for HAWB {$waybill->hawb_number} marked as RECONCILED");
                    }
                } else {
                    $totalReceivedPieces = DB::table('partial_waybills')
                        ->where('master_awb_number', $waybill->awb_number)
                        ->where('manifest_id', $checkinRecord->manifest_id)
                        ->sum('received_pieces');

                    if ($totalReceivedPieces >= $waybill->total_pieces) {
                        // Update all partial waybills for this MAWB to mark them as reconciled
                        DB::table('partial_waybills')
                            ->where('master_awb_number', $waybill->awb_number)
                            ->where('manifest_id', $checkinRecord->manifest_id)
                            ->update([
                                'status' => 'RECONCILED',
                                'reconciled_at' => now(),
                                'updated_at' => now(),
                                'updated_by' => Auth::id()
                            ]);

                        Log::info("All partial waybills for MAWB {$waybill->awb_number} marked as RECONCILED");
                    }
                }

                // If this is a master waybill, also update any associated house waybills
                if ($waybill->type_code === '741') {
                    // Check if the house_waybills table exists and has the master_awb_number column
                    if (Schema::hasTable('house_waybills') && Schema::hasColumn('house_waybills', 'master_awb_number')) {
                        DB::table('house_waybills')
                            ->where('master_awb_number', $waybill->awb_number)
                            ->update([
                                'is_partial' => true,
                                'updated_at' => now(),
                                'updated_by' => Auth::id()
                            ]);
                        Log::info("Updated is_partial flag for house waybills under master {$waybill->awb_number} (update)");
                    } else if (Schema::hasTable('house_waybills') && Schema::hasColumn('house_waybills', 'mawb_number')) {
                        // Try with mawb_number column instead
                        DB::table('house_waybills')
                            ->where('mawb_number', $waybill->awb_number)
                            ->update([
                                'is_partial' => true,
                                'updated_at' => now(),
                                'updated_by' => Auth::id()
                            ]);
                        Log::info("Updated is_partial flag for house waybills under master {$waybill->awb_number} using mawb_number column (update)");
                    } else {
                        Log::warning("house_waybills table or master_awb_number/mawb_number column does not exist. Skipping update for house waybills (update).");
                    }
                }
            }

            // Clear cache
            $this->clearCache($checkinRecord->manifest_id);

            DB::commit();

            return $checkinRecord;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate Inward Flight Report (IFR)
     */
    public function generateIFR($manifestId)
    {
        $manifest = FlightManifestModel::where('manifest_id', $manifestId)->first();

        if (!$manifest) {
            return null;
        }

        // Get all AWBs for the manifest
        $awbs = MasterWaybill::where('manifest_id', $manifestId)->get();

        // Get checkin records for the AWBs with their cargo states
        $checkinRecords = CheckinRecord::with('cargoStates')
            ->where('manifest_id', $manifestId)
            ->get()
            ->keyBy('waybill_id');

        // Calculate totals
        $totalPieces = 0;
        $totalWeight = 0;
        $totalPiecesReceived = 0;
        $totalWeightReceived = 0;
        $totalDamagedPieces = 0;
        $totalDamagedWeight = 0;
        $totalMissingPieces = 0;
        $totalMissingWeight = 0;
        $totalExcessPieces = 0;
        $totalExcessWeight = 0;

        // Process each AWB
        foreach ($awbs as $awb) {
            $totalPieces += $awb->total_pieces;
            $totalWeight += $awb->total_weight;

            if (isset($checkinRecords[$awb->awb_number])) {
                $record = $checkinRecords[$awb->awb_number];
                $totalPiecesReceived += $record->pieces_received;
                $totalWeightReceived += $record->weight_received;

                // Calculate missing pieces and weight
                $missingPieces = max(0, $awb->total_pieces - $record->pieces_received);
                $missingWeight = max(0, $awb->total_weight - $record->weight_received);
                $totalMissingPieces += $missingPieces;
                $totalMissingWeight += $missingWeight;

                // Process cargo states for damaged and excess
                if ($record->cargoStates && $record->cargoStates->count() > 0) {
                    foreach ($record->cargoStates as $state) {
                        // Damaged cargo
                        if (in_array($state->state_id, ['DAMAGED', 'DISC', 'DMG', 'WET', 'TORN'])) {
                            $totalDamagedPieces += $state->pieces;
                            $totalDamagedWeight += $state->weight;
                        }

                        // Excess cargo
                        if (in_array($state->state_id, ['FDCA', 'FDAW'])) {
                            $totalExcessPieces += $state->pieces;
                            $totalExcessWeight += $state->weight;
                        }
                    }
                } else if ($record->is_damaged) {
                    // If no cargo states but is_damaged is true
                    $totalDamagedPieces += $record->pieces_received;
                    $totalDamagedWeight += $record->weight_received;
                }
            } else {
                // If no check-in record, all pieces are missing
                $totalMissingPieces += $awb->total_pieces;
                $totalMissingWeight += $awb->total_weight;
            }
        }

        // Get excess documents from the new excess_documents table
        $excessDocuments = ExcessDocument::where('manifest_id', $manifestId)->get();

        foreach ($excessDocuments as $excessDoc) {
            $totalExcessPieces += $excessDoc->pieces;
            $totalExcessWeight += $excessDoc->weight ?? 0;
        }

        return [
            'manifest' => $manifest,
            'awbs' => $awbs,
            'checkin_records' => $checkinRecords,
            'excess_documents' => $excessDocuments,
            'total_pieces' => $totalPieces,
            'total_weight' => $totalWeight,
            'total_pieces_received' => $totalPiecesReceived,
            'total_weight_received' => $totalWeightReceived,
            'total_damaged_pieces' => $totalDamagedPieces,
            'total_damaged_weight' => $totalDamagedWeight,
            'total_missing_pieces' => $totalMissingPieces,
            'total_missing_weight' => $totalMissingWeight,
            'total_excess_pieces' => $totalExcessPieces,
            'total_excess_weight' => $totalExcessWeight,
        ];
    }

    /**
     * Update ULD status based on checked-in AWBs
     */
    public function updateUldStatus($uldId, $manifestId)
    {
        // Get all AWBs for this ULD
        $awbs = DB::table('uld_awbs')
            ->where('uld_id', $uldId)
            ->where('manifest_id', $manifestId)
            ->get();

        if ($awbs->isEmpty()) {
            return;
        }

        // Get all checked-in AWBs for this ULD
        $checkedInAwbs = DB::table('checkin_records')
            ->whereIn('waybill_id', $awbs->pluck('awb_number'))
            ->where('manifest_id', $manifestId)
            ->get();

        // If all AWBs are checked in, update ULD status to COMPLETE
        if ($checkedInAwbs->count() === $awbs->count()) {
            DB::table('uld_details')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $manifestId)
                ->update(['status' => 'COMPLETE']);
        } else if ($checkedInAwbs->count() > 0) {
            // If some AWBs are checked in, update ULD status to PARTIAL
            DB::table('uld_details')
                ->where('uld_id', $uldId)
                ->where('manifest_id', $manifestId)
                ->update(['status' => 'PARTIAL']);
        }
    }

    /**
     * Update BULK status based on checked-in AWBs
     */
    public function updateBulkStatus($manifestId)
    {
        // Get all BULK AWBs (not in ULDs)
        $uldAwbNumbers = DB::table('uld_awbs')
            ->where('manifest_id', $manifestId)
            ->pluck('awb_number')
            ->toArray();

        $bulkAwbs = MasterWaybill::where('manifest_id', $manifestId)
            ->whereNotIn('awb_number', $uldAwbNumbers)
            ->get();

        if ($bulkAwbs->isEmpty()) {
            return;
        }

        // Get all checked-in BULK AWBs
        $checkedInBulkAwbs = DB::table('checkin_records')
            ->whereIn('waybill_id', $bulkAwbs->pluck('awb_number'))
            ->where('manifest_id', $manifestId)
            ->get();

        // If all BULK AWBs are checked in, update BULK status to COMPLETE
        if ($checkedInBulkAwbs->count() === $bulkAwbs->count()) {
            // Update BULK status in flight_manifests table
            DB::table('flight_manifests')
                ->where('manifest_id', $manifestId)
                ->update(['bulk_status' => 'COMPLETE']);
        } else if ($checkedInBulkAwbs->count() > 0) {
            // If some BULK AWBs are checked in, update BULK status to PARTIAL
            DB::table('flight_manifests')
                ->where('manifest_id', $manifestId)
                ->update(['bulk_status' => 'PARTIAL']);
        }
    }

    /**
     * Clear cache for a manifest
     */
    public function clearCache($manifestId = null)
    {
        if ($manifestId) {
            Cache::forget(static::$cachePrefix . '.ulds.' . $manifestId);
            Cache::forget(static::$cachePrefix . '.bulk_awbs.' . $manifestId);

            // Clear ULD AWBs cache
            $ulds = DB::table('uld_details')
                ->where('manifest_id', $manifestId)
                ->get();

            foreach ($ulds as $uld) {
                Cache::forget(static::$cachePrefix . '.uld_awbs.' . $uld->uld_id . '.' . $manifestId);
            }
        } else {
            // Clear all cache keys that start with the prefix
            $keys = Cache::get(static::$cachePrefix . '.keys', []);

            foreach ($keys as $key) {
                Cache::forget($key);
            }

            Cache::forget(static::$cachePrefix . '.keys');
        }
    }

    /**
     * Generate sequential partial ID consistent with XML parser.
     * Format: PWB-{awbNumber}-{sequenceNumber} for master AWBs
     * Format: PHWB-{hawbNumber}-{sequenceNumber} for house AWBs
     */
    private function generateSequentialPartialId(string $awbNumber, bool $isHouse = false): string
    {
        $prefix = $isHouse ? 'PHWB' : 'PWB';

        // Get the highest existing sequence number for this AWB across all manifests
        $existingPartials = DB::table('partial_waybills')
            ->where('partial_id', 'LIKE', "{$prefix}-{$awbNumber}-%")
            ->orderBy('partial_id', 'desc')
            ->first();

        $nextSequence = 1;
        if ($existingPartials && $existingPartials->partial_id) {
            // Extract sequence number from existing partial ID
            $parts = explode('-', $existingPartials->partial_id);
            if (count($parts) >= 3) {
                $lastSequence = (int) end($parts);
                $nextSequence = $lastSequence + 1;
            }
        }

        return "{$prefix}-{$awbNumber}-{$nextSequence}";
    }
}
