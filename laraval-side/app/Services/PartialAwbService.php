<?php

namespace App\Services;

use App\Models\MasterWaybill;
use App\Models\HouseWaybill;
use App\Models\PartialWaybill;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * Service for managing partial AWB operations during check-in process.
 *
 * Handles automatic partial detection when checked-in pieces are less than
 * total pieces, and manages existing partial AWB check-ins.
 */
class PartialAwbService
{
    /**
     * Detect and handle partial AWB during check-in process.
     *
     * @param string $awbNumber AWB number being checked in
     * @param int $checkedInPieces Number of pieces being checked in
     * @param float $checkedInWeight Weight being checked in
     * @param int $userId User performing the check-in
     * @param bool $isHouse Whether this is a house waybill
     * @return array Result of partial detection and handling
     */
    public function handleCheckinPartialDetection(
        string $awbNumber,
        int $checkedInPieces,
        float $checkedInWeight,
        int $userId,
        bool $isHouse = false
    ): array {
        $result = [
            'partial_created' => false,
            'partial_updated' => false,
            'partial_id' => null,
            'action_taken' => 'NONE',
            'errors' => [],
            'warnings' => []
        ];

        try {
            DB::beginTransaction();

            // Find the AWB record
            $awbRecord = $this->findAwbRecord($awbNumber, $isHouse);
            if (!$awbRecord) {
                $result['errors'][] = "AWB {$awbNumber} not found";
                return $result;
            }

            // Validate checked-in pieces don't exceed total
            if ($checkedInPieces > $awbRecord->total_pieces) {
                $result['errors'][] = "Checked-in pieces ({$checkedInPieces}) cannot exceed total pieces ({$awbRecord->total_pieces})";
                return $result;
            }

            // Handle based on whether AWB is already marked as partial
            if (!$awbRecord->is_partial) {
                // Non-partial AWB - check if we need to create partial
                $result = $this->handleNonPartialAwbCheckin(
                    $awbRecord,
                    $checkedInPieces,
                    $checkedInWeight,
                    $userId,
                    $isHouse
                );
            } else {
                // Already partial AWB - update existing partials
                $result = $this->handlePartialAwbCheckin(
                    $awbRecord,
                    $checkedInPieces,
                    $checkedInWeight,
                    $userId,
                    $isHouse
                );
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error in partial AWB check-in detection: " . $e->getMessage());
            $result['errors'][] = "System error during partial detection: " . $e->getMessage();
        }

        return $result;
    }

    /**
     * Handle check-in for non-partial AWBs.
     */
    private function handleNonPartialAwbCheckin(
        $awbRecord,
        int $checkedInPieces,
        float $checkedInWeight,
        int $userId,
        bool $isHouse
    ): array {
        $result = [
            'partial_created' => false,
            'partial_updated' => false,
            'partial_id' => null,
            'action_taken' => 'NONE',
            'errors' => [],
            'warnings' => []
        ];

        // Check if checked-in pieces are less than total pieces
        if ($checkedInPieces < $awbRecord->total_pieces) {
            // Create partial waybill for remaining expected pieces
            $remainingPieces = $awbRecord->total_pieces - $checkedInPieces;
            $remainingWeight = $awbRecord->total_weight - $checkedInWeight;

            $partialId = $this->createPartialFromCheckin(
                $awbRecord,
                $checkedInPieces,
                $checkedInWeight,
                $remainingPieces,
                $remainingWeight,
                $userId,
                $isHouse
            );

            if ($partialId) {
                // Update AWB to mark as partial and add received_pieces field
                $this->updateAwbForPartialCheckin($awbRecord, $checkedInPieces, $checkedInWeight, $userId, $isHouse);

                $result['partial_created'] = true;
                $result['partial_id'] = $partialId;
                $result['action_taken'] = 'CREATE_PARTIAL';

                Log::info("Created partial waybill {$partialId} for AWB {$awbRecord->awb_number} during check-in");
            } else {
                $result['errors'][] = "Failed to create partial waybill";
            }
        } else {
            // Full check-in - just update received pieces
            $this->updateAwbReceivedPieces($awbRecord, $checkedInPieces, $checkedInWeight, $userId, $isHouse);
            $result['action_taken'] = 'FULL_CHECKIN';
        }

        return $result;
    }

    /**
     * Handle check-in for already partial AWBs.
     */
    private function handlePartialAwbCheckin(
        $awbRecord,
        int $checkedInPieces,
        float $checkedInWeight,
        int $userId,
        bool $isHouse
    ): array {
        $result = [
            'partial_created' => false,
            'partial_updated' => false,
            'partial_id' => null,
            'action_taken' => 'NONE',
            'errors' => [],
            'warnings' => []
        ];

        // Get existing partial waybills for this AWB
        $partials = $this->getExistingPartials($awbRecord, $isHouse);

        // Calculate total remaining expected pieces
        $totalRemainingExpected = $partials->where('status', 'PENDING')->sum('expected_pieces');

        // Validate check-in doesn't exceed remaining expected
        if ($checkedInPieces > $totalRemainingExpected) {
            $result['errors'][] = "Checked-in pieces ({$checkedInPieces}) exceed remaining expected pieces ({$totalRemainingExpected})";
            return $result;
        }

        // Update partial waybills with received pieces
        $remainingToAllocate = $checkedInPieces;
        $remainingWeightToAllocate = $checkedInWeight;

        foreach ($partials->where('status', 'PENDING') as $partial) {
            if ($remainingToAllocate <= 0) break;

            $piecesToAllocate = min($remainingToAllocate, $partial->expected_pieces - $partial->received_pieces);
            $weightToAllocate = min($remainingWeightToAllocate, $partial->expected_weight - $partial->received_weight);

            if ($piecesToAllocate > 0) {
                $this->updatePartialReceived($partial, $piecesToAllocate, $weightToAllocate, $userId);
                $remainingToAllocate -= $piecesToAllocate;
                $remainingWeightToAllocate -= $weightToAllocate;

                $result['partial_updated'] = true;
                $result['partial_id'] = $partial->partial_id;
            }
        }

        // Update AWB received pieces
        $currentReceived = $awbRecord->received_pieces ?? 0;
        $newReceivedPieces = $currentReceived + $checkedInPieces;
        $this->updateAwbReceivedPieces($awbRecord, $newReceivedPieces, $checkedInWeight, $userId, $isHouse);

        $result['action_taken'] = 'UPDATE_PARTIALS';

        return $result;
    }

    /**
     * Generate sequential partial ID consistent with XML parser.
     * Format: PWB-{awbNumber}-{sequenceNumber}
     */
    private function generateSequentialPartialId(string $awbNumber, bool $isHouse = false): string
    {
        // Get the highest existing sequence number for this AWB across all manifests
        $query = PartialWaybill::where(function ($q) use ($awbNumber, $isHouse) {
            if ($isHouse) {
                $q->where('house_awb_number', $awbNumber);
            } else {
                $q->where('master_awb_number', $awbNumber);
            }
        })
        ->where('partial_id', 'LIKE', "PWB-{$awbNumber}-%")
        ->orderBy('partial_id', 'desc')
        ->first();

        $nextSequence = 1;
        if ($query && $query->partial_id) {
            // Extract sequence number from existing partial ID
            $parts = explode('-', $query->partial_id);
            if (count($parts) >= 3) {
                $lastSequence = (int) end($parts);
                $nextSequence = $lastSequence + 1;
            }
        }

        return "PWB-{$awbNumber}-{$nextSequence}";
    }

    /**
     * Create partial waybill from check-in process.
     */
    private function createPartialFromCheckin(
        $awbRecord,
        int $receivedPieces,
        float $receivedWeight,
        int $remainingPieces,
        float $remainingWeight,
        int $userId,
        bool $isHouse
    ): ?string {
        try {
            // Generate sequential partial ID consistent with XML parser
            $awbNumber = $isHouse ? $awbRecord->hawb_number : $awbRecord->awb_number;
            $partialId = $this->generateSequentialPartialId($awbNumber, $isHouse);

            $partialData = [
                'partial_id' => $partialId,
                'master_awb_number' => $isHouse ? null : $awbRecord->awb_number,
                'house_awb_number' => $isHouse ? $awbRecord->hawb_number : null,
                'manifest_id' => $awbRecord->manifest_id,
                'total_pieces_in_awb' => $awbRecord->total_pieces,
                'total_weight_in_awb' => $awbRecord->total_weight,
                'weight_unit' => $awbRecord->weight_unit ?? 'KGM',
                'origin_airport' => $awbRecord->origin_airport,
                'destination_airport' => $awbRecord->destination_airport,
                'expected_pieces' => $remainingPieces,
                'expected_weight' => $remainingWeight,
                'received_pieces' => 0, // Not yet received
                'received_weight' => 0.0,
                'remaining_pieces' => $remainingPieces,
                'remaining_weight' => $remainingWeight,
                'split_code' => 'P',
                'split_sequence' => 1,
                'status' => 'PENDING',
                'source' => 'CHECK_IN',
                'mra_success' => $awbRecord->mra_success,
                'special_handling_code' => $awbRecord->special_handling_code,
                'notes' => "Created during check-in process - remaining pieces after partial check-in",
                'branch_id' => auth()->user()->branch_id ?? 1,
                'created_by' => $userId,
                'updated_by' => $userId,
            ];

            $partial = PartialWaybill::create($partialData);

            return $partial->partial_id;

        } catch (\Exception $e) {
            Log::error("Error creating partial from check-in: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update AWB for partial check-in.
     */
    private function updateAwbForPartialCheckin($awbRecord, int $receivedPieces, float $receivedWeight, int $userId, bool $isHouse): void
    {
        $updateData = [
            'is_partial' => true,
            'received_pieces' => $receivedPieces,
            'received_weight' => $receivedWeight,
            'updated_by' => $userId,
        ];

        // Add check-in audit trail to xml_data
        $xmlData = is_string($awbRecord->xml_data) ? json_decode($awbRecord->xml_data, true) : ($awbRecord->xml_data ?? []);
        $xmlData['partial_info'] = [
            'check_in_date' => Carbon::now()->toISOString(),
            'received_pieces' => $receivedPieces,
            'remaining_pieces' => $awbRecord->total_pieces - $receivedPieces,
            'created_by_check_in' => true
        ];
        $updateData['xml_data'] = json_encode($xmlData);

        $awbRecord->update($updateData);
    }

    /**
     * Update AWB received pieces.
     */
    private function updateAwbReceivedPieces($awbRecord, int $receivedPieces, float $receivedWeight, int $userId, bool $isHouse): void
    {
        $awbRecord->update([
            'received_pieces' => $receivedPieces,
            'received_weight' => $receivedWeight,
            'updated_by' => $userId,
        ]);
    }

    /**
     * Update partial waybill received quantities.
     */
    private function updatePartialReceived(PartialWaybill $partial, int $receivedPieces, float $receivedWeight, int $userId): void
    {
        $newReceivedPieces = $partial->received_pieces + $receivedPieces;
        $newReceivedWeight = $partial->received_weight + $receivedWeight;

        $updateData = [
            'received_pieces' => $newReceivedPieces,
            'received_weight' => $newReceivedWeight,
            'remaining_pieces' => $partial->expected_pieces - $newReceivedPieces,
            'remaining_weight' => $partial->expected_weight - $newReceivedWeight,
            'updated_by' => $userId,
        ];

        // Mark as received if fully received
        if ($newReceivedPieces >= $partial->expected_pieces) {
            $updateData['status'] = 'RECEIVED';
        }

        $partial->update($updateData);
    }

    /**
     * Find AWB record (master or house).
     */
    private function findAwbRecord(string $awbNumber, bool $isHouse)
    {
        if ($isHouse) {
            return HouseWaybill::where('hawb_number', $awbNumber)->first();
        } else {
            return MasterWaybill::where('awb_number', $awbNumber)->first();
        }
    }

    /**
     * Get existing partial waybills for an AWB.
     */
    private function getExistingPartials($awbRecord, bool $isHouse)
    {
        if ($isHouse) {
            return PartialWaybill::where('house_awb_number', $awbRecord->hawb_number)->get();
        } else {
            return PartialWaybill::where('master_awb_number', $awbRecord->awb_number)->get();
        }
    }

    /**
     * Get partial AWB summary for display.
     */
    public function getPartialSummary(string $awbNumber, bool $isHouse = false): array
    {
        $awbRecord = $this->findAwbRecord($awbNumber, $isHouse);
        if (!$awbRecord) {
            return ['error' => 'AWB not found'];
        }

        $partials = $this->getExistingPartials($awbRecord, $isHouse);

        return [
            'awb_number' => $awbNumber,
            'is_partial' => $awbRecord->is_partial,
            'total_pieces' => $awbRecord->total_pieces,
            'total_weight' => $awbRecord->total_weight,
            'received_pieces' => $awbRecord->received_pieces ?? 0,
            'received_weight' => $awbRecord->received_weight ?? 0.0,
            'partials' => $partials->map(function ($partial) {
                return [
                    'partial_id' => $partial->partial_id,
                    'expected_pieces' => $partial->expected_pieces,
                    'expected_weight' => $partial->expected_weight,
                    'received_pieces' => $partial->received_pieces,
                    'received_weight' => $partial->received_weight,
                    'status' => $partial->status,
                    'source' => $partial->source,
                    'created_at' => $partial->created_at,
                ];
            })->toArray()
        ];
    }
}
