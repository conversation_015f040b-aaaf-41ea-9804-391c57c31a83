@extends('layouts.master')

@section('title')
    @if(isset($uldType) && $uldType === 'BULK')
        BULK Cargo Check-in
    @elseif(isset($uldType) && isset($uldNumber))
        {{ $uldType }} {{ $uldNumber }} Check-in
    @else
        AWB Check-in
    @endif
@endsection

@section('css')
    <!-- DataTables -->
    <link href="{{ URL::asset('build/libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('build/libs/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Select2 -->
    <link href="{{ URL::asset('build/libs/select2/css/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('build/libs/select2-bootstrap5-theme/select2-bootstrap-5-theme.min.css') }}" rel="stylesheet" type="text/css" />

    <style>
        .royal-flight-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .checkin-form {
            display: none;
        }
        /* Select2 custom styles */
        .select2-container--bootstrap-5 .select2-selection {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.25rem;
        }
        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px);
        }
        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: calc(1.5em + 0.75rem + 2px);
        }
        /* DataTables alternating row colors */
        #mawbs-table tbody tr.odd,
        .hawbs-subtable tbody tr.odd {
            background-color: rgba(0, 0, 0, 0.05);
        }
        #mawbs-table tbody tr.even,
        .hawbs-subtable tbody tr.even {
            background-color: #ffffff;
        }
    </style>
@endsection

@section('content')
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">
                    @if($uldType === 'BULK')
                        BULK Cargo Check-in
                    @else
                        {{ $uldType }} {{ $uldNumber }} Check-in
                    @endif
                </h4>

                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">Operations</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('checkin.index') }}">Flight Check-in</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('checkin.uld-list', $manifest->manifest_id) }}">ULD Check-in</a></li>
                        <li class="breadcrumb-item active">
                            @if($uldType === 'BULK')
                                BULK Cargo Check-in
                            @else
                                {{ $uldType }} {{ $uldNumber }} Check-in
                            @endif
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title mb-0">
                                Flight &
                                @if($uldType === 'BULK')
                                    BULK Cargo
                                @else
                                    ULD
                                @endif
                                Details
                            </h4>
                        </div>
                        <div>
                            <a href="{{ route('checkin.uld-list', $manifest->manifest_id) }}" class="btn btn-secondary waves-effect me-2">
                                <i class="bx bx-arrow-back me-1"></i>
                                @if($uldType === 'BULK')
                                    Back to ULD List
                                @else
                                    Back to ULDs
                                @endif
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body border-top">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">Flight Number</label>
                                <p class="fw-semibold">{{ $manifest->flight_number }}</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">Route</label>
                                <p class="fw-semibold">{{ $manifest->departure_airport }} → {{ $manifest->arrival_airport }}</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">Flight Date</label>
                                <p class="fw-semibold">{{ date('Y-m-d', strtotime($manifest->actual_arrival)) }}</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">Flight Type</label>
                                <p class="fw-semibold">{{ $manifest->flight_type }}</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    @if($uldType === 'BULK')
                                        BULK Details
                                    @else
                                        ULD Details
                                    @endif
                                </label>
                                <p class="fw-semibold">
                                    @if($uldType === 'BULK')
                                        BULK Cargo
                                    @else
                                        {{ $uldType }} {{ $uldNumber }}
                                        @if(isset($uldStatus))
                                            @if($uldStatus == 'COMPLETE')
                                                <span class="badge bg-success ms-2">Completed</span>
                                            @elseif($uldStatus == 'PARTIAL' || (isset($hasPartialAwbs) && $hasPartialAwbs))
                                                <span class="badge bg-warning ms-2">Partial</span>
                                            @elseif($uldStatus == 'MISSING')
                                                <span class="badge bg-danger ms-2">Missing</span>
                                            @else
                                                <span class="badge bg-secondary ms-2">Pending</span>
                                            @endif
                                        @endif
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label text-muted">AWB Count</label>
                                <p class="fw-semibold">
                                    @php
                                        // Calculate AWB count from the existing arrays
                                        $masterAwbCount = isset($awbs) ? count($awbs) : 0;
                                        $houseAwbCount = isset($houseAwbs) ? count($houseAwbs) : 0;
                                        $totalAwbCount = $masterAwbCount + $houseAwbCount;
                                    @endphp
                                    {{ $totalAwbCount }} ({{ $masterAwbCount }} Master, {{ $houseAwbCount }} House)
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AWBs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            @if($uldType === 'BULK')
                                <h4 class="card-title">BULK Cargo AWBs for Check-in</h4>
                                <p class="card-title-desc">Check-in cargo that is not assigned to any ULD</p>
                            @else
                                <h4 class="card-title">AWBs for {{ $uldType }} {{ $uldNumber }} Check-in</h4>
                                <p class="card-title-desc">Check-in cargo for this specific ULD only</p>
                            @endif
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#excessDocumentModal">
                                <i class="bx bx-file me-1"></i> Add Excess Document
                            </button>
                            @if($uldType != 'BULK' && !empty($uldId))
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" id="mark-uld-complete-btn">
                                    <i class="bx bx-check-double me-1"></i> Mark ULD as Complete
                                </button>
                                <button type="button" class="btn btn-warning" id="mark-uld-partial-btn">
                                    <i class="bx bx-check me-1"></i> Mark ULD as Partial
                                </button>
                                <button type="button" class="btn btn-secondary" id="mark-uld-pending-btn">
                                    <i class="bx bx-reset me-1"></i> Reset ULD Status
                                </button>
                            </div>
                            @endif
                        </div>
                    </div>
                    @if($manifest->flight_type == 'ROYAL')
                        <div class="alert alert-danger mb-4">
                            <i class="bx bx-error-circle me-2"></i>
                            This is a <strong>ROYAL FLIGHT</strong>. Please ensure all cargo is handled with priority and according to royal flight procedures.
                        </div>
                    @endif

                    <!-- Nav tabs for Master and House AWBs -->
                    <ul class="nav nav-tabs nav-tabs-custom mb-3" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#master-awbs" role="tab">
                                <span class="d-none d-sm-block">Master AWBs</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#house-awbs" role="tab">
                                <span class="d-none d-sm-block">House AWBs</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content p-3 text-muted">
                        <!-- Master AWBs Tab -->
                        <div class="tab-pane active" id="master-awbs" role="tabpanel">
                            <div class="table-responsive">
                                <table id="mawbs-table" class="table table-bordered dataTable dt-responsive nowrap w-100">
                                    <thead>
                                        <tr>
                                            <th>AWB Number</th>
                                            <th>Type</th>
                                            <th>Route</th>
                                            <th>Expected Pieces</th>
                                            <th>Expected Weight</th>
                                            <th>Overall Status</th>
                                            <th>ULD Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(count($awbs) === 0)
                                            <tr>
                                                <td colspan="9" class="text-center">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="bx bx-info-circle me-2"></i>
                                                        @if($uldType === 'BULK')
                                                            No BULK cargo AWBs found for this flight manifest.
                                                        @else
                                                            No AWBs assigned to ULD {{ $uldType }} {{ $uldNumber }} for this flight manifest.
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @else
                                            @php
                                                // Track AWB numbers to avoid duplicates
                                                $displayedAwbNumbers = [];
                                            @endphp
                                            @foreach($awbs as $awb)
                                                @if($awb->waybill_type == 'MASTER' && !in_array($awb->awb_number, $displayedAwbNumbers))
                                                    @php
                                                        // Add this AWB number to the list of displayed AWBs
                                                        $displayedAwbNumbers[] = $awb->awb_number;
                                                    @endphp
                                                    <tr>
                                                        <td>
                                                            {{ $awb->awb_number ?? 'N/A' }}
                                                            @if($awb->is_partial)
                                                                <span class="badge bg-warning ms-1" title="Partial Shipment">PARTIAL</span>
                                                            @endif
                                                            @if(isset($awb->checkin_record) && isset($awb->checkin_record->is_missing) && $awb->checkin_record->is_missing)
                                                                <span class="badge bg-danger ms-1" title="Completely Missing">MISSING</span>
                                                            @endif
                                                        </td>
                                                    <td>
                                                        @if($awb->type_code == '740')
                                                            <span class="badge bg-info" title="Master AWB">MAWB</span>
                                                        @elseif($awb->type_code == '741')
                                                            <span class="badge bg-primary" title="Direct AWB">Direct</span>
                                                        @else
                                                            {{ $awb->type_code }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $awb->origin_airport ?? 'N/A' }}-{{ $awb->destination_airport ?? 'N/A' }}</td>
                                                    <td>
                                                        {{ $awb->expected_pieces }}
                                                        @if($awb->is_partial && isset($awb->remaining_pieces))
                                                            <small class="d-block text-muted">Remaining: {{ $awb->remaining_pieces }}</small>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        {{ number_format($awb->expected_weight, 2) }} KG
                                                        @if($awb->is_partial && isset($awb->remaining_weight))
                                                            <small class="d-block text-muted">Remaining: {{ number_format($awb->remaining_weight, 2) }} KG</small>
                                                        @endif
                                                    </td>
                                                    <td>{!! $awb->status_badge !!}</td>
                                                    <td>
                                                        @php
                                                            // Check if AWB has been checked in
                                                            $isCheckedIn = isset($awb->is_checked_in) && $awb->is_checked_in;
                                                            $checkinRecord = isset($awb->checkin_record) ? $awb->checkin_record : null;
                                                            $isPartial = isset($awb->is_partial) && $awb->is_partial;
                                                            $isMissing = $checkinRecord && isset($checkinRecord->is_missing) && $checkinRecord->is_missing;

                                                            // Default status
                                                            $statusClass = 'secondary';
                                                            $displayUldStatus = 'PENDING';

                                                            // First, check if the ULD has a status
                                                            if (isset($uldStatus)) {
                                                                // If ULD has a status, use it for all AWBs
                                                                if ($uldStatus == 'COMPLETE') {
                                                                    $statusClass = 'success';
                                                                    $displayUldStatus = 'COMPLETE';
                                                                } else if ($uldStatus == 'PARTIAL') {
                                                                    $statusClass = 'warning';
                                                                    $displayUldStatus = 'PARTIAL';
                                                                } else if ($uldStatus == 'MISSING') {
                                                                    $statusClass = 'danger';
                                                                    $displayUldStatus = 'MISSING';
                                                                }
                                                            } else {
                                                                // If no ULD status, determine based on AWB status
                                                                if ($isCheckedIn) {
                                                                    if ($isMissing) {
                                                                        $statusClass = 'danger';
                                                                        $displayUldStatus = 'MISSING';
                                                                    } else if ($isPartial) {
                                                                        $statusClass = 'warning';
                                                                        $displayUldStatus = 'PARTIAL';
                                                                    } else {
                                                                        $statusClass = 'success';
                                                                        $displayUldStatus = 'COMPLETE';
                                                                    }
                                                                }
                                                            }
                                                        @endphp

                                                        <span class="badge bg-{{ $statusClass }}">{{ $displayUldStatus }}</span>

                                                        @if($isCheckedIn && $checkinRecord)
                                                            <small class="d-block mt-1">{{ $checkinRecord->pieces_received }}/{{ $awb->uld_expected_pieces ?? $awb->total_pieces }} pieces</small>
                                                            @if($checkinRecord->pieces_received < ($awb->uld_expected_pieces ?? $awb->total_pieces))
                                                                <small class="d-block mt-1 text-danger">Missing: {{ ($awb->uld_expected_pieces ?? $awb->total_pieces) - $checkinRecord->pieces_received }} pieces</small>
                                                            @elseif($checkinRecord->pieces_received > ($awb->uld_expected_pieces ?? $awb->total_pieces))
                                                                <small class="d-block mt-1 text-success">Excess: {{ $checkinRecord->pieces_received - ($awb->uld_expected_pieces ?? $awb->total_pieces) }} pieces</small>
                                                            @endif
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($isCheckedIn)
                                                            <div class="d-flex flex-column gap-2">
                                                                <button type="button" class="btn btn-info btn-sm checkin-btn"
                                                                    data-awb-id="{{ $awb->waybill_id }}"
                                                                    data-awb-pieces="{{ $awb->expected_pieces ?? 0 }}"
                                                                    data-awb-weight="{{ $awb->expected_weight ?? 0 }}">
                                                                    <i class="bx bx-edit me-1"></i> Edit
                                                                </button>

                                                                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('cargo_executive') || auth()->user()->hasRole('cargo_supervisor'))
                                                                <button type="button" class="btn btn-warning btn-sm reverse-btn" data-awb-id="{{ $awb->waybill_id }}" data-bs-toggle="modal" data-bs-target="#reverseCheckinModal" data-awb-number="{{ $awb->waybill_id }}">
                                                                    <i class="bx bx-undo me-1"></i> Reverse
                                                                </button>
                                                                @endif
                                                            </div>
                                                        @else
                                                            <div class="d-flex flex-column gap-2">
                                                                <button type="button" class="btn btn-primary btn-sm checkin-btn"
                                                                    data-awb-id="{{ $awb->waybill_id }}"
                                                                    data-awb-pieces="{{ $awb->expected_pieces ?? 0 }}"
                                                                    data-awb-weight="{{ $awb->expected_weight ?? 0 }}"
                                                                    data-total-awb-pieces="{{ $awb->expected_pieces ?? 0 }}"
                                                                    data-total-awb-weight="{{ $awb->expected_weight ?? 0 }}">
                                                                    <i class="bx bx-check-circle me-1"></i> Check-in
                                                                </button>
                                                                <button type="button" class="btn btn-danger btn-sm missing-btn" data-awb-id="{{ $awb->waybill_id }}">
                                                                    <i class="bx bx-x-circle me-1"></i> Missing
                                                                </button>
                                                            </div>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @endif
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- House AWBs Tab -->
                        <div class="tab-pane" id="house-awbs" role="tabpanel">
                            <div class="alert alert-info mb-3">
                                <i class="bx bx-info-circle me-2"></i>
                                <strong>Note:</strong> House waybills are automatically checked in when their associated Master waybill is checked in.
                                You only need to check in House waybills individually if they have Handling Instructions requirements.
                            </div>

                            @if(isset($houseAwbs) && count($houseAwbs) > 0)
                                <!-- Group HAWBs by Master AWB -->
                                @php
                                    // First, ensure we have unique HAWBs by waybill_id
                                    $uniqueHawbs = collect();
                                    $processedHawbIds = [];

                                    foreach ($houseAwbs as $hawb) {
                                        if (!in_array($hawb->waybill_id, $processedHawbIds)) {
                                            $uniqueHawbs->push($hawb);
                                            $processedHawbIds[] = $hawb->waybill_id;
                                        }
                                    }

                                    // Then group by master AWB number
                                    $groupedHawbs = $uniqueHawbs->groupBy('master_awb_number');
                                @endphp

                                @foreach($groupedHawbs as $masterAwbNumber => $hawbGroup)
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h5 class="mb-0">
                                                <i class="bx bx-package me-1"></i>
                                                Master AWB: <strong>{{ $masterAwbNumber }}</strong>
                                                <span class="badge bg-primary ms-2">{{ count($hawbGroup) }} House Waybills</span>
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered dataTable dt-responsive nowrap w-100 hawbs-subtable">
                                                    <thead>
                                                        <tr>
                                                            <th>HAWB Number</th>
                                                            <th>Route</th>
                                                            <th>Expected Pieces</th>
                                                            <th>Expected Weight</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($hawbGroup as $hawb)
                                                            <tr>
                                                                <td>
                                                                    {{ $hawb->hawb_number ?? 'N/A' }}
                                                                    @if(isset($hawb->is_partial) && $hawb->is_partial)
                                                                        <span class="badge bg-warning ms-1" title="Partial Shipment">PARTIAL</span>
                                                                    @endif
                                                                    @if(isset($hawb->checkin_record) && isset($hawb->checkin_record->is_missing) && $hawb->checkin_record->is_missing)
                                                                        <span class="badge bg-danger ms-1" title="Completely Missing">MISSING</span>
                                                                    @endif
                                                                </td>
                                                                <td>{{ $hawb->origin_airport ?? 'N/A' }}-{{ $hawb->destination_airport ?? 'N/A' }}</td>
                                                                <td>{{ $hawb->expected_pieces ?? 'N/A' }}</td>
                                                                <td>{{ number_format($hawb->expected_weight ?? 0, 2) }} KG</td>
                                                                <td>{!! $hawb->status_badge !!}</td>
                                                                <td>
                                                                    @if($hawb->is_checked_in)
                                                                        @php
                                                                            // Check if HAWB has been checked in
                                                                            $isCheckedIn = true;
                                                                            $checkinRecord = isset($hawb->checkin_record) ? $hawb->checkin_record : null;
                                                                            $isPartial = isset($hawb->is_partial) && $hawb->is_partial;
                                                                            $isMissing = $checkinRecord && isset($checkinRecord->is_missing) && $checkinRecord->is_missing;

                                                                            // Default status
                                                                            $hawbStatusClass = 'success';
                                                                            $hawbStatusText = 'COMPLETE';

                                                                            // First, check if the ULD has a status
                                                                            if (isset($uldStatus)) {
                                                                                // If ULD has a status, use it for all HAWBs
                                                                                if ($uldStatus == 'COMPLETE') {
                                                                                    $hawbStatusClass = 'success';
                                                                                    $hawbStatusText = 'COMPLETE';
                                                                                } else if ($uldStatus == 'PARTIAL') {
                                                                                    $hawbStatusClass = 'warning';
                                                                                    $hawbStatusText = 'PARTIAL';
                                                                                } else if ($uldStatus == 'MISSING') {
                                                                                    $hawbStatusClass = 'danger';
                                                                                    $hawbStatusText = 'MISSING';
                                                                                }
                                                                            } else {
                                                                                // If no ULD status, determine based on HAWB status
                                                                                if ($isMissing) {
                                                                                    $hawbStatusClass = 'danger';
                                                                                    $hawbStatusText = 'MISSING';
                                                                                } else if ($isPartial) {
                                                                                    $hawbStatusClass = 'warning';
                                                                                    $hawbStatusText = 'PARTIAL';
                                                                                }
                                                                            }
                                                                        @endphp
                                                                        <div class="d-flex flex-column gap-2">
                                                                            <span class="badge bg-{{ $hawbStatusClass }}">{{ $hawbStatusText }}</span>
                                                                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('cargo_executive') || auth()->user()->hasRole('cargo_supervisor'))
                                                                            <button type="button" class="btn btn-warning btn-sm reverse-btn" data-awb-id="{{ $hawb->waybill_id }}" data-bs-toggle="modal" data-bs-target="#reverseCheckinModal" data-awb-number="{{ $hawb->waybill_id }}">
                                                                                <i class="bx bx-undo me-1"></i> Reverse
                                                                            </button>
                                                                            @endif
                                                                        </div>
                                                                    @else
                                                                        <button type="button" class="btn btn-primary btn-sm checkin-btn"
                                                                            data-awb-id="{{ $hawb->waybill_id }}"
                                                                            data-awb-pieces="{{ $hawb->expected_pieces ?? 1 }}"
                                                                            data-awb-weight="{{ $hawb->expected_weight ?? 0 }}"
                                                                            data-total-awb-pieces="{{ $hawb->expected_pieces ?? 1 }}"
                                                                            data-total-awb-weight="{{ $hawb->expected_weight ?? 0 }}">
                                                                            <i class="bx bx-check-circle me-1"></i> Check-in
                                                                        </button>
                                                                        <button type="button" class="btn btn-danger btn-sm missing-btn" data-awb-id="{{ $hawb->waybill_id }}">
                                                                            <i class="bx bx-x-circle me-1"></i> Missing
                                                                        </button>
                                                                    @endif
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-info mb-0">
                                    <i class="bx bx-info-circle me-2"></i>
                                    @if($uldType === 'BULK')
                                        No House Air Waybills found in BULK cargo for this flight manifest.
                                    @else
                                        No House Air Waybills assigned to ULD {{ $uldType }} {{ $uldNumber }} for this flight manifest.
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <!-- Check-in Form (initially hidden) -->
    <div class="row checkin-form" id="checkin-form-container">
        <!-- This will be populated dynamically with the check-in form -->
    </div>

    <!-- Reverse Check-in Modal -->
    <div class="modal fade" id="reverseCheckinModal" tabindex="-1" aria-labelledby="reverseCheckinModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="reverseCheckinModalLabel">Reverse Check-in</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bx bx-error-circle me-2"></i>
                        <strong>Warning:</strong> Reversing check-in will reset the waybill status to PENDING and remove all check-in data. This action cannot be undone.
                    </div>
                    <form id="reverse-checkin-form">
                        <input type="hidden" id="reverse_waybill_id" name="waybill_id">
                        <div class="mb-3">
                            <label for="reverse_reason" class="form-label">Reason for Reversing Check-in <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="reverse_reason" name="reason" rows="3" required></textarea>
                            <div class="form-text">Please provide a detailed reason for reversing the check-in.</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="confirm-reverse-checkin">
                        <i class="bx bx-undo me-1"></i> Confirm Reverse
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Excess Document Modal (AWB Documents Only) -->
    <div class="modal fade" id="excessDocumentModal" tabindex="-1" aria-labelledby="excessDocumentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="excessDocumentModalLabel">Add Excess Document (AWB Documents Only)</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bx bx-info-circle me-2"></i>
                        Use this form to add excess AWB documents only (FDAW) that were not included in the flight manifest.
                    </div>
                    <form id="excess-document-form">
                        <input type="hidden" name="manifest_id" value="{{ $manifest->manifest_id }}">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="awb_number" class="form-label">AWB Number</label>
                                <input type="text" class="form-control" id="awb_number" name="awb_number" required>
                            </div>
                            <div class="col-md-3">
                                <label for="pieces" class="form-label">Pieces</label>
                                <input type="number" class="form-control" id="pieces" name="pieces" min="1" value="1" required>
                            </div>
                            <div class="col-md-3">
                                <label for="weight" class="form-label">Weight (KG) <small class="text-muted">(Optional for documents)</small></label>
                                <input type="number" step="0.01" class="form-control" id="weight" name="weight" min="0" value="0" placeholder="Leave empty for documents">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="branch_id" class="form-label">Branch</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="1">Main Branch</option>
                                    @foreach(\App\Models\Branch::all() as $branch)
                                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="e.g., AWB Document and Invoice"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="save-excess-document">Save Excess Document</button>
                </div>
            </div>
        </div>
    </div>


@endsection

@section('script')
    <!-- Required datatable js -->
    <script src="{{ URL::asset('build/libs/datatables.net/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('build/libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
    <!-- Responsive examples -->
    <script src="{{ URL::asset('build/libs/datatables.net-responsive/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ URL::asset('build/libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js') }}"></script>
    <!-- Select2 -->
    <script src="{{ URL::asset('build/libs/select2/js/select2.full.min.js') }}"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Check-in Module JS -->
    <script src="{{ URL::asset('js/checkin-combined.js') }}"></script>
    <!-- AWB Reconciliation JS -->
    <script src="{{ URL::asset('js/checkin-reconciliation.js') }}"></script>

    <script>
        // Initialize the AWB list functionality when the document is ready
        $(document).ready(function() {
            // Configure the AWB list
            initAwbList({
                checkinStoreUrl: "{{ route('checkin.store') }}",
                excessDocumentStoreUrl: "{{ route('checkin.excess-document.store') }}",
                reverseCheckinUrl: "{{ route('checkin.reverse') }}",
                updateUldStatusUrl: "{{ route('checkin.update-uld-status') }}",
                cargoStates: @json($cargoStates)
            });

            // Add data attributes to buttons for ULD operations
            $('.checkin-btn, .missing-btn').each(function() {
                $(this).attr('data-uld-type', "{{ $uldType }}");
                $(this).attr('data-uld-number', "{{ $uldNumber ?? '' }}");
                $(this).attr('data-manifest-id', "{{ $manifest->manifest_id }}");
            });

            $('#mark-uld-complete-btn, #mark-uld-partial-btn, #mark-uld-pending-btn').each(function() {
                $(this).attr('data-uld-id', "{{ $uldId }}");
                $(this).attr('data-manifest-id', "{{ $manifest->manifest_id }}");
            });

            // Set default airport based on user branch if available
            @if(Auth::user()->branch && Auth::user()->branch->airport_code)
            const userBranchAirport = "{{ Auth::user()->branch->airport_code }}";
            if (userBranchAirport) {
                $('#origin_airport').val(userBranchAirport).trigger('change');
            }
            @endif
        });
    </script>

@endsection
