[2025-06-28 09:27:05] local.ERROR: Simple XML Import Error: The command "'/var/www/aircargomis/venv/bin/python' '/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py' '/var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml' '--branch-id' '1' '--user-id' '4' '--format=json' '--verbose'" failed.

Exit Code: 1(General error)

Working directory: /var/www/aircargomis/public

Output:
================
2025-06-28 11:27:05,422 - XFFMProcessor - INFO - Starting to process XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml
2025-06-28 11:27:05,430 - XFFMParser - INFO - Connected to the database
2025-06-28 11:27:05,430 - XFFMParser - INFO - Connected to the database
2025-06-28 11:27:05,430 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 11:27:05,430 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 11:27:05,430 - XFFMProcessor - INFO - Parsing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml
2025-06-28 11:27:05,430 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml
2025-06-28 11:27:05,430 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml
2025-06-28 11:27:05,430 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 11:27:05,430 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 11:27:05,431 - XFFMParser - INFO - XML parsed successfully
2025-06-28 11:27:05,431 - XFFMParser - INFO - XML parsed successfully
2025-06-28 11:27:05,431 - XFFMParser - INFO - Extracting data from XML
2025-06-28 11:27:05,431 - XFFMParser - INFO - Extracting data from XML
2025-06-28 11:27:05,440 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 11:27:05,440 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 11:27:05,440 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 11:27:05,440 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 11:27:05,441 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 11:27:05,441 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 11:27:05,441 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 11:27:05,441 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 11:27:05,442 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 11:27:05,443 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 11:27:05,443 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 11:27:05,443 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 11:27:05,443 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 11:27:05,443 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 11:27:05,443 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-60497953 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-60497953 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-60606280 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-60606280 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-41026672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - WARNING - AWB 706-41026672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 11:27:05,443 - XFFMParser - INFO - Processed 3 complete AWBs and 2 partial AWBs
2025-06-28 11:27:05,443 - XFFMParser - INFO - Processed 3 complete AWBs and 2 partial AWBs
2025-06-28 11:27:05,443 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 3 pieces, 111.0 KGM
2025-06-28 11:27:05,443 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 3 pieces, 111.0 KGM
2025-06-28 11:27:05,443 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 3 pieces, 111.0 KGM
2025-06-28 11:27:05,443 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 3 pieces, 111.0 KGM
2025-06-28 11:27:05,444 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 11:27:05,444 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 11:27:05,444 - XFFMParser - INFO - Validating extracted data
2025-06-28 11:27:05,444 - XFFMParser - INFO - Validating extracted data
2025-06-28 11:27:05,444 - XFFMParser - ERROR - Validation error: Waybills must be a list (field: waybills)
2025-06-28 11:27:05,444 - XFFMParser - ERROR - Validation error: Waybills must be a list (field: waybills)
2025-06-28 11:27:05,444 - XFFMParser - ERROR - Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/parsers/xffm_parser.py", line 148, in parse_string
    is_valid = self.validator.validate(extracted_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/validators/xffm_validator.py", line 50, in validate
    self.validate_business_rules(data)
  File "/var/www/aircargomis/python/xml-parsers/validators/xffm_validator.py", line 384, in validate_business_rules
    awb_number = waybill.get("awb_number")
                 ^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'get'
2025-06-28 11:27:05,444 - XFFMParser - ERROR - Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/parsers/xffm_parser.py", line 148, in parse_string
    is_valid = self.validator.validate(extracted_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/validators/xffm_validator.py", line 50, in validate
    self.validate_business_rules(data)
  File "/var/www/aircargomis/python/xml-parsers/validators/xffm_validator.py", line 384, in validate_business_rules
    awb_number = waybill.get("awb_number")
                 ^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'get'
2025-06-28 11:27:05,445 - XFFMProcessor - INFO - Parsing completed in 0.01 seconds
2025-06-28 11:27:05,445 - XFFMProcessor - ERROR - Error processing XFFM file: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
2025-06-28 11:27:05,445 - XFFMProcessor - ERROR - Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'

2025-06-28 11:27:05,446 - XFFMParser - INFO - Database cursor closed
2025-06-28 11:27:05,446 - XFFMParser - INFO - Database cursor closed
2025-06-28 11:27:05,446 - XFFMParser - INFO - Database connection closed
2025-06-28 11:27:05,446 - XFFMParser - INFO - Database connection closed
2025-06-28 11:27:05,448 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
{"success": false, "file_name": "1751102825_XFFM-EK9747_28JUN.xml", "error": "Error calling process_xffm.py: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'"}


Error Output:
================
2025-06-28 11:27:05,399 - IntegratedCargoParser - INFO - Processing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751102825_XFFM-EK9747_28JUN.xml
2025-06-28 11:27:05,448 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Unexpected error during XFFM parsing: 'str' object has no attribute 'get'
  
