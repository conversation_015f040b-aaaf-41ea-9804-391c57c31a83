[2025-06-28 10:15:17] local.ERROR: Simple XML Import Error: The command "'/var/www/aircargomis/venv/bin/python' '/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py' '/var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml' '--branch-id' '1' '--user-id' '2' '--format=json' '--verbose'" failed.

Exit Code: 1(General error)

Working directory: /var/www/aircargomis/public

Output:
================
2025-06-28 12:15:16,949 - XFFMProcessor - INFO - Starting to process XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml
2025-06-28 12:15:16,959 - XFFMParser - INFO - Connected to the database
2025-06-28 12:15:16,959 - XFF<PERSON>arser - INFO - Connected to the database
2025-06-28 12:15:16,959 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 12:15:16,959 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 12:15:16,959 - XFFMProcessor - INFO - Parsing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml
2025-06-28 12:15:16,959 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml
2025-06-28 12:15:16,959 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml
2025-06-28 12:15:16,959 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 12:15:16,959 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 12:15:16,960 - XFFMParser - INFO - XML parsed successfully
2025-06-28 12:15:16,960 - XFFMParser - INFO - XML parsed successfully
2025-06-28 12:15:16,960 - XFFMParser - INFO - Extracting data from XML
2025-06-28 12:15:16,960 - XFFMParser - INFO - Extracting data from XML
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 167-07132753 is complete shipment (code: T)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 167-07132753 is complete shipment (code: T)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,971 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,972 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,973 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,974 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,975 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:16,976 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:16,977 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:16,978 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:16,979 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:16,980 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:16,981 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,982 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:16,983 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:16,984 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,985 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-09851225 is complete shipment (code: T)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-09851225 is complete shipment (code: T)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:16,986 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:16,987 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:16,988 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:16,989 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:16,990 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:16,991 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:16,992 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:16,992 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:16,992 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:16,992 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:16,993 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:16,994 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 167-07132753 is complete shipment (code: T)
2025-06-28 12:15:16,995 - XFFMParser - INFO - AWB 167-07132753 is complete shipment (code: T)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,996 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:16,997 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,998 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:16,999 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:17,000 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:17,001 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:17,001 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:17,001 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:17,001 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:17,002 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:17,003 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:17,004 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:17,005 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:17,006 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:17,007 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,008 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-09851225 is complete shipment (code: T)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-09851225 is complete shipment (code: T)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:17,009 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:17,010 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:17,011 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:17,012 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:17,013 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:17,014 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:17,015 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:17,016 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:17,017 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:17,017 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 10259 (split code: S) - Total pieces: 33, Total weight: 1123.46
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 10259 (split code: S) - Total pieces: 33, Total weight: 1123.46
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556530 from ULD 11131 (split code: S) - Total pieces: 9, Total weight: 215.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556530 from ULD 11131 (split code: S) - Total pieces: 9, Total weight: 215.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556526 from ULD 11131 (split code: S) - Total pieces: 12, Total weight: 180.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556526 from ULD 11131 (split code: S) - Total pieces: 12, Total weight: 180.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556515 from ULD 11131 (split code: S) - Total pieces: 58, Total weight: 810.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556515 from ULD 11131 (split code: S) - Total pieces: 58, Total weight: 810.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556541 from ULD 30427 (split code: S) - Total pieces: 35, Total weight: 454.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556541 from ULD 30427 (split code: S) - Total pieces: 35, Total weight: 454.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 30427 (split code: S) - Total pieces: 48, Total weight: 1634.1200000000001
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 30427 (split code: S) - Total pieces: 48, Total weight: 1634.1200000000001
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 30427 (split code: S) - Total pieces: 48, Total weight: 1434.3
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 30427 (split code: S) - Total pieces: 48, Total weight: 1434.3
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 56, Total weight: 1390.49
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 56, Total weight: 1390.49
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 36057 (split code: S) - Total pieces: 36, Total weight: 1835.81
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 36057 (split code: S) - Total pieces: 36, Total weight: 1835.81
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 66, Total weight: 1824.98
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 66, Total weight: 1824.98
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 38043 (split code: S) - Total pieces: 104, Total weight: 2891.0
2025-06-28 12:15:17,017 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 38043 (split code: S) - Total pieces: 104, Total weight: 2891.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 3620.62
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 3620.62
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 89, Total weight: 4538.5199999999995
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 89, Total weight: 4538.5199999999995
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 38414 (split code: S) - Total pieces: 20, Total weight: 680.89
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 38414 (split code: S) - Total pieces: 20, Total weight: 680.89
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 38414 (split code: S) - Total pieces: 59, Total weight: 2008.6100000000001
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 38414 (split code: S) - Total pieces: 59, Total weight: 2008.6100000000001
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 2042.22
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 2042.22
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 74133 (split code: S) - Total pieces: 93, Total weight: 4742.499999999999
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 74133 (split code: S) - Total pieces: 93, Total weight: 4742.499999999999
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2281.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2281.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2280.96
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2280.96
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759712 from ULD 74671 (split code: S) - Total pieces: 19, Total weight: 719.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759712 from ULD 74671 (split code: S) - Total pieces: 19, Total weight: 719.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 77990 (split code: S) - Total pieces: 72, Total weight: 2378.0
2025-06-28 12:15:17,018 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 77990 (split code: S) - Total pieces: 72, Total weight: 2378.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556740 from ULD 77990 (split code: S) - Total pieces: 2, Total weight: 598.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556740 from ULD 77990 (split code: S) - Total pieces: 2, Total weight: 598.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 78044 (split code: S) - Total pieces: 68, Total weight: 2315.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 78044 (split code: S) - Total pieces: 68, Total weight: 2315.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-02587200 from ULD 78044 (split code: S) - Total pieces: 200, Total weight: 286.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-02587200 from ULD 78044 (split code: S) - Total pieces: 200, Total weight: 286.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-09298295 from ULD 63401 (split code: S) - Total pieces: 25, Total weight: 417.0
2025-06-28 12:15:17,019 - XFFMParser - INFO - Aggregating split-complete AWB 176-09298295 from ULD 63401 (split code: S) - Total pieces: 25, Total weight: 417.0
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132753 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132753 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05555922 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05555922 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132646 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-07132646 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556736 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556736 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,019 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,020 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-11428071 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-11428071 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-13740031 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-13740031 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-06875540 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-06875540 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-04371076 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-04371076 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-04371102 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-04371102 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-08793422 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-08793422 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-09982066 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,021 - XFFMParser - WARNING - AWB 176-09982066 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-08793411 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-08793411 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09296431 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09296431 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09189084 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09189084 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-05387723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-05387723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-05387734 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-05387734 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-10392896 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-10392896 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09981252 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,022 - XFFMParser - WARNING - AWB 176-09981252 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-09516183 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-09516183 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-03141331 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-03141331 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-08320874 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-08320874 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132764 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 167-07132764 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,023 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-12266505 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-12266505 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13759701 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-13759701 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-11518511 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-11518511 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-06525912 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-06525912 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-09189106 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-09189106 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-11773392 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-11773392 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-16998741 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-16998741 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-19757032 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,024 - XFFMParser - WARNING - AWB 176-19757032 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-09851225 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-09851225 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13730673 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-13730673 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-03636371 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-03636371 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-03211880 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,025 - XFFMParser - WARNING - AWB 176-03211880 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-06551053 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-06551053 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-72794632 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-72794632 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-09252515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-09252515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-07132705 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-07132705 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-07132731 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 167-07132731 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-77676351 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-77676351 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-06716065 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,026 - XFFMParser - WARNING - AWB 176-06716065 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-12323500 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-12323500 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-00433650 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-00433650 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-01559121 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-01559121 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-07132716 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-07132716 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-12253146 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-12253146 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-09982055 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-09982055 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-05555944 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-05555944 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-09982044 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 176-09982044 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,027 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-08991570 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-08991570 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 167-07132742 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 167-07132742 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-09192676 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-09192676 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-73003862 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-73003862 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 167-05555933 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 167-05555933 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-12064850 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,028 - XFFMParser - WARNING - AWB 176-12064850 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-09010525 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-09010525 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-03039643 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-03039643 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-13715122 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-13715122 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-12276574 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - WARNING - AWB 176-12276574 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:17,029 - XFFMParser - INFO - Processed 73 complete AWBs and 0 partial AWBs
2025-06-28 12:15:17,029 - XFFMParser - INFO - Processed 73 complete AWBs and 0 partial AWBs
2025-06-28 12:15:17,029 - XFFMParser - INFO - Complete AWB 176-09298295: Split across ULDs ['01629', '63401'] (code: S) - Final totals: 50 pieces, 834.0 KGM
2025-06-28 12:15:17,029 - XFFMParser - INFO - Complete AWB 176-09298295: Split across ULDs ['01629', '63401'] (code: S) - Final totals: 50 pieces, 834.0 KGM
2025-06-28 12:15:17,029 - XFFMParser - INFO - Complete AWB 176-13760530: Split across ULDs ['01629', '10259', '30427', '38414', '74133', '78044'] (code: S) - Final totals: 136 pieces, 4630.0 KGM
2025-06-28 12:15:17,029 - XFFMParser - INFO - Complete AWB 176-13760530: Split across ULDs ['01629', '10259', '30427', '38414', '74133', '78044'] (code: S) - Final totals: 136 pieces, 4630.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13760541: Split across ULDs ['01629', '30427', '38043'] (code: S) - Final totals: 208 pieces, 5782.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13760541: Split across ULDs ['01629', '30427', '38043'] (code: S) - Final totals: 208 pieces, 5782.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-07132672: Split across ULDs ['10259', '36057', '38414', '38414', '74133'] (code: S) - Final totals: 186 pieces, 9484.999999999998 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-07132672: Split across ULDs ['10259', '36057', '38414', '38414', '74133'] (code: S) - Final totals: 186 pieces, 9484.999999999998 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556515: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 116 pieces, 1620.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556515: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 116 pieces, 1620.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556526: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 24 pieces, 360.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556526: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 24 pieces, 360.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13760526: Split across ULDs ['10259', '36057', '36057', '38414', '77990'] (code: S) - Final totals: 144 pieces, 4755.999999999999 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13760526: Split across ULDs ['10259', '36057', '36057', '38414', '77990'] (code: S) - Final totals: 144 pieces, 4755.999999999999 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556541: Split across ULDs ['10259', '30427'] (code: S) - Final totals: 70 pieces, 908.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556541: Split across ULDs ['10259', '30427'] (code: S) - Final totals: 70 pieces, 908.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556530: Split across ULDs ['11131', '11131'] (code: S) - Final totals: 18 pieces, 430.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 167-05556530: Split across ULDs ['11131', '11131'] (code: S) - Final totals: 18 pieces, 430.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13759745: Split across ULDs ['30427', '38414', '74133'] (code: S) - Final totals: 134 pieces, 4562.0 KGM
2025-06-28 12:15:17,030 - XFFMParser - INFO - Complete AWB 176-13759745: Split across ULDs ['30427', '38414', '74133'] (code: S) - Final totals: 134 pieces, 4562.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 176-13759712: Split across ULDs ['36057', '74671'] (code: S) - Final totals: 38 pieces, 1438.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 176-13759712: Split across ULDs ['36057', '74671'] (code: S) - Final totals: 38 pieces, 1438.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 176-02587200: Split across ULDs ['36621', '78044'] (code: S) - Final totals: 400 pieces, 572.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 176-02587200: Split across ULDs ['36621', '78044'] (code: S) - Final totals: 400 pieces, 572.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 167-05556740: Split across ULDs ['74671', '77990'] (code: S) - Final totals: 4 pieces, 1196.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Complete AWB 167-05556740: Split across ULDs ['74671', '77990'] (code: S) - Final totals: 4 pieces, 1196.0 KGM
2025-06-28 12:15:17,031 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 12:15:17,031 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 12:15:17,031 - XFFMParser - INFO - Validating extracted data
2025-06-28 12:15:17,031 - XFFMParser - INFO - Validating extracted data
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 167-05556530 appears multiple times in ULD 11131 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 167-05556530 appears multiple times in ULD 11131 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 176-13760526 appears multiple times in ULD 36057 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 176-13760526 appears multiple times in ULD 36057 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 167-07132672 appears multiple times in ULD 38414 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - WARNING - Validation warning: AWB number 167-07132672 appears multiple times in ULD 38414 (will be aggregated) (field: ulds)
2025-06-28 12:15:17,032 - XFFMParser - INFO - Validating 73 complete AWBs and 0 partial AWBs
2025-06-28 12:15:17,032 - XFFMParser - INFO - Validating 73 complete AWBs and 0 partial AWBs
2025-06-28 12:15:17,032 - XFFMParser - INFO - Saving data to database
2025-06-28 12:15:17,032 - XFFMParser - INFO - Saving data to database
2025-06-28 12:15:17,034 - XFFMParser - ERROR - Failed to save data to database
2025-06-28 12:15:17,034 - XFFMParser - ERROR - Failed to save data to database
2025-06-28 12:15:17,034 - XFFMProcessor - INFO - Parsing completed in 0.07 seconds
2025-06-28 12:15:17,034 - XFFMProcessor - ERROR - Error processing XFFM file: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
2025-06-28 12:15:17,034 - XFFMProcessor - ERROR - Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists

2025-06-28 12:15:17,034 - XFFMParser - INFO - Database cursor closed
2025-06-28 12:15:17,034 - XFFMParser - INFO - Database cursor closed
2025-06-28 12:15:17,035 - XFFMParser - INFO - Database connection closed
2025-06-28 12:15:17,035 - XFFMParser - INFO - Database connection closed
2025-06-28 12:15:17,035 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
{"success": false, "file_name": "1751105716_flight_manifest_2025-06-27 (7).xml", "error": "Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists"}


Error Output:
================
2025-06-28 12:15:16,918 - IntegratedCargoParser - INFO - Processing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105716_flight_manifest_2025-06-27 (7).xml
2025-06-28 12:15:17,035 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
  
[2025-06-28 10:15:30] local.ERROR: Simple XML Import Error: The command "'/var/www/aircargomis/venv/bin/python' '/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py' '/var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml' '--branch-id' '1' '--user-id' '2' '--format=json' '--verbose'" failed.

Exit Code: 1(General error)

Working directory: /var/www/aircargomis/public

Output:
================
2025-06-28 12:15:30,807 - XFFMProcessor - INFO - Starting to process XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml
2025-06-28 12:15:30,813 - XFFMParser - INFO - Connected to the database
2025-06-28 12:15:30,813 - XFFMParser - INFO - Connected to the database
2025-06-28 12:15:30,814 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 12:15:30,814 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 12:15:30,814 - XFFMProcessor - INFO - Parsing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml
2025-06-28 12:15:30,814 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml
2025-06-28 12:15:30,814 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml
2025-06-28 12:15:30,814 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 12:15:30,814 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 12:15:30,815 - XFFMParser - INFO - XML parsed successfully
2025-06-28 12:15:30,815 - XFFMParser - INFO - XML parsed successfully
2025-06-28 12:15:30,815 - XFFMParser - INFO - Extracting data from XML
2025-06-28 12:15:30,815 - XFFMParser - INFO - Extracting data from XML
2025-06-28 12:15:30,825 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,825 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,826 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:30,827 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,828 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,829 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,830 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:30,831 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,832 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:30,833 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:30,834 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,835 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,836 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:30,837 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:30,838 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,839 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,840 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,840 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,840 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,840 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:30,841 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:30,842 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:30,843 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:30,844 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:30,845 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:30,846 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:30,847 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:30,847 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:30,847 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,847 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,848 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,848 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,848 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:30,848 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:30,849 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:30,850 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:30,851 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:30,852 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:30,852 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,853 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:30,854 - XFFMParser - INFO - AWB 167-05555922 is complete shipment (code: T)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:30,855 - XFFMParser - INFO - AWB 167-07132646 is complete shipment (code: T)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556736 is complete shipment (code: T)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556530 split across ULDs (code: S)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,856 - XFFMParser - INFO - AWB 167-05556526 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 167-05556515 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 167-05556541 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,857 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,858 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-11428071 is complete shipment (code: T)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:30,859 - XFFMParser - INFO - AWB 176-13740031 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-06875540 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-04371076 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:30,860 - XFFMParser - INFO - AWB 176-04371102 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-08793422 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-09982066 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-08793411 is complete shipment (code: T)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,861 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-09296431 is complete shipment (code: T)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-09189084 is complete shipment (code: T)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:30,862 - XFFMParser - INFO - AWB 176-05387723 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-05387734 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-10392896 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:30,863 - XFFMParser - INFO - AWB 176-09981252 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-09516183 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-03141331 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:30,864 - XFFMParser - INFO - AWB 176-08320874 is complete shipment (code: T)
2025-06-28 12:15:30,865 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,865 - XFFMParser - INFO - AWB 176-13760541 split across ULDs (code: S)
2025-06-28 12:15:30,865 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,865 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 167-07132764 is complete shipment (code: T)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,866 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,867 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:30,867 - XFFMParser - INFO - AWB 176-12266505 is complete shipment (code: T)
2025-06-28 12:15:30,867 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,867 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,868 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,868 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,869 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:30,869 - XFFMParser - INFO - AWB 176-13759701 is complete shipment (code: T)
2025-06-28 12:15:30,869 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:30,869 - XFFMParser - INFO - AWB 176-11518511 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-06525912 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-09189106 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:30,870 - XFFMParser - INFO - AWB 176-11773392 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-16998741 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-19757032 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:30,871 - XFFMParser - INFO - AWB 176-13759723 is complete shipment (code: T)
2025-06-28 12:15:30,872 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,872 - XFFMParser - INFO - AWB 167-07132672 split across ULDs (code: S)
2025-06-28 12:15:30,872 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,872 - XFFMParser - INFO - AWB 176-13759745 split across ULDs (code: S)
2025-06-28 12:15:30,873 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,873 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,874 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,874 - XFFMParser - INFO - AWB 176-13759712 split across ULDs (code: S)
2025-06-28 12:15:30,875 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:30,875 - XFFMParser - INFO - AWB 176-13730673 is complete shipment (code: T)
2025-06-28 12:15:30,875 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:30,875 - XFFMParser - INFO - AWB 176-03636371 is complete shipment (code: T)
2025-06-28 12:15:30,876 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:30,876 - XFFMParser - INFO - AWB 176-03211880 is complete shipment (code: T)
2025-06-28 12:15:30,876 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:30,876 - XFFMParser - INFO - AWB 176-06551053 is complete shipment (code: T)
2025-06-28 12:15:30,877 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:30,877 - XFFMParser - INFO - AWB 176-72794632 is complete shipment (code: T)
2025-06-28 12:15:30,877 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:30,877 - XFFMParser - INFO - AWB 176-09252515 is complete shipment (code: T)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-07132705 is complete shipment (code: T)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:30,878 - XFFMParser - INFO - AWB 167-07132731 is complete shipment (code: T)
2025-06-28 12:15:30,879 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:30,879 - XFFMParser - INFO - AWB 176-77676351 is complete shipment (code: T)
2025-06-28 12:15:30,879 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:30,879 - XFFMParser - INFO - AWB 176-06716065 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-12323500 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-00433650 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 176-01559121 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:30,880 - XFFMParser - INFO - AWB 167-07132716 is complete shipment (code: T)
2025-06-28 12:15:30,881 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:30,881 - XFFMParser - INFO - AWB 176-12253146 is complete shipment (code: T)
2025-06-28 12:15:30,881 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:30,881 - XFFMParser - INFO - AWB 176-09982055 is complete shipment (code: T)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 176-13760526 split across ULDs (code: S)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 167-05555944 is complete shipment (code: T)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 176-09982044 is complete shipment (code: T)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,882 - XFFMParser - INFO - AWB 167-05556740 split across ULDs (code: S)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-13760530 split across ULDs (code: S)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-08991570 is complete shipment (code: T)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,883 - XFFMParser - INFO - AWB 176-02587200 split across ULDs (code: S)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 167-07132742 is complete shipment (code: T)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 176-09192676 is complete shipment (code: T)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:30,884 - XFFMParser - INFO - AWB 176-73003862 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 167-05555933 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 176-12064850 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:30,885 - XFFMParser - INFO - AWB 176-09010525 is complete shipment (code: T)
2025-06-28 12:15:30,886 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:30,886 - XFFMParser - INFO - AWB 176-03039643 is complete shipment (code: T)
2025-06-28 12:15:30,886 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:30,886 - XFFMParser - INFO - AWB 176-09298295 split across ULDs (code: S)
2025-06-28 12:15:30,889 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:30,889 - XFFMParser - INFO - AWB 176-13715122 is complete shipment (code: T)
2025-06-28 12:15:30,890 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:30,890 - XFFMParser - INFO - AWB 176-12276574 is complete shipment (code: T)
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556530 from ULD 11131 (split code: S) - Total pieces: 9, Total weight: 215.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556530 from ULD 11131 (split code: S) - Total pieces: 9, Total weight: 215.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556526 from ULD 11131 (split code: S) - Total pieces: 12, Total weight: 180.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556526 from ULD 11131 (split code: S) - Total pieces: 12, Total weight: 180.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556515 from ULD 11131 (split code: S) - Total pieces: 58, Total weight: 810.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556515 from ULD 11131 (split code: S) - Total pieces: 58, Total weight: 810.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556541 from ULD 30427 (split code: S) - Total pieces: 35, Total weight: 454.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556541 from ULD 30427 (split code: S) - Total pieces: 35, Total weight: 454.0
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 30427 (split code: S) - Total pieces: 36, Total weight: 1225.59
2025-06-28 12:15:30,890 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 30427 (split code: S) - Total pieces: 36, Total weight: 1225.59
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 56, Total weight: 1390.49
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 56, Total weight: 1390.49
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 36057 (split code: S) - Total pieces: 36, Total weight: 1835.81
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 36057 (split code: S) - Total pieces: 36, Total weight: 1835.81
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 66, Total weight: 1824.98
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 36057 (split code: S) - Total pieces: 66, Total weight: 1824.98
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 38043 (split code: S) - Total pieces: 80, Total weight: 2023.85
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760541 from ULD 38043 (split code: S) - Total pieces: 80, Total weight: 2023.85
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 3620.62
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 3620.62
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 89, Total weight: 4538.5199999999995
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 38414 (split code: S) - Total pieces: 89, Total weight: 4538.5199999999995
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 38414 (split code: S) - Total pieces: 20, Total weight: 680.89
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 38414 (split code: S) - Total pieces: 20, Total weight: 680.89
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 38414 (split code: S) - Total pieces: 47, Total weight: 1600.08
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 38414 (split code: S) - Total pieces: 47, Total weight: 1600.08
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 2042.22
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 38414 (split code: S) - Total pieces: 71, Total weight: 2042.22
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 74133 (split code: S) - Total pieces: 93, Total weight: 4742.499999999999
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 167-07132672 from ULD 74133 (split code: S) - Total pieces: 93, Total weight: 4742.499999999999
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2281.0
2025-06-28 12:15:30,891 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759745 from ULD 74133 (split code: S) - Total pieces: 67, Total weight: 2281.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 74133 (split code: S) - Total pieces: 55, Total weight: 1872.4299999999998
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 74133 (split code: S) - Total pieces: 55, Total weight: 1872.4299999999998
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759712 from ULD 74671 (split code: S) - Total pieces: 19, Total weight: 719.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13759712 from ULD 74671 (split code: S) - Total pieces: 19, Total weight: 719.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 77990 (split code: S) - Total pieces: 72, Total weight: 2378.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760526 from ULD 77990 (split code: S) - Total pieces: 72, Total weight: 2378.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556740 from ULD 77990 (split code: S) - Total pieces: 2, Total weight: 598.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 167-05556740 from ULD 77990 (split code: S) - Total pieces: 2, Total weight: 598.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 78044 (split code: S) - Total pieces: 56, Total weight: 1906.4699999999998
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-13760530 from ULD 78044 (split code: S) - Total pieces: 56, Total weight: 1906.4699999999998
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-02587200 from ULD 78044 (split code: S) - Total pieces: 200, Total weight: 286.0
2025-06-28 12:15:30,892 - XFFMParser - INFO - Aggregating split-complete AWB 176-02587200 from ULD 78044 (split code: S) - Total pieces: 200, Total weight: 286.0
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05555922 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05555922 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,892 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-07132646 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-07132646 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556736 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556736 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-05556541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-11428071 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-11428071 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13740031 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-13740031 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-06875540 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-06875540 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-04371076 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-04371076 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-04371102 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,893 - XFFMParser - WARNING - AWB 176-04371102 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08793422 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08793422 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09982066 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09982066 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08793411 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08793411 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09296431 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09296431 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09189084 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09189084 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-05387723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-05387723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-05387734 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-05387734 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-10392896 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-10392896 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09981252 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09981252 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09516183 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-09516183 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-03141331 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-03141331 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08320874 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-08320874 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,894 - XFFMParser - WARNING - AWB 176-13760541 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132764 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132764 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-12266505 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-12266505 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759701 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759701 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-11518511 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-11518511 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-06525912 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-06525912 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-09189106 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-09189106 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-11773392 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-11773392 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-16998741 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-16998741 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-19757032 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-19757032 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759723 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 167-07132672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759745 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13759712 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13730673 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,895 - XFFMParser - WARNING - AWB 176-13730673 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-03636371 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-03636371 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-03211880 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-03211880 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-06551053 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-06551053 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-72794632 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-72794632 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09252515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09252515 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132705 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132705 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132731 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132731 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-77676351 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-77676351 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-06716065 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-06716065 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-12323500 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-12323500 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-00433650 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-00433650 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-01559121 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-01559121 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132716 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-07132716 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-12253146 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-12253146 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09982055 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09982055 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-13760526 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05555944 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05555944 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09982044 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-09982044 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 167-05556740 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-13760530 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-08991570 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-08991570 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,896 - XFFMParser - WARNING - AWB 176-02587200 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 167-07132742 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 167-07132742 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09192676 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09192676 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-73003862 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-73003862 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 167-05555933 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 167-05555933 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-12064850 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-12064850 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09010525 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09010525 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-03039643 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-03039643 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-09298295 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-13715122 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-13715122 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-12276574 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - WARNING - AWB 176-12276574 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 12:15:30,897 - XFFMParser - INFO - Processed 71 complete AWBs and 0 partial AWBs
2025-06-28 12:15:30,897 - XFFMParser - INFO - Processed 71 complete AWBs and 0 partial AWBs
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-07132672: Split across ULDs ['10259', '36057', '38414', '38414', '74133'] (code: S) - Final totals: 186 pieces, 9484.999999999998 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-07132672: Split across ULDs ['10259', '36057', '38414', '38414', '74133'] (code: S) - Final totals: 186 pieces, 9484.999999999998 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760530: Split across ULDs ['10259', '30427', '38414', '74133', '78044'] (code: S) - Final totals: 112 pieces, 3812.939999999999 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760530: Split across ULDs ['10259', '30427', '38414', '74133', '78044'] (code: S) - Final totals: 112 pieces, 3812.939999999999 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556515: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 116 pieces, 1620.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556515: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 116 pieces, 1620.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556526: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 24 pieces, 360.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556526: Split across ULDs ['10259', '11131'] (code: S) - Final totals: 24 pieces, 360.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760526: Split across ULDs ['10259', '36057', '36057', '38414', '77990'] (code: S) - Final totals: 144 pieces, 4755.999999999999 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760526: Split across ULDs ['10259', '36057', '36057', '38414', '77990'] (code: S) - Final totals: 144 pieces, 4755.999999999999 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556541: Split across ULDs ['10259', '30427'] (code: S) - Final totals: 70 pieces, 908.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556541: Split across ULDs ['10259', '30427'] (code: S) - Final totals: 70 pieces, 908.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556530: Split across ULDs ['11131', '11131'] (code: S) - Final totals: 18 pieces, 430.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 167-05556530: Split across ULDs ['11131', '11131'] (code: S) - Final totals: 18 pieces, 430.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760541: Split across ULDs ['30427', '38043'] (code: S) - Final totals: 160 pieces, 4047.7 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13760541: Split across ULDs ['30427', '38043'] (code: S) - Final totals: 160 pieces, 4047.7 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13759745: Split across ULDs ['30427', '38414', '74133'] (code: S) - Final totals: 134 pieces, 4562.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13759745: Split across ULDs ['30427', '38414', '74133'] (code: S) - Final totals: 134 pieces, 4562.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13759712: Split across ULDs ['36057', '74671'] (code: S) - Final totals: 38 pieces, 1438.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-13759712: Split across ULDs ['36057', '74671'] (code: S) - Final totals: 38 pieces, 1438.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-02587200: Split across ULDs ['36621', '78044'] (code: S) - Final totals: 400 pieces, 572.0 KGM
2025-06-28 12:15:30,897 - XFFMParser - INFO - Complete AWB 176-02587200: Split across ULDs ['36621', '78044'] (code: S) - Final totals: 400 pieces, 572.0 KGM
2025-06-28 12:15:30,898 - XFFMParser - INFO - Complete AWB 167-05556740: Split across ULDs ['74671', '77990'] (code: S) - Final totals: 4 pieces, 1196.0 KGM
2025-06-28 12:15:30,898 - XFFMParser - INFO - Complete AWB 167-05556740: Split across ULDs ['74671', '77990'] (code: S) - Final totals: 4 pieces, 1196.0 KGM
2025-06-28 12:15:30,898 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 12:15:30,898 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 12:15:30,898 - XFFMParser - INFO - Validating extracted data
2025-06-28 12:15:30,898 - XFFMParser - INFO - Validating extracted data
2025-06-28 12:15:30,898 - XFFMParser - WARNING - Validation warning: AWB number 167-05556530 appears multiple times in ULD 11131 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,898 - XFFMParser - WARNING - Validation warning: AWB number 167-05556530 appears multiple times in ULD 11131 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,898 - XFFMParser - WARNING - Validation warning: AWB number 176-13760526 appears multiple times in ULD 36057 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,898 - XFFMParser - WARNING - Validation warning: AWB number 176-13760526 appears multiple times in ULD 36057 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,899 - XFFMParser - WARNING - Validation warning: AWB number 167-07132672 appears multiple times in ULD 38414 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,899 - XFFMParser - WARNING - Validation warning: AWB number 167-07132672 appears multiple times in ULD 38414 (will be aggregated) (field: ulds)
2025-06-28 12:15:30,899 - XFFMParser - INFO - Validating 71 complete AWBs and 0 partial AWBs
2025-06-28 12:15:30,899 - XFFMParser - INFO - Validating 71 complete AWBs and 0 partial AWBs
2025-06-28 12:15:30,899 - XFFMParser - INFO - Saving data to database
2025-06-28 12:15:30,899 - XFFMParser - INFO - Saving data to database
2025-06-28 12:15:30,902 - XFFMParser - ERROR - Failed to save data to database
2025-06-28 12:15:30,902 - XFFMParser - ERROR - Failed to save data to database
2025-06-28 12:15:30,902 - XFFMProcessor - INFO - Parsing completed in 0.09 seconds
2025-06-28 12:15:30,903 - XFFMProcessor - ERROR - Error processing XFFM file: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
2025-06-28 12:15:30,903 - XFFMProcessor - ERROR - Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists

2025-06-28 12:15:30,903 - XFFMParser - INFO - Database cursor closed
2025-06-28 12:15:30,903 - XFFMParser - INFO - Database cursor closed
2025-06-28 12:15:30,903 - XFFMParser - INFO - Database connection closed
2025-06-28 12:15:30,903 - XFFMParser - INFO - Database connection closed
2025-06-28 12:15:30,903 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
{"success": false, "file_name": "1751105730_flight_manifest_2025-06-27 (6).xml", "error": "Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists"}


Error Output:
================
2025-06-28 12:15:30,790 - IntegratedCargoParser - INFO - Processing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751105730_flight_manifest_2025-06-27 (6).xml
2025-06-28 12:15:30,903 - IntegratedCargoParser - ERROR - Error calling process_xffm.py: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/integrated_cargo_parser.py", line 134, in _call_parser_script
    return process_xffm(file_path, self.branch_id, self.user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/process_xffm.py", line 53, in process_xffm
    raise Exception(error_msg)
Exception: Failed to parse XFFM file: Manifest FM-EK9747-20250628 already exists
  
[2025-06-28 10:36:47] local.INFO: Simple XML Import Output: 2025-06-28 12:36:47,090 - XFZBParser - INFO - Connected to the database
2025-06-28 12:36:47,090 - XFZBParser - INFO - Connected to the database
2025-06-28 12:36:47,090 - XFZBParser - INFO - XFZB Parser initialized with modular architecture
2025-06-28 12:36:47,090 - XFZBParser - INFO - XFZB Parser initialized with modular architecture
2025-06-28 12:36:47,090 - XFZBProcessor - INFO - Parsing XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107007_SHA123 (2).xml
2025-06-28 12:36:47,090 - XFZBParser - INFO - Starting to parse XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107007_SHA123 (2).xml
2025-06-28 12:36:47,090 - XFZBParser - INFO - Starting to parse XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107007_SHA123 (2).xml
2025-06-28 12:36:47,090 - XFZBParser - INFO - Starting to parse XFZB XML string
2025-06-28 12:36:47,090 - XFZBParser - INFO - Starting to parse XFZB XML string
2025-06-28 12:36:47,090 - XFZBParser - INFO - XML parsed successfully
2025-06-28 12:36:47,090 - XFZBParser - INFO - XML parsed successfully
2025-06-28 12:36:47,090 - XFZBParser - INFO - Extracting data from XML
2025-06-28 12:36:47,090 - XFZBParser - INFO - Extracting data from XML
2025-06-28 12:36:47,091 - XFZBParser - INFO - Extracted data for House AWB: SHA123 (Master: 706-60497953)
2025-06-28 12:36:47,091 - XFZBParser - INFO - Extracted data for House AWB: SHA123 (Master: 706-60497953)
2025-06-28 12:36:47,091 - XFZBParser - INFO - Validating extracted data
2025-06-28 12:36:47,091 - XFZBParser - INFO - Validating extracted data
2025-06-28 12:36:47,091 - XFZBParser - WARNING - Validation warning: No goods description found (field: description)
2025-06-28 12:36:47,091 - XFZBParser - WARNING - Validation warning: No goods description found (field: description)
2025-06-28 12:36:47,092 - XFZBParser - INFO - Saving data to database
2025-06-28 12:36:47,092 - XFZBParser - INFO - Saving data to database
2025-06-28 12:36:47,095 - XFZBParser - INFO - Storing original XML content (5807 characters) for HAWB SHA123
2025-06-28 12:36:47,095 - XFZBParser - INFO - Storing original XML content (5807 characters) for HAWB SHA123
2025-06-28 12:36:47,097 - XFZBParser - INFO - Saved house waybill with ID 1
2025-06-28 12:36:47,097 - XFZBParser - INFO - Saved house waybill with ID 1
2025-06-28 12:36:47,098 - XFZBParser - INFO - Created new consignee: CAPTEC LIMITED (ID: 2)
2025-06-28 12:36:47,098 - XFZBParser - INFO - Created new consignee: CAPTEC LIMITED (ID: 2)
2025-06-28 12:36:47,099 - XFZBParser - INFO - Updated House AWB 1 with party information: {'shipper_code': 51, 'consignee_code': 2}
2025-06-28 12:36:47,099 - XFZBParser - INFO - Updated House AWB 1 with party information: {'shipper_code': 51, 'consignee_code': 2}
2025-06-28 12:36:47,100 - XFZBParser - INFO - Successfully saved XFZB data for House AWB SHA123
2025-06-28 12:36:47,100 - XFZBParser - INFO - Successfully saved XFZB data for House AWB SHA123
2025-06-28 12:36:47,100 - XFZBParser - INFO - Successfully processed XFZB for House AWB SHA123
2025-06-28 12:36:47,100 - XFZBParser - INFO - Successfully processed XFZB for House AWB SHA123
2025-06-28 12:36:47,100 - XFZBProcessor - INFO - Successfully processed XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107007_SHA123 (2).xml
2025-06-28 12:36:47,100 - XFZBProcessor - INFO - Processed house waybill with HAWB number: SHA123, linked to MAWB: 706-60497953
2025-06-28 12:36:47,100 - XFZBProcessor - INFO - House AWB ID: 1
2025-06-28 12:36:47,100 - XFZBProcessor - WARNING - Processing warnings:
2025-06-28 12:36:47,100 - XFZBProcessor - WARNING -   - No goods description found
2025-06-28 12:36:47,100 - XFZBParser - INFO - Database cursor closed
2025-06-28 12:36:47,100 - XFZBParser - INFO - Database cursor closed
2025-06-28 12:36:47,100 - XFZBParser - INFO - Database connection closed
2025-06-28 12:36:47,100 - XFZBParser - INFO - Database connection closed
{"success": true, "hawb_number": "SHA123", "hawb_id": 1, "mawb_number": "706-60497953", "origin_airport": "BOM", "destination_airport": "LLW", "total_pieces": 1, "total_weight": 30.0, "shipper_name": "IEI INTEGRATION CORP", "consignee_name": "CAPTEC LIMITED", "is_mail": false, "is_human_remains": false, "is_partial": false, "warnings": ["No goods description found"]}
  
[2025-06-28 10:36:55] local.INFO: Simple XML Import Output: 2025-06-28 12:36:54,995 - XFZBParser - INFO - Connected to the database
2025-06-28 12:36:54,995 - XFZBParser - INFO - Connected to the database
2025-06-28 12:36:54,995 - XFZBParser - INFO - XFZB Parser initialized with modular architecture
2025-06-28 12:36:54,995 - XFZBParser - INFO - XFZB Parser initialized with modular architecture
2025-06-28 12:36:54,995 - XFZBProcessor - INFO - Parsing XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107014_SHA124 (2).xml
2025-06-28 12:36:54,995 - XFZBParser - INFO - Starting to parse XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107014_SHA124 (2).xml
2025-06-28 12:36:54,995 - XFZBParser - INFO - Starting to parse XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107014_SHA124 (2).xml
2025-06-28 12:36:54,995 - XFZBParser - INFO - Starting to parse XFZB XML string
2025-06-28 12:36:54,995 - XFZBParser - INFO - Starting to parse XFZB XML string
2025-06-28 12:36:54,995 - XFZBParser - INFO - XML parsed successfully
2025-06-28 12:36:54,995 - XFZBParser - INFO - XML parsed successfully
2025-06-28 12:36:54,996 - XFZBParser - INFO - Extracting data from XML
2025-06-28 12:36:54,996 - XFZBParser - INFO - Extracting data from XML
2025-06-28 12:36:54,997 - XFZBParser - INFO - Extracted data for House AWB: SHA124 (Master: 706-60497953)
2025-06-28 12:36:54,997 - XFZBParser - INFO - Extracted data for House AWB: SHA124 (Master: 706-60497953)
2025-06-28 12:36:54,997 - XFZBParser - INFO - Validating extracted data
2025-06-28 12:36:54,997 - XFZBParser - INFO - Validating extracted data
2025-06-28 12:36:54,997 - XFZBParser - WARNING - Validation warning: No goods description found (field: description)
2025-06-28 12:36:54,997 - XFZBParser - WARNING - Validation warning: No goods description found (field: description)
2025-06-28 12:36:54,997 - XFZBParser - INFO - Saving data to database
2025-06-28 12:36:54,997 - XFZBParser - INFO - Saving data to database
2025-06-28 12:36:55,001 - XFZBParser - INFO - Storing original XML content (5808 characters) for HAWB SHA124
2025-06-28 12:36:55,001 - XFZBParser - INFO - Storing original XML content (5808 characters) for HAWB SHA124
2025-06-28 12:36:55,002 - XFZBParser - INFO - Saved house waybill with ID 2
2025-06-28 12:36:55,002 - XFZBParser - INFO - Saved house waybill with ID 2
2025-06-28 12:36:55,003 - XFZBParser - INFO - Updated House AWB 2 with party information: {'shipper_code': 51, 'consignee_code': 2}
2025-06-28 12:36:55,003 - XFZBParser - INFO - Updated House AWB 2 with party information: {'shipper_code': 51, 'consignee_code': 2}
2025-06-28 12:36:55,004 - XFZBParser - INFO - Successfully saved XFZB data for House AWB SHA124
2025-06-28 12:36:55,004 - XFZBParser - INFO - Successfully saved XFZB data for House AWB SHA124
2025-06-28 12:36:55,004 - XFZBParser - INFO - Successfully processed XFZB for House AWB SHA124
2025-06-28 12:36:55,004 - XFZBParser - INFO - Successfully processed XFZB for House AWB SHA124
2025-06-28 12:36:55,004 - XFZBProcessor - INFO - Successfully processed XFZB file: /var/www/aircargomis/storage/app/private/xml-imports/1751107014_SHA124 (2).xml
2025-06-28 12:36:55,004 - XFZBProcessor - INFO - Processed house waybill with HAWB number: SHA124, linked to MAWB: 706-60497953
2025-06-28 12:36:55,004 - XFZBProcessor - INFO - House AWB ID: 2
2025-06-28 12:36:55,004 - XFZBProcessor - WARNING - Processing warnings:
2025-06-28 12:36:55,004 - XFZBProcessor - WARNING -   - No goods description found
2025-06-28 12:36:55,004 - XFZBParser - INFO - Database cursor closed
2025-06-28 12:36:55,004 - XFZBParser - INFO - Database cursor closed
2025-06-28 12:36:55,005 - XFZBParser - INFO - Database connection closed
2025-06-28 12:36:55,005 - XFZBParser - INFO - Database connection closed
{"success": true, "hawb_number": "SHA124", "hawb_id": 2, "mawb_number": "706-60497953", "origin_airport": "BOM", "destination_airport": "LLW", "total_pieces": 1, "total_weight": 25.0, "shipper_name": "IEI INTEGRATION CORP", "consignee_name": "CAPTEC LIMITED", "is_mail": false, "is_human_remains": false, "is_partial": false, "warnings": ["No goods description found"]}
  
[2025-06-28 10:37:06] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:06] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:06] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 10:37:06] local.INFO: Found 8 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672  
[2025-06-28 10:37:06] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-60606280, 706-41026672  
[2025-06-28 10:37:06] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 10:37:06] local.INFO: Adding 2 unique house waybills to the collection  
[2025-06-28 10:37:06] local.INFO: Added HAWB SHA123 to ULD 01921 with 0 pieces and 0 kg  
[2025-06-28 10:37:06] local.INFO: Added HAWB SHA124 to ULD 01921 with 0 pieces and 0 kg  
[2025-06-28 10:37:06] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:06] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 10:37:47] local.INFO: AWB 706-60497953 marked as RECEIVED during check-in  
[2025-06-28 10:37:49] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:49] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:49] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 10:37:50] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 10:37:50] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-41026672, 706-60497953  
[2025-06-28 10:37:50] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 10:37:50] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 10:37:50] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:50] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 10:37:53] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:53] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:53] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 10:37:53] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 10:37:53] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-41026672, 706-60497953  
[2025-06-28 10:37:53] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 10:37:53] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 10:37:53] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:37:53] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 10:38:05] local.WARNING: Foreign key constraint error when creating checkin record. Trying raw insert: SQLSTATE[23503]: Foreign key violation: 7 ERROR:  insert or update on table "checkin_records" violates foreign key constraint "checkin_records_waybill_id_foreign"
DETAIL:  Key (waybill_id)=(SHA123) is not present in table "master_waybills". (Connection: pgsql, SQL: insert into "checkin_records" ("waybill_id", "manifest_id", "uld_id", "pieces_received", "weight_received", "cargo_condition", "is_damaged", "is_missing", "is_completely_missing", "damage_notes", "storage_location", "branch_id", "created_by", "updated_by", "is_house", "status", "updated_at", "created_at") values (SHA123, FM-EK9747-20250628, PMC01921, 1, 30, GOOD, 0, 0, 0, ?, ?, 1, 2, 2, 1, CHECKED_IN, 2025-06-28 10:38:05, 2025-06-28 10:38:05) returning "id")  
[2025-06-28 10:38:05] local.ERROR: Unexpected error during check-in: SQLSTATE[25P02]: In failed sql transaction: 7 ERROR:  current transaction is aborted, commands ignored until end of transaction block (Connection: pgsql, SQL: insert into "checkin_records" ("waybill_id", "is_house", "manifest_id", "uld_id", "pieces_received", "weight_received", "cargo_condition", "is_damaged", "is_missing", "is_completely_missing", "damage_notes", "storage_location", "branch_id", "created_by", "updated_by", "created_at", "updated_at", "status") values (SHA123, 1, FM-EK9747-20250628, PMC01921, 1, 30, GOOD, 0, 0, 0, ?, ?, 1, 2, 2, 2025-06-28 10:38:05, 2025-06-28 10:38:05, CHECKED_IN) returning "id") {"waybill_id":"SHA123","manifest_id":"FM-EK9747-20250628","exception":"[object] (Illuminate\\Database\\QueryException(code: 25P02): SQLSTATE[25P02]: In failed sql transaction: 7 ERROR:  current transaction is aborted, commands ignored until end of transaction block (Connection: pgsql, SQL: insert into \"checkin_records\" (\"waybill_id\", \"is_house\", \"manifest_id\", \"uld_id\", \"pieces_received\", \"weight_received\", \"cargo_condition\", \"is_damaged\", \"is_missing\", \"is_completely_missing\", \"damage_notes\", \"storage_location\", \"branch_id\", \"created_by\", \"updated_by\", \"created_at\", \"updated_at\", \"status\") values (SHA123, 1, FM-EK9747-20250628, PMC01921, 1, 30, GOOD, 0, 0, 0, ?, ?, 1, 2, 2, 2025-06-28 10:38:05, 2025-06-28 10:38:05, CHECKED_IN) returning \"id\") at /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run()
#2 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(382): Illuminate\\Database\\Connection->select()
#3 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection()
#4 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3779): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId()
#5 /var/www/aircargomis/app/Domains/Checkin/Services/CheckinService.php(638): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 /var/www/aircargomis/app/Domains/Checkin/Controllers/CheckinController.php(469): App\\Domains\\Checkin\\Services\\CheckinService->createCheckinRecord()
#7 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Domains\\Checkin\\Controllers\\CheckinController->processCheckin()
#8 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#9 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#10 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#11 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#12 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#13 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#15 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#17 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 /var/www/aircargomis/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 {main}

[previous exception] [object] (PDOException(code: 25P02): SQLSTATE[25P02]: In failed sql transaction: 7 ERROR:  current transaction is aborted, commands ignored until end of transaction block at /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php:409)
[stacktrace]
#0 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(409): PDOStatement->execute()
#1 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run()
#4 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Connection.php(382): Illuminate\\Database\\Connection->select()
#5 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection()
#6 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3779): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId()
#7 /var/www/aircargomis/app/Domains/Checkin/Services/CheckinService.php(638): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 /var/www/aircargomis/app/Domains/Checkin/Controllers/CheckinController.php(469): App\\Domains\\Checkin\\Services\\CheckinService->createCheckinRecord()
#9 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Domains\\Checkin\\Controllers\\CheckinController->processCheckin()
#10 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#13 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#14 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#19 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#21 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#23 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#25 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#26 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#28 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#30 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#32 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#34 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#35 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#36 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#40 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#43 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#45 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#47 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#49 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#51 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#53 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#55 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#57 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 /var/www/aircargomis/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 /var/www/aircargomis/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#60 {main}
"} 
[2025-06-28 10:44:42] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:44:42] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:44:42] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 10:44:42] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 10:44:42] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-41026672, 706-60497953  
[2025-06-28 10:44:42] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 10:44:42] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 10:44:42] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 10:44:42] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 10:59:36] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 10:59:36] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 10:59:36] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 11:02:52] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 11:02:52] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 11:02:52] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 11:05:32] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9747-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 11:05:32] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9747-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 11:05:32] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9747-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 11:13:17] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:13:17] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:13:17] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:13:17] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 11:13:17] local.INFO: Using unified view: Found 0 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:13:17] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
[2025-06-28 11:13:32] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:13:32] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:13:32] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:13:32] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:13:32] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-41026672, 706-60497953  
[2025-06-28 11:13:32] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:13:32] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:13:32] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:13:32] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:17:20] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:20] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:20] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:17:20] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:17:20] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-41026672, 706-60497953  
[2025-06-28 11:17:20] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:17:20] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:17:20] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:20] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:17:28] local.INFO: AWB 706-41026672 marked as RECEIVED during check-in  
[2025-06-28 11:17:29] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:29] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:29] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:17:29] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:17:29] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-60497953, 706-41026672  
[2025-06-28 11:17:29] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:17:29] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:17:29] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:17:29] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:18:16] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:16] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:16] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:18:16] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:18:16] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-60497953, 706-41026672  
[2025-06-28 11:18:16] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:18:16] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:18:16] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:16] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:18:31] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:31] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:31] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:18:31] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:18:31] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-60497953, 706-41026672  
[2025-06-28 11:18:31] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:18:31] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:18:31] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:18:31] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:20:05] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:20:05] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:20:05] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:20:05] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:20:05] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60606280, 706-60497953, 706-41026672  
[2025-06-28 11:20:05] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:20:05] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:20:05] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:20:05] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:21:40] local.INFO: Created partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280 during check-in  
[2025-06-28 11:21:40] local.INFO: Created partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280 during check-in  
[2025-06-28 11:21:40] local.INFO: Processing legacy partial waybill for MAWB: 706-60606280  
[2025-06-28 11:21:40] local.INFO: Setting is_partial flag for MAWB: 706-60606280  
[2025-06-28 11:21:40] local.INFO: Partial waybill record updated with ID: PWB-bcb375bc-3045-4eed-991a-0b246bd86095  
[2025-06-28 11:21:40] local.INFO: AWB 706-60606280 marked as RECEIVED during check-in  
[2025-06-28 11:21:40] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:21:40] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:21:40] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:21:40] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:21:40] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:21:40] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:21:40] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:21:40] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:21:40] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:21:40] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:21:40] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 11:21:40] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:21:40] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:21:40] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:22:05] local.INFO: Within absolute tolerance: difference 1 <= tolerance 1  
[2025-06-28 11:22:05] local.INFO: Within absolute tolerance: difference 1 <= tolerance 5  
[2025-06-28 11:22:13] local.INFO: Within absolute tolerance: difference 1 <= tolerance 1  
[2025-06-28 11:22:13] local.INFO: Within absolute tolerance: difference 1 <= tolerance 5  
[2025-06-28 11:23:50] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:23:50] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:23:50] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:23:50] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 11:23:50] local.INFO: Using unified view: Found 0 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:23:50] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
[2025-06-28 11:24:20] local.INFO: Within absolute tolerance: difference 1 <= tolerance 1  
[2025-06-28 11:24:20] local.INFO: Within absolute tolerance: difference 1 <= tolerance 5  
[2025-06-28 11:47:21] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 11:47:21] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 11:47:21] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 11:48:16] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 11:48:16] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 11:48:16] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 11:51:11] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:51:11] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:51:11] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 11:51:11] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:51:11] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 11:51:11] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:51:11] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 11:51:11] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:51:11] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:51:11] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:51:11] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 11:55:33] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:55:33] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:55:33] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:55:33] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 11:55:33] local.INFO: Using unified view: Found 0 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 11:55:33] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
[2025-06-28 11:59:49] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:59:49] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:59:49] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 11:59:49] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 11:59:49] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 11:59:49] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 11:59:49] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 11:59:49] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 11:59:49] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 11:59:49] local.INFO: Using unified view: Found 5 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 11:59:49] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 12:39:43] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:43] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:43] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 12:39:43] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:39:43] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 12:39:43] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 12:39:43] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 12:39:43] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 12:39:43] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 12:39:43] local.INFO: Using unified view: Found 7 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:43] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 12:39:54] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:54] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:54] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 12:39:54] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:39:54] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 12:39:54] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 12:39:54] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 12:39:54] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 12:39:54] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 12:39:54] local.INFO: Using unified view: Found 7 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:39:54] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 12:40:33] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:40:33] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:40:33] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 12:40:33] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:40:33] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 12:40:33] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 12:40:33] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 12:40:33] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 12:40:33] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 12:40:33] local.INFO: Using unified view: Found 7 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:40:33] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 12:41:27] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:41:27] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:41:27] local.INFO: Partial shipment detected: 706-60606280  
[2025-06-28 12:41:27] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:41:27] local.INFO: Found partial waybill PWB-bcb375bc-3045-4eed-991a-0b246bd86095 for AWB 706-60606280  
[2025-06-28 12:41:27] local.INFO: Found 10 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672, SHA123, SHA124  
[2025-06-28 12:41:27] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-41026672, 706-60606280  
[2025-06-28 12:41:27] local.INFO: Found 2 house waybills for master AWBs in ULD 01921  
[2025-06-28 12:41:27] local.INFO: Adding 0 unique house waybills to the collection  
[2025-06-28 12:41:27] local.INFO: Using unified view: Found 7 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:41:27] local.INFO: Using unified view: Found 3 Master AWBs and 2 House AWBs  
[2025-06-28 12:41:41] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:41:41] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:41:41] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:41:41] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 12:41:41] local.INFO: Using unified view: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:41:41] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
[2025-06-28 12:42:07] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:42:07] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:42:07] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:42:07] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 12:42:07] local.INFO: Using unified view: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:42:07] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
[2025-06-28 12:44:14] local.INFO: Simple XML Import Output: 2025-06-28 14:44:14,661 - XFFMProcessor - INFO - Starting to process XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114654_XFFM-EK9747_28JUN.xml
2025-06-28 14:44:14,670 - XFFMParser - INFO - Connected to the database
2025-06-28 14:44:14,670 - XFFMParser - INFO - Connected to the database
2025-06-28 14:44:14,670 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 14:44:14,670 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 14:44:14,670 - XFFMProcessor - INFO - Parsing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114654_XFFM-EK9747_28JUN.xml
2025-06-28 14:44:14,670 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114654_XFFM-EK9747_28JUN.xml
2025-06-28 14:44:14,670 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114654_XFFM-EK9747_28JUN.xml
2025-06-28 14:44:14,670 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 14:44:14,670 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 14:44:14,671 - XFFMParser - INFO - XML parsed successfully
2025-06-28 14:44:14,671 - XFFMParser - INFO - XML parsed successfully
2025-06-28 14:44:14,671 - XFFMParser - INFO - Extracting data from XML
2025-06-28 14:44:14,671 - XFFMParser - INFO - Extracting data from XML
2025-06-28 14:44:14,679 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:14,679 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 14:44:14,680 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 14:44:14,681 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:14,681 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:14,681 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 14:44:14,681 - XFFMParser - INFO - AWB 706-60497953 is complete shipment (code: T)
2025-06-28 14:44:14,682 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 14:44:14,682 - XFFMParser - INFO - AWB 706-60606280 is complete shipment (code: T)
2025-06-28 14:44:14,682 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 14:44:14,682 - XFFMParser - INFO - AWB 706-41026672 is complete shipment (code: T)
2025-06-28 14:44:14,682 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 14:44:14,682 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 14:44:14,682 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,682 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,682 - XFFMParser - WARNING - AWB 706-60497953 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,682 - XFFMParser - WARNING - AWB 706-60497953 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,682 - XFFMParser - WARNING - AWB 706-60606280 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,682 - XFFMParser - WARNING - AWB 706-60606280 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,683 - XFFMParser - WARNING - AWB 706-41026672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,683 - XFFMParser - WARNING - AWB 706-41026672 found in both ULDs and standalone waybills - aggregating totals
2025-06-28 14:44:14,683 - XFFMParser - INFO - Processed 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,683 - XFFMParser - INFO - Processed 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,683 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 3 pieces, 111.0 KGM
2025-06-28 14:44:14,683 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 3 pieces, 111.0 KGM
2025-06-28 14:44:14,683 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 3 pieces, 111.0 KGM
2025-06-28 14:44:14,683 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 3 pieces, 111.0 KGM
2025-06-28 14:44:14,683 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 14:44:14,683 - XFFMParser - INFO - Extracted data for manifest: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 14:44:14,683 - XFFMParser - INFO - Validating extracted data
2025-06-28 14:44:14,683 - XFFMParser - INFO - Validating extracted data
2025-06-28 14:44:14,683 - XFFMParser - INFO - Validating 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,683 - XFFMParser - INFO - Validating 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,683 - XFFMParser - WARNING - Validation warning: AWB 706-51634332 is partial shipment (code: P) - will be aggregated (field: waybills)
2025-06-28 14:44:14,683 - XFFMParser - WARNING - Validation warning: AWB 706-51634332 is partial shipment (code: P) - will be aggregated (field: waybills)
2025-06-28 14:44:14,684 - XFFMParser - INFO - Saving data to database
2025-06-28 14:44:14,684 - XFFMParser - INFO - Saving data to database
2025-06-28 14:44:14,685 - XFFMParser - INFO - Storing original XML content (5425 characters) for manifest FM-EK9747-20250628
2025-06-28 14:44:14,685 - XFFMParser - INFO - Storing original XML content (5425 characters) for manifest FM-EK9747-20250628
2025-06-28 14:44:14,687 - XFFMParser - INFO - Saved flight manifest with ID FM-EK9747-20250628
2025-06-28 14:44:14,687 - XFFMParser - INFO - Saved flight manifest with ID FM-EK9747-20250628
2025-06-28 14:44:14,689 - XFFMParser - INFO - Created ULD movement record for 01921: ARRIVAL
2025-06-28 14:44:14,689 - XFFMParser - INFO - Created ULD movement record for 01921: ARRIVAL
2025-06-28 14:44:14,691 - XFFMParser - INFO - Inserted new ULD AWB: 706-51634332 in ULD 01921
2025-06-28 14:44:14,691 - XFFMParser - INFO - Inserted new ULD AWB: 706-51634332 in ULD 01921
2025-06-28 14:44:14,691 - XFFMParser - INFO - Allocated AWB 706-51634332 to ULD 01921
2025-06-28 14:44:14,691 - XFFMParser - INFO - Allocated AWB 706-51634332 to ULD 01921
2025-06-28 14:44:14,691 - XFFMParser - INFO - Processing partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,691 - XFFMParser - INFO - Processing partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,693 - XFFMParser - INFO - MAX sequence query result for AWB 706-51634332: (None,)
2025-06-28 14:44:14,693 - XFFMParser - INFO - MAX sequence query result for AWB 706-51634332: (None,)
2025-06-28 14:44:14,693 - XFFMParser - INFO - Creating new partial AWB 706-51634332 (part 1)
2025-06-28 14:44:14,693 - XFFMParser - INFO - Creating new partial AWB 706-51634332 (part 1)
2025-06-28 14:44:14,694 - XFFMParser - INFO - Created partial waybill PWB-706-51634332-1 for AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,694 - XFFMParser - INFO - Created partial waybill PWB-706-51634332-1 for AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,695 - XFFMParser - INFO - Special handling codes for partial AWB 706-51634332: ['ELI', 'GEN']
2025-06-28 14:44:14,695 - XFFMParser - INFO - Special handling codes for partial AWB 706-51634332: ['ELI', 'GEN']
2025-06-28 14:44:14,696 - XFFMParser - INFO - Inserted new ULD AWB: 706-60497953 in ULD 01921
2025-06-28 14:44:14,696 - XFFMParser - INFO - Inserted new ULD AWB: 706-60497953 in ULD 01921
2025-06-28 14:44:14,696 - XFFMParser - INFO - Allocated AWB 706-60497953 to ULD 01921
2025-06-28 14:44:14,696 - XFFMParser - INFO - Allocated AWB 706-60497953 to ULD 01921
2025-06-28 14:44:14,699 - XFFMParser - INFO - Created master waybill 706-60497953 from XFFM (ID: 1, split code: T)
2025-06-28 14:44:14,699 - XFFMParser - INFO - Created master waybill 706-60497953 from XFFM (ID: 1, split code: T)
2025-06-28 14:44:14,699 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,699 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,700 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,700 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,701 - XFFMParser - INFO - Inserted new ULD AWB: 706-60606280 in ULD 01921
2025-06-28 14:44:14,701 - XFFMParser - INFO - Inserted new ULD AWB: 706-60606280 in ULD 01921
2025-06-28 14:44:14,701 - XFFMParser - INFO - Allocated AWB 706-60606280 to ULD 01921
2025-06-28 14:44:14,701 - XFFMParser - INFO - Allocated AWB 706-60606280 to ULD 01921
2025-06-28 14:44:14,702 - XFFMParser - INFO - Created master waybill 706-60606280 from XFFM (ID: 2, split code: T)
2025-06-28 14:44:14,702 - XFFMParser - INFO - Created master waybill 706-60606280 from XFFM (ID: 2, split code: T)
2025-06-28 14:44:14,703 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,703 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,703 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,703 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,704 - XFFMParser - INFO - Inserted new ULD AWB: 706-41026672 in ULD 01921
2025-06-28 14:44:14,704 - XFFMParser - INFO - Inserted new ULD AWB: 706-41026672 in ULD 01921
2025-06-28 14:44:14,704 - XFFMParser - INFO - Allocated AWB 706-41026672 to ULD 01921
2025-06-28 14:44:14,704 - XFFMParser - INFO - Allocated AWB 706-41026672 to ULD 01921
2025-06-28 14:44:14,705 - XFFMParser - INFO - Created master waybill 706-41026672 from XFFM (ID: 3, split code: T)
2025-06-28 14:44:14,705 - XFFMParser - INFO - Created master waybill 706-41026672 from XFFM (ID: 3, split code: T)
2025-06-28 14:44:14,705 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,705 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,705 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,705 - XFFMParser - WARNING - Special handling code 'COU' not found in master table
2025-06-28 14:44:14,705 - XFFMParser - INFO - Saved ULD 01921 with 4 AWBs
2025-06-28 14:44:14,705 - XFFMParser - INFO - Saved ULD 01921 with 4 AWBs
2025-06-28 14:44:14,705 - XFFMParser - INFO - Processing 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,705 - XFFMParser - INFO - Processing 3 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-60497953 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-60497953 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-60606280 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-60606280 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-41026672 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Skipping complete AWB 706-41026672 - already saved via ULD processing
2025-06-28 14:44:14,705 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,705 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,706 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:14,706 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:14,706 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,706 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:14,706 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:14,706 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:14,706 - XFFMParser - INFO - Saved 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,706 - XFFMParser - INFO - Saved 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:14,708 - XFFMParser - INFO - Successfully saved XFFM data for manifest FM-EK9747-20250628 with 1 ULDs, 4 complete AWBs, and 2 partial AWBs
2025-06-28 14:44:14,708 - XFFMParser - INFO - Successfully saved XFFM data for manifest FM-EK9747-20250628 with 1 ULDs, 4 complete AWBs, and 2 partial AWBs
2025-06-28 14:44:14,709 - XFFMParser - INFO - Successfully processed XFFM for manifest FM-EK9747-20250628
2025-06-28 14:44:14,709 - XFFMParser - INFO - Successfully processed XFFM for manifest FM-EK9747-20250628
2025-06-28 14:44:14,709 - XFFMProcessor - INFO - Parsing completed in 0.04 seconds
2025-06-28 14:44:14,709 - XFFMProcessor - INFO - Successfully processed XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114654_XFFM-EK9747_28JUN.xml
2025-06-28 14:44:14,709 - XFFMProcessor - INFO - Created flight manifest with ID: FM-EK9747-20250628 (Flight: EK9747)
2025-06-28 14:44:14,709 - XFFMProcessor - INFO - Manifest DB ID: FM-EK9747-20250628
2025-06-28 14:44:14,709 - XFFMProcessor - INFO - Total processing time: 0.05 seconds
2025-06-28 14:44:14,709 - XFFMProcessor - WARNING - Processing warnings:
2025-06-28 14:44:14,709 - XFFMProcessor - WARNING -   - AWB 706-51634332 is partial shipment (code: P) - will be aggregated
2025-06-28 14:44:14,709 - XFFMParser - INFO - Database cursor closed
2025-06-28 14:44:14,709 - XFFMParser - INFO - Database cursor closed
2025-06-28 14:44:14,710 - XFFMParser - INFO - Database connection closed
2025-06-28 14:44:14,710 - XFFMParser - INFO - Database connection closed
{"success": true, "manifest_id": "FM-EK9747-20250628", "manifest_db_id": "FM-EK9747-20250628", "flight_number": "EK9747", "carrier_code": "EK", "departure_airport": "NBO", "arrival_airport": "LLW", "uld_count": 1, "uld_ids": [1], "awb_count": 0, "awb_ids": [], "processing_time": 0.04765439033508301, "warnings": ["AWB 706-51634332 is partial shipment (code: P) - will be aggregated"]}
  
[2025-06-28 12:44:34] local.INFO: Simple XML Import Output: 2025-06-28 14:44:34,588 - XFFMProcessor - INFO - Starting to process XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114674_XFFM-EK9750_29JUN.xml
2025-06-28 14:44:34,596 - XFFMParser - INFO - Connected to the database
2025-06-28 14:44:34,596 - XFFMParser - INFO - Connected to the database
2025-06-28 14:44:34,597 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 14:44:34,597 - XFFMParser - INFO - XFFM Parser initialized with modular architecture
2025-06-28 14:44:34,597 - XFFMProcessor - INFO - Parsing XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114674_XFFM-EK9750_29JUN.xml
2025-06-28 14:44:34,597 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114674_XFFM-EK9750_29JUN.xml
2025-06-28 14:44:34,597 - XFFMParser - INFO - Starting to parse XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114674_XFFM-EK9750_29JUN.xml
2025-06-28 14:44:34,597 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 14:44:34,597 - XFFMParser - INFO - Starting to parse XFFM XML string
2025-06-28 14:44:34,597 - XFFMParser - INFO - XML parsed successfully
2025-06-28 14:44:34,597 - XFFMParser - INFO - XML parsed successfully
2025-06-28 14:44:34,597 - XFFMParser - INFO - Extracting data from XML
2025-06-28 14:44:34,597 - XFFMParser - INFO - Extracting data from XML
2025-06-28 14:44:34,604 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:34,604 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:34,605 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:34,605 - XFFMParser - INFO - AWB 706-51634332 marked as partial shipment (code: P)
2025-06-28 14:44:34,605 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 14:44:34,605 - XFFMParser - INFO - Found partial AWB 706-51634332 in ULD 01921 (split code: P) - storing separately
2025-06-28 14:44:34,605 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,605 - XFFMParser - INFO - Found standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,605 - XFFMParser - INFO - Processed 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,605 - XFFMParser - INFO - Processed 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,605 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 10 pieces, 389.0 KGM
2025-06-28 14:44:34,605 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD 01921 - 10 pieces, 389.0 KGM
2025-06-28 14:44:34,605 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 10 pieces, 389.0 KGM
2025-06-28 14:44:34,605 - XFFMParser - INFO - Partial AWB 706-51634332: Split code P in ULD standalone - 10 pieces, 389.0 KGM
2025-06-28 14:44:34,605 - XFFMParser - INFO - Extracted data for manifest: FM-EK9750-20250628 (Flight: EK9750)
2025-06-28 14:44:34,605 - XFFMParser - INFO - Extracted data for manifest: FM-EK9750-20250628 (Flight: EK9750)
2025-06-28 14:44:34,605 - XFFMParser - INFO - Validating extracted data
2025-06-28 14:44:34,605 - XFFMParser - INFO - Validating extracted data
2025-06-28 14:44:34,606 - XFFMParser - INFO - Validating 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,606 - XFFMParser - INFO - Validating 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,606 - XFFMParser - WARNING - Validation warning: AWB 706-51634332 is partial shipment (code: P) - will be aggregated (field: waybills)
2025-06-28 14:44:34,606 - XFFMParser - WARNING - Validation warning: AWB 706-51634332 is partial shipment (code: P) - will be aggregated (field: waybills)
2025-06-28 14:44:34,606 - XFFMParser - INFO - Saving data to database
2025-06-28 14:44:34,606 - XFFMParser - INFO - Saving data to database
2025-06-28 14:44:34,608 - XFFMParser - INFO - Storing original XML content (3239 characters) for manifest FM-EK9750-20250628
2025-06-28 14:44:34,608 - XFFMParser - INFO - Storing original XML content (3239 characters) for manifest FM-EK9750-20250628
2025-06-28 14:44:34,610 - XFFMParser - INFO - Saved flight manifest with ID FM-EK9750-20250628
2025-06-28 14:44:34,610 - XFFMParser - INFO - Saved flight manifest with ID FM-EK9750-20250628
2025-06-28 14:44:34,611 - XFFMParser - INFO - Created ULD movement record for 01921: ARRIVAL
2025-06-28 14:44:34,611 - XFFMParser - INFO - Created ULD movement record for 01921: ARRIVAL
2025-06-28 14:44:34,613 - XFFMParser - INFO - Inserted new ULD AWB: 706-51634332 in ULD 01921
2025-06-28 14:44:34,613 - XFFMParser - INFO - Inserted new ULD AWB: 706-51634332 in ULD 01921
2025-06-28 14:44:34,613 - XFFMParser - INFO - Allocated AWB 706-51634332 to ULD 01921
2025-06-28 14:44:34,613 - XFFMParser - INFO - Allocated AWB 706-51634332 to ULD 01921
2025-06-28 14:44:34,613 - XFFMParser - INFO - Processing partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,613 - XFFMParser - INFO - Processing partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,614 - XFFMParser - INFO - MAX sequence query result for AWB 706-51634332: (1,)
2025-06-28 14:44:34,614 - XFFMParser - INFO - MAX sequence query result for AWB 706-51634332: (1,)
2025-06-28 14:44:34,614 - XFFMParser - INFO - Adding part 2 for existing partial AWB 706-51634332
2025-06-28 14:44:34,614 - XFFMParser - INFO - Adding part 2 for existing partial AWB 706-51634332
2025-06-28 14:44:34,615 - XFFMParser - INFO - Created partial waybill PWB-706-51634332-2 for AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,615 - XFFMParser - INFO - Created partial waybill PWB-706-51634332-2 for AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,615 - XFFMParser - INFO - Special handling codes for partial AWB 706-51634332: ['ELI', 'GEN']
2025-06-28 14:44:34,615 - XFFMParser - INFO - Special handling codes for partial AWB 706-51634332: ['ELI', 'GEN']
2025-06-28 14:44:34,615 - XFFMParser - INFO - Saved ULD 01921 with 1 AWBs
2025-06-28 14:44:34,615 - XFFMParser - INFO - Saved ULD 01921 with 1 AWBs
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,616 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:34,616 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,616 - XFFMParser - INFO - Processing standalone partial AWB 706-51634332 (split code: P)
2025-06-28 14:44:34,616 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:34,616 - XFFMParser - INFO - Partial AWB 706-51634332 with same pieces/weight already exists - skipping duplicate
2025-06-28 14:44:34,616 - XFFMParser - INFO - Saved 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,616 - XFFMParser - INFO - Saved 0 complete AWBs and 2 partial AWBs
2025-06-28 14:44:34,618 - XFFMParser - INFO - Successfully saved XFFM data for manifest FM-EK9750-20250628 with 1 ULDs, 1 complete AWBs, and 2 partial AWBs
2025-06-28 14:44:34,618 - XFFMParser - INFO - Successfully saved XFFM data for manifest FM-EK9750-20250628 with 1 ULDs, 1 complete AWBs, and 2 partial AWBs
2025-06-28 14:44:34,618 - XFFMParser - INFO - Successfully processed XFFM for manifest FM-EK9750-20250628
2025-06-28 14:44:34,618 - XFFMParser - INFO - Successfully processed XFFM for manifest FM-EK9750-20250628
2025-06-28 14:44:34,618 - XFFMProcessor - INFO - Parsing completed in 0.02 seconds
2025-06-28 14:44:34,618 - XFFMProcessor - INFO - Successfully processed XFFM file: /var/www/aircargomis/storage/app/private/xml-imports/1751114674_XFFM-EK9750_29JUN.xml
2025-06-28 14:44:34,618 - XFFMProcessor - INFO - Created flight manifest with ID: FM-EK9750-20250628 (Flight: EK9750)
2025-06-28 14:44:34,618 - XFFMProcessor - INFO - Manifest DB ID: FM-EK9750-20250628
2025-06-28 14:44:34,618 - XFFMProcessor - INFO - Total processing time: 0.03 seconds
2025-06-28 14:44:34,618 - XFFMProcessor - WARNING - Processing warnings:
2025-06-28 14:44:34,618 - XFFMProcessor - WARNING -   - AWB 706-51634332 is partial shipment (code: P) - will be aggregated
2025-06-28 14:44:34,618 - XFFMParser - INFO - Database cursor closed
2025-06-28 14:44:34,618 - XFFMParser - INFO - Database cursor closed
2025-06-28 14:44:34,619 - XFFMParser - INFO - Database connection closed
2025-06-28 14:44:34,619 - XFFMParser - INFO - Database connection closed
{"success": true, "manifest_id": "FM-EK9750-20250628", "manifest_db_id": "FM-EK9750-20250628", "flight_number": "EK9750", "carrier_code": "EK", "departure_airport": "NBO", "arrival_airport": "LLW", "uld_count": 1, "uld_ids": [2], "awb_count": 0, "awb_ids": [], "processing_time": 0.030057430267333984, "warnings": ["AWB 706-51634332 is partial shipment (code: P) - will be aggregated"]}
  
[2025-06-28 12:44:42] local.INFO: Simple XML Import Output: 2025-06-28 14:44:42,927 - XFWBProcessor - INFO - Starting to process XFWB file: /var/www/aircargomis/storage/app/private/xml-imports/1751114682_XFWB-706-51634332.xml
2025-06-28 14:44:42,938 - XFWBParser - INFO - Connected to the database
2025-06-28 14:44:42,938 - XFWBParser - INFO - Connected to the database
2025-06-28 14:44:42,938 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-28 14:44:42,938 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-28 14:44:42,939 - XFWBProcessor - INFO - Parsing XFWB file: /var/www/aircargomis/storage/app/private/xml-imports/1751114682_XFWB-706-51634332.xml
2025-06-28 14:44:42,939 - XFWBParser - INFO - Starting to parse XFWB file: /var/www/aircargomis/storage/app/private/xml-imports/1751114682_XFWB-706-51634332.xml
2025-06-28 14:44:42,939 - XFWBParser - INFO - Starting to parse XFWB file: /var/www/aircargomis/storage/app/private/xml-imports/1751114682_XFWB-706-51634332.xml
2025-06-28 14:44:42,939 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-28 14:44:42,939 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-28 14:44:42,939 - XFWBParser - INFO - XML parsed successfully
2025-06-28 14:44:42,939 - XFWBParser - INFO - XML parsed successfully
2025-06-28 14:44:42,939 - XFWBParser - INFO - Extracting data from XML
2025-06-28 14:44:42,939 - XFWBParser - INFO - Extracting data from XML
2025-06-28 14:44:42,943 - XFWBParser - INFO - Extracted data for AWB: 706-51634332
2025-06-28 14:44:42,943 - XFWBParser - INFO - Extracted data for AWB: 706-51634332
2025-06-28 14:44:42,944 - XFWBParser - INFO - Validating extracted data
2025-06-28 14:44:42,944 - XFWBParser - INFO - Validating extracted data
2025-06-28 14:44:42,945 - XFWBParser - INFO - Saving data to database
2025-06-28 14:44:42,945 - XFWBParser - INFO - Saving data to database
2025-06-28 14:44:42,952 - XFWBParser - INFO - Storing original XML content (7577 characters) for AWB 706-51634332
2025-06-28 14:44:42,952 - XFWBParser - INFO - Storing original XML content (7577 characters) for AWB 706-51634332
2025-06-28 14:44:42,957 - XFWBParser - INFO - Saved master waybill with ID 4
2025-06-28 14:44:42,957 - XFWBParser - INFO - Saved master waybill with ID 4
2025-06-28 14:44:42,959 - XFWBParser - INFO - Created new consignee: DUMMY CONSIGNEE (ID: 1)
2025-06-28 14:44:42,959 - XFWBParser - INFO - Created new consignee: DUMMY CONSIGNEE (ID: 1)
2025-06-28 14:44:42,960 - XFWBParser - INFO - Updated AWB 4 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-28 14:44:42,960 - XFWBParser - INFO - Updated AWB 4 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-28 14:44:42,964 - XFWBParser - INFO - Saved special handling code GEN with ID 1
2025-06-28 14:44:42,964 - XFWBParser - INFO - Saved special handling code GEN with ID 1
2025-06-28 14:44:42,965 - XFWBParser - INFO - Saved special handling code ELI with ID 2
2025-06-28 14:44:42,965 - XFWBParser - INFO - Saved special handling code ELI with ID 2
2025-06-28 14:44:42,966 - XFWBParser - INFO - Updated 2 partial AWB records for AWB 706-51634332 with master totals: 13 pieces, 500.0 weight
2025-06-28 14:44:42,966 - XFWBParser - INFO - Updated 2 partial AWB records for AWB 706-51634332 with master totals: 13 pieces, 500.0 weight
2025-06-28 14:44:42,967 - XFWBParser - INFO -   - Partial AWB PWB-706-51634332-1: 3 pieces, 111.00 weight (now shows total AWB: 13 pieces, 500.0 weight)
2025-06-28 14:44:42,967 - XFWBParser - INFO -   - Partial AWB PWB-706-51634332-1: 3 pieces, 111.00 weight (now shows total AWB: 13 pieces, 500.0 weight)
2025-06-28 14:44:42,967 - XFWBParser - INFO -   - Partial AWB PWB-706-51634332-2: 10 pieces, 389.00 weight (now shows total AWB: 13 pieces, 500.0 weight)
2025-06-28 14:44:42,967 - XFWBParser - INFO -   - Partial AWB PWB-706-51634332-2: 10 pieces, 389.00 weight (now shows total AWB: 13 pieces, 500.0 weight)
2025-06-28 14:44:42,968 - XFWBParser - INFO - Successfully saved XFWB data for AWB 706-51634332
2025-06-28 14:44:42,968 - XFWBParser - INFO - Successfully saved XFWB data for AWB 706-51634332
2025-06-28 14:44:42,968 - XFWBParser - INFO - Successfully processed XFWB for AWB 706-51634332
2025-06-28 14:44:42,968 - XFWBParser - INFO - Successfully processed XFWB for AWB 706-51634332
2025-06-28 14:44:42,968 - XFWBProcessor - INFO - Parsing completed in 0.03 seconds
2025-06-28 14:44:42,968 - XFWBProcessor - INFO - Successfully processed XFWB file: /var/www/aircargomis/storage/app/private/xml-imports/1751114682_XFWB-706-51634332.xml
2025-06-28 14:44:42,968 - XFWBProcessor - INFO - Processed waybill with AWB number: 706-51634332
2025-06-28 14:44:42,968 - XFWBProcessor - INFO - AWB ID: 4
2025-06-28 14:44:42,968 - XFWBProcessor - INFO - Total processing time: 0.04 seconds
2025-06-28 14:44:42,969 - XFWBParser - INFO - Database cursor closed
2025-06-28 14:44:42,969 - XFWBParser - INFO - Database cursor closed
2025-06-28 14:44:42,969 - XFWBParser - INFO - Database connection closed
2025-06-28 14:44:42,969 - XFWBParser - INFO - Database connection closed
2025-06-28 14:44:42,977 - XFWBProcessor - INFO - Profiling results:
         8090 function calls (8041 primitive calls) in 0.042 seconds

   Ordered by: cumulative time
   List reduced from 419 to 30 due to restriction <30>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.029    0.029 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:54(parse_file)
        1    0.000    0.000    0.029    0.029 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:98(parse_string)
        1    0.000    0.000    0.023    0.023 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:41(save_data)
       15    0.000    0.000    0.014    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:36(execute_query)
       15    0.014    0.001    0.014    0.001 {method 'execute' of 'psycopg2.extensions.cursor' objects}
        1    0.000    0.000    0.012    0.012 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:34(__init__)
        1    0.000    0.000    0.011    0.011 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:38(__init__)
        1    0.000    0.000    0.011    0.011 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:95(_connect_to_db)
        1    0.000    0.000    0.011    0.011 /var/www/aircargomis/venv/lib/python3.12/site-packages/psycopg2/__init__.py:80(connect)
        1    0.011    0.011    0.011    0.011 {built-in method psycopg2._psycopg._connect}
        8    0.000    0.000    0.006    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:158(find_record)
        4    0.000    0.000    0.006    0.002 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:80(insert_record)
      6/1    0.000    0.000    0.005    0.005 <frozen importlib._bootstrap>:1349(_find_and_load)
      6/1    0.000    0.000    0.005    0.005 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
       29    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1529(info)
        1    0.000    0.000    0.005    0.005 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:176(save_master_waybill)
       29    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1660(_log)
        1    0.000    0.000    0.005    0.005 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:456(save_cargo_details)
        2    0.000    0.000    0.005    0.003 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:469(save_handling_instruction)
      4/2    0.000    0.000    0.005    0.003 <frozen importlib._bootstrap>:911(_load_unlocked)
     11/3    0.000    0.000    0.005    0.002 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
      4/2    0.000    0.000    0.005    0.002 <frozen importlib._bootstrap_external>:989(exec_module)
      4/2    0.000    0.000    0.005    0.002 {built-in method builtins.exec}
        1    0.000    0.000    0.005    0.005 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:1(<module>)
       29    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1686(handle)
       29    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1746(callHandlers)
        1    0.000    0.000    0.004    0.004 /usr/lib/python3.12/uuid.py:1(<module>)
      102    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1011(handle)
      102    0.000    0.000    0.003    0.000 /usr/lib/python3.12/logging/__init__.py:1148(emit)
        6    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:1240(_find_spec)



{"success": true, "awb_number": "706-51634332", "awb_id": 4, "origin_airport": "DXB", "destination_airport": "LLW", "total_pieces": 13, "total_weight": 500.0, "shipper_name": "DUMMY CONSIGNOR", "consignee_name": "DUMMY CONSIGNEE", "carrier_code": "Unknown", "is_mail": false, "is_human_remains": false, "is_partial": false, "processing_time": 0.04156637191772461, "warnings": []}
  
[2025-06-28 12:44:52] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:44:52] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:44:52] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:45:04] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9747-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:45:04] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9747-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:45:04] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9747-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:45:17] local.INFO: Updating flight manifest status {"id":"FM-EK9747-20250628","validated_data":{"status":"ARRIVED"}} 
[2025-06-28 12:45:17] local.INFO: Calling updateManifestStatus {"id":"FM-EK9747-20250628","status":"ARRIVED","data":{"status":"ARRIVED","updated_by":4}} 
[2025-06-28 12:45:17] local.INFO: Status updated successfully {"manifest_id":"FM-EK9747-20250628","new_status":"ARRIVED"} 
[2025-06-28 12:45:18] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9747-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:45:18] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9747-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:45:18] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9747-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:45:27] local.INFO: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:45:27] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:45:27] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:45:27] local.INFO: Found 8 AWB numbers for ULD 01921 in manifest FM-EK9747-20250628: 706-41026672, 706-51634332, 706-60497953, 706-60606280, 706-51634332, 706-60497953, 706-60606280, 706-41026672  
[2025-06-28 12:45:27] local.INFO: Found 3 master AWB numbers for ULD 01921: 706-60497953, 706-60606280, 706-41026672  
[2025-06-28 12:45:27] local.INFO: Using unified view: Found 4 AWBs for ULD 01921 in manifest FM-EK9747-20250628  
[2025-06-28 12:45:27] local.INFO: Using unified view: Found 3 Master AWBs and 0 House AWBs  
[2025-06-28 12:46:40] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9747-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:46:40] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9747-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:46:40] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9747-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:48:13] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:48:13] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:48:13] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:48:26] local.INFO: Updating flight manifest status {"id":"FM-EK9750-20250628","validated_data":{"status":"ARRIVED"}} 
[2025-06-28 12:48:26] local.INFO: Calling updateManifestStatus {"id":"FM-EK9750-20250628","status":"ARRIVED","data":{"status":"ARRIVED","updated_by":4}} 
[2025-06-28 12:48:26] local.INFO: Status updated successfully {"manifest_id":"FM-EK9750-20250628","new_status":"ARRIVED"} 
[2025-06-28 12:48:27] local.DEBUG: Master Waybills in manifest: {"manifest_id":"FM-EK9750-20250628","masterWaybills":["706-51634332"]} 
[2025-06-28 12:48:27] local.DEBUG: House Waybills retrieved: {"manifest_id":"FM-EK9750-20250628","count":0,"houseWaybills":[]} 
[2025-06-28 12:48:27] local.DEBUG: House AWBs by Master (after deduplication): {"manifest_id":"FM-EK9750-20250628","houseAwbsByMaster":[],"unique_count":0} 
[2025-06-28 12:48:37] local.INFO: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:48:37] local.INFO: Found 0 partial AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:48:37] local.INFO: Found 0 HAWBs for MAWB 706-51634332  
[2025-06-28 12:48:37] local.INFO: Found 2 AWB numbers for ULD 01921 in manifest FM-EK9750-20250628: 706-51634332, 706-51634332  
[2025-06-28 12:48:37] local.INFO: Using unified view: Found 1 AWBs for ULD 01921 in manifest FM-EK9750-20250628  
[2025-06-28 12:48:37] local.INFO: Using unified view: Found 0 Master AWBs and 0 House AWBs  
