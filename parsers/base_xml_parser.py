#!/usr/bin/env python3
"""
Base XML Parser class with common functionality.

This module provides a base class for XML parsers with common functionality
including database connection, logging, error handling, and profiling.
"""

import os
import sys
import logging
import psycopg2
import time
import cProfile
import pstats
import io
from datetime import datetime
from abc import ABC, abstractmethod

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from parsers.config import DATABASE_CONFIG


class BaseXMLParser(ABC):
    """
    Base class for XML parsers with common functionality.
    
    This class provides:
    - Database connection management
    - Logging setup
    - Error handling
    - Profiling capabilities
    - Common utility methods
    """
    
    def __init__(self, user_id=1, branch_id=1, enable_profiling=False):
        """
        Initialize the base parser.
        
        Args:
            user_id (int): User ID for database operations.
            branch_id (int): Branch ID for database operations.
            enable_profiling (bool): Whether to enable profiling.
        """
        self.user_id = user_id
        self.branch_id = branch_id
        self.enable_profiling = enable_profiling
        
        # Initialize logging
        self.logger = self._setup_logging()
        
        # Initialize database connection
        self.db_connection = None
        self.db_cursor = None
        self._connect_to_db()
        
        # Initialize profiling
        self.profiler = None
        if self.enable_profiling:
            self.profiler = cProfile.Profile()
    
    def _setup_logging(self):
        """
        Set up logging for the parser.
        
        Returns:
            logging.Logger: Configured logger instance.
        """
        logger_name = f"{self.__class__.__name__}"
        logger = logging.getLogger(logger_name)
        
        if not logger.handlers:
            # Configure logging format
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            # Add console handler
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # Add file handler
            log_file = f"{logger_name.lower()}.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            logger.setLevel(logging.INFO)
        
        return logger
    
    def _connect_to_db(self):
        """
        Connect to the database using configuration from config module.
        """
        try:
            self.db_connection = psycopg2.connect(**DATABASE_CONFIG)
            self.db_cursor = self.db_connection.cursor()
            self.logger.info("Connected to the database")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise
    
    def start_profiling(self):
        """Start profiling if enabled."""
        if self.enable_profiling and self.profiler:
            self.profiler.enable()
    
    def stop_profiling(self):
        """Stop profiling and log results if enabled."""
        if self.enable_profiling and self.profiler:
            self.profiler.disable()
            s = io.StringIO()
            ps = pstats.Stats(self.profiler, stream=s).sort_stats('cumulative')
            ps.print_stats(30)  # Print top 30 time-consuming functions
            self.logger.info(f"Profiling results:\n{s.getvalue()}")
    
    def close(self):
        """
        Close database connections and clean up resources.
        """
        try:
            if self.db_cursor:
                self.db_cursor.close()
                self.logger.info("Database cursor closed")
            
            if self.db_connection:
                self.db_connection.close()
                self.logger.info("Database connection closed")
        except Exception as e:
            self.logger.error(f"Error closing database connections: {e}")
    
    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):
        """
        Execute a database query with error handling.
        
        Args:
            query (str): SQL query to execute.
            params (tuple): Query parameters.
            fetch_one (bool): Whether to fetch one result.
            fetch_all (bool): Whether to fetch all results.
            
        Returns:
            Query result or None.
        """
        try:
            self.db_cursor.execute(query, params)
            
            if fetch_one:
                return self.db_cursor.fetchone()
            elif fetch_all:
                return self.db_cursor.fetchall()
            
            return None
        except Exception as e:
            self.logger.error(f"Database query error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            raise
    
    def commit_transaction(self):
        """Commit the current database transaction."""
        try:
            self.db_connection.commit()
        except Exception as e:
            self.logger.error(f"Error committing transaction: {e}")
            raise
    
    def rollback_transaction(self):
        """Rollback the current database transaction."""
        try:
            self.db_connection.rollback()
        except Exception as e:
            self.logger.error(f"Error rolling back transaction: {e}")
            raise
    
    @abstractmethod
    def parse_file(self, xml_file_path):
        """
        Parse an XML file. Must be implemented by subclasses.
        
        Args:
            xml_file_path (str): Path to the XML file.
            
        Returns:
            dict: Parsed data.
        """
        pass
    
    @abstractmethod
    def parse_string(self, xml_string):
        """
        Parse an XML string. Must be implemented by subclasses.
        
        Args:
            xml_string (str): XML content as string.
            
        Returns:
            dict: Parsed data.
        """
        pass
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
