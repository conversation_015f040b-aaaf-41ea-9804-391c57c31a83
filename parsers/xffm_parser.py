#!/usr/bin/env python3
"""
XFFM Parser using modular architecture.

This module provides the main XFFM parser class that inherits from BaseXMLParser
and uses modular extractors, validators, and database operations.
"""

import os
import sys

from lxml import etree

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.xffm_operations import XFFMDatabaseOperations
from extractors.xffm_extractor import XFFMExtractor
from parsers.base_xml_parser import BaseXMLParser
from validators.xffm_validator import XFFMValidator


class XFFMParser(BaseXMLParser):
    """
    XFFM (Flight Manifest) parser using modular architecture.

    This parser inherits from BaseXMLParser and uses separate modules for:
    - Data extraction (XFFMExtractor)
    - Data validation (XFFMValidator)
    - Database operations (XFFMDatabaseOperations)
    """

    def __init__(self, user_id=1, branch_id=1, enable_profiling=False):
        """
        Initialize the XFFM parser.

        Args:
            user_id (int): User ID for database operations.
            branch_id (int): Branch ID for database operations.
            enable_profiling (bool): Whether to enable profiling.
        """
        super().__init__(user_id, branch_id, enable_profiling)

        # Initialize modular components
        self.extractor = XFFMExtractor(self.logger)
        self.validator = XFFMValidator(self.logger)
        self.db_operations = XFFMDatabaseOperations(
            self.db_connection, self.db_cursor, user_id, branch_id, self.logger
        )

        self.logger.info("XFFM Parser initialized with modular architecture")

    def parse_file(self, xml_file_path):
        """
        Parse an XFFM XML file.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info(f"Starting to parse XFFM file: {xml_file_path}")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "manifest_id": None,
            "uld_ids": [],
        }

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            return self.parse_string(xml_content)

        except FileNotFoundError:
            error_msg = f"XML file not found: {xml_file_path}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        except Exception as e:
            error_msg = f"Error reading XML file: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_string(self, xml_string):
        """
        Parse an XFFM XML string.

        Args:
            xml_string (str): XML content as string.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info("Starting to parse XFFM XML string")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "manifest_id": None,
            "uld_ids": [],
        }

        try:
            # Parse XML
            root = self.parse_xml_string(xml_string)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            self.logger.info("Extracting data from XML")
            extracted_data = self.extractor.extract(root)

            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Add original XML content to extracted data for storage
            extracted_data["xml_content"] = xml_string

            manifest_id = extracted_data.get("manifest_id", "Unknown")
            flight_number = extracted_data.get("flight_number", "Unknown")
            self.logger.info(
                f"Extracted data for manifest: {manifest_id} (Flight: {flight_number})"
            )

            # Validate data
            self.logger.info("Validating extracted data")
            is_valid = self.validator.validate(extracted_data)

            # Collect validation errors and warnings
            result["errors"].extend(
                [error["message"] for error in self.validator.get_errors()]
            )
            result["warnings"].extend(
                [warning["message"] for warning in self.validator.get_warnings()]
            )

            if not is_valid:
                self.logger.error("Data validation failed")
                result["data"] = (
                    extracted_data  # Include data even if validation failed
                )
                return result

            # Save to database
            self.logger.info("Saving data to database")
            save_result = self.db_operations.save_data(extracted_data)

            if save_result["success"]:
                result["success"] = True
                result["data"] = extracted_data
                result["manifest_id"] = save_result["manifest_id"]
                result["uld_ids"] = save_result["uld_ids"]
                result["errors"].extend(save_result.get("errors", []))
                result["warnings"].extend(save_result.get("warnings", []))

                self.logger.info(
                    f"Successfully processed XFFM for manifest {manifest_id}"
                )
            else:
                result["errors"].extend(save_result.get("errors", []))
                result["data"] = extracted_data
                self.logger.error("Failed to save data to database")

        except Exception as e:
            error_msg = f"Unexpected error during XFFM parsing: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg, exc_info=True)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_xml_string(self, xml_string):
        """
        Parse XML string into an Element tree.

        Args:
            xml_string (str): XML content as string.

        Returns:
            Element: Root element or None if parsing failed.
        """
        try:
            # Remove BOM if present
            if xml_string.startswith("\ufeff"):
                xml_string = xml_string[1:]

            # Parse XML
            parser = etree.XMLParser(strip_cdata=False, recover=True)
            root = etree.fromstring(xml_string.encode("utf-8"), parser)

            self.logger.info("XML parsed successfully")
            return root

        except etree.XMLSyntaxError as e:
            self.logger.error(f"XML syntax error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing XML: {e}")
            return None

    def get_manifest_summary(self, manifest_id):
        """
        Get summary information for a flight manifest.

        Args:
            manifest_id (str): Manifest ID.

        Returns:
            dict: Manifest summary or None if not found.
        """
        try:
            return self.db_operations.get_manifest_summary(manifest_id)
        except Exception as e:
            self.logger.error(f"Error getting manifest summary: {e}")
            return None

    def update_manifest_status(self, manifest_id, status, notes=None):
        """
        Update flight manifest status.

        Args:
            manifest_id (str): Manifest ID.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            rows_affected = self.db_operations.update_manifest_status(
                manifest_id, status, notes
            )
            if rows_affected > 0:
                self.logger.info(
                    f"Updated status for manifest {manifest_id} to {status}"
                )
                return True
            else:
                self.logger.warning(
                    f"No rows affected when updating manifest {manifest_id}"
                )
                return False
        except Exception as e:
            self.logger.error(f"Error updating manifest status: {e}")
            return False

    def validate_xml_only(self, xml_file_path):
        """
        Validate XML file without saving to database.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Validation result.
        """
        result = {"valid": False, "data": None, "errors": [], "warnings": []}

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            # Parse XML
            root = self.parse_xml_string(xml_content)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            extracted_data = self.extractor.extract(root)
            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Validate data
            is_valid = self.validator.validate(extracted_data)

            result["valid"] = is_valid
            result["data"] = extracted_data
            result["errors"] = [
                error["message"] for error in self.validator.get_errors()
            ]
            result["warnings"] = [
                warning["message"] for warning in self.validator.get_warnings()
            ]

        except Exception as e:
            result["errors"].append(f"Error during validation: {e}")

        return result
