#!/usr/bin/env python3
"""
XFZB Parser using modular architecture.

This module provides the main XFZB parser class that inherits from BaseXMLParser
and uses modular extractors, validators, and database operations.
"""

import os
import sys

from lxml import etree

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.xfzb_operations import XFZBDatabaseOperations
from extractors.xfzb_extractor import XFZBExtractor
from parsers.base_xml_parser import BaseXMLParser
from validators.xfzb_validator import XFZBValidator


class XFZBParser(BaseXMLParser):
    """
    XFZB (House Air Waybill) parser using modular architecture.

    This parser inherits from BaseXMLParser and uses separate modules for:
    - Data extraction (XFZBExtractor)
    - Data validation (XFZBValidator)
    - Database operations (XFZBDatabaseOperations)
    """

    def __init__(self, user_id=1, branch_id=1, enable_profiling=False):
        """
        Initialize the XFZB parser.

        Args:
            user_id (int): User ID for database operations.
            branch_id (int): Branch ID for database operations.
            enable_profiling (bool): Whether to enable profiling.
        """
        super().__init__(user_id, branch_id, enable_profiling)

        # Initialize modular components
        self.extractor = XFZBExtractor(self.logger)
        self.validator = XFZBValidator(self.logger)
        self.db_operations = XFZBDatabaseOperations(
            self.db_connection, self.db_cursor, user_id, branch_id, self.logger
        )

        self.logger.info("XFZB Parser initialized with modular architecture")

    def parse_file(self, xml_file_path):
        """
        Parse an XFZB XML file.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info(f"Starting to parse XFZB file: {xml_file_path}")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "hawb_id": None,
        }

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            return self.parse_string(xml_content)

        except FileNotFoundError:
            error_msg = f"XML file not found: {xml_file_path}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        except Exception as e:
            error_msg = f"Error reading XML file: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_string(self, xml_string):
        """
        Parse an XFZB XML string.

        Args:
            xml_string (str): XML content as string.

        Returns:
            dict: Parsing result with extracted data and status.
        """
        self.logger.info("Starting to parse XFZB XML string")

        if self.enable_profiling:
            self.start_profiling()

        result = {
            "success": False,
            "data": None,
            "errors": [],
            "warnings": [],
            "hawb_id": None,
        }

        try:
            # Parse XML
            root = self.parse_xml_string(xml_string)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            self.logger.info("Extracting data from XML")
            extracted_data = self.extractor.extract(root)

            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Add original XML content to extracted data for storage
            extracted_data["xml_content"] = xml_string

            hawb_number = extracted_data.get("hawb_number", "Unknown")
            mawb_number = extracted_data.get("mawb_number", "Unknown")
            self.logger.info(
                f"Extracted data for House AWB: {hawb_number} (Master: {mawb_number})"
            )

            # Validate data
            self.logger.info("Validating extracted data")
            is_valid = self.validator.validate(extracted_data)

            # Collect validation errors and warnings
            result["errors"].extend(
                [error["message"] for error in self.validator.get_errors()]
            )
            result["warnings"].extend(
                [warning["message"] for warning in self.validator.get_warnings()]
            )

            if not is_valid:
                self.logger.error("Data validation failed")
                result["data"] = (
                    extracted_data  # Include data even if validation failed
                )
                return result

            # Save to database
            self.logger.info("Saving data to database")
            save_result = self.db_operations.save_data(extracted_data)

            if save_result["success"]:
                result["success"] = True
                result["data"] = extracted_data
                result["hawb_id"] = save_result["hawb_id"]
                result["errors"].extend(save_result.get("errors", []))
                result["warnings"].extend(save_result.get("warnings", []))

                self.logger.info(
                    f"Successfully processed XFZB for House AWB {hawb_number}"
                )
            else:
                result["errors"].extend(save_result.get("errors", []))
                result["data"] = extracted_data
                self.logger.error("Failed to save data to database")

        except Exception as e:
            error_msg = f"Unexpected error during XFZB parsing: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg, exc_info=True)
        finally:
            if self.enable_profiling:
                self.stop_profiling()

        return result

    def parse_xml_string(self, xml_string):
        """
        Parse XML string into an Element tree.

        Args:
            xml_string (str): XML content as string.

        Returns:
            Element: Root element or None if parsing failed.
        """
        try:
            # Remove BOM if present
            if xml_string.startswith("\ufeff"):
                xml_string = xml_string[1:]

            # Parse XML
            parser = etree.XMLParser(strip_cdata=False, recover=True)
            root = etree.fromstring(xml_string.encode("utf-8"), parser)

            self.logger.info("XML parsed successfully")
            return root

        except etree.XMLSyntaxError as e:
            self.logger.error(f"XML syntax error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing XML: {e}")
            return None

    def get_hawb_summary(self, hawb_number):
        """
        Get summary information for a house AWB.

        Args:
            hawb_number (str): House AWB number.

        Returns:
            dict: House AWB summary or None if not found.
        """
        try:
            return self.db_operations.get_hawb_summary(hawb_number)
        except Exception as e:
            self.logger.error(f"Error getting house AWB summary: {e}")
            return None

    def update_hawb_status(self, hawb_number, status, notes=None):
        """
        Update house AWB status.

        Args:
            hawb_number (str): House AWB number.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            rows_affected = self.db_operations.update_hawb_status(
                hawb_number, status, notes
            )
            if rows_affected > 0:
                self.logger.info(
                    f"Updated status for house AWB {hawb_number} to {status}"
                )
                return True
            else:
                self.logger.warning(
                    f"No rows affected when updating house AWB {hawb_number}"
                )
                return False
        except Exception as e:
            self.logger.error(f"Error updating house AWB status: {e}")
            return False

    def link_to_master_awb(self, hawb_number, mawb_number):
        """
        Link house AWB to a master AWB.

        Args:
            hawb_number (str): House AWB number.
            mawb_number (str): Master AWB number.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            return self.db_operations.link_to_master_awb(hawb_number, mawb_number)
        except Exception as e:
            self.logger.error(f"Error linking house AWB to master AWB: {e}")
            return False

    def validate_xml_only(self, xml_file_path):
        """
        Validate XML file without saving to database.

        Args:
            xml_file_path (str): Path to the XML file.

        Returns:
            dict: Validation result.
        """
        result = {"valid": False, "data": None, "errors": [], "warnings": []}

        try:
            # Read and parse XML file
            with open(xml_file_path, "r", encoding="utf-8") as file:
                xml_content = file.read()

            # Parse XML
            root = self.parse_xml_string(xml_content)
            if root is None:
                result["errors"].append("Failed to parse XML")
                return result

            # Extract data
            extracted_data = self.extractor.extract(root)
            if not extracted_data:
                result["errors"].append("No data extracted from XML")
                return result

            # Add original XML content to extracted data for storage
            extracted_data["xml_content"] = xml_content

            # Validate data
            is_valid = self.validator.validate(extracted_data)

            result["valid"] = is_valid
            result["data"] = extracted_data
            result["errors"] = [
                error["message"] for error in self.validator.get_errors()
            ]
            result["warnings"] = [
                warning["message"] for warning in self.validator.get_warnings()
            ]

        except Exception as e:
            result["errors"].append(f"Error during validation: {e}")

        return result
