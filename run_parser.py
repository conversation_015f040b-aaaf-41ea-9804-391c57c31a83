#!/usr/bin/env python3
"""
Unified command-line interface for XML parsers.
This script provides a unified interface to run all XML parsers (XFFM, XFWB, XFZB).
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("/var/log/xml_parser.log"),
        logging.StreamHandler(sys.stdout),
    ],
)

logger = logging.getLogger("XMLParser")


def process_file(file_path, parser_type=None, branch_id=1, user_id=1):
    """
    Process an XML file using the appropriate parser.

    Args:
        file_path (str): Path to the XML file.
        parser_type (str, optional): Type of parser to use (xffm, xfwb, xfzb).
                                    If None, will be auto-detected from XML content.
        branch_id (int): Branch ID.
        user_id (int): User ID.

    Returns:
        dict: Result of the parsing operation.
    """
    # Auto-detect parser type from XML content if not provided
    if not parser_type:
        # First try auto-detection from XML content
        try:
            from parsers.base_parser import BaseParser

            detected_type = BaseParser.detect_message_type_from_file(file_path)
            if detected_type:
                parser_type = detected_type
                logger.info(f"Auto-detected parser type: {parser_type}")
            else:
                # Fall back to filename-based detection
                filename = os.path.basename(file_path).upper()
                if filename.startswith("XFFM"):
                    parser_type = "xffm"
                elif filename.startswith("XFWB"):
                    parser_type = "xfwb"
                elif filename.startswith("XFZB"):
                    parser_type = "xfzb"
                else:
                    return {
                        "success": False,
                        "error": f"Could not determine parser type from XML content or filename: {filename}",
                    }
                logger.info(f"Fallback to filename-based detection: {parser_type}")
        except Exception as e:
            logger.error(f"Error during auto-detection: {e}")
            return {
                "success": False,
                "error": f"Error during parser type detection: {str(e)}",
            }

    # Import the appropriate parser module
    try:
        if parser_type == "xffm":
            from process_xffm import process_xffm

            result = process_xffm(file_path, branch_id, user_id)
            return result
        elif parser_type == "xfwb":
            from process_xfwb import process_xfwb

            result = process_xfwb(file_path, branch_id, user_id)
            return result
        elif parser_type == "xfzb":
            from process_xfzb import process_xfzb

            result = process_xfzb(file_path, branch_id, user_id)
            return result
        else:
            return {"success": False, "error": f"Unknown parser type: {parser_type}"}
    except Exception as e:
        logger.error(
            f"Error processing file {file_path} with {parser_type} parser: {e}"
        )
        import traceback

        logger.error(traceback.format_exc())
        return {"success": False, "error": str(e)}


def process_directory(directory_path, parser_type=None, branch_id=1, user_id=1):
    """
    Process all XML files in a directory.

    Args:
        directory_path (str): Path to the directory.
        parser_type (str, optional): Type of parser to use (xffm, xfwb, xfzb).
                                    If None, will be determined from each filename.
        branch_id (int): Branch ID.
        user_id (int): User ID.

    Returns:
        dict: Summary of processing results.
    """
    if not os.path.isdir(directory_path):
        return {"success": False, "error": f"Directory not found: {directory_path}"}

    total_files = 0
    successful_files = 0
    failed_files = []
    results = []

    # Process files based on parser type
    if parser_type:
        # Process only files matching the specified parser type
        pattern = parser_type.upper()
        for filename in os.listdir(directory_path):
            if filename.upper().startswith(pattern) and filename.upper().endswith(
                ".XML"
            ):
                file_path = os.path.join(directory_path, filename)
                total_files += 1

                result = process_file(file_path, parser_type, branch_id, user_id)
                if result.get("success", False):
                    successful_files += 1
                    results.append(result)
                else:
                    failed_files.append(
                        {
                            "file": filename,
                            "error": result.get("error", "Unknown error"),
                        }
                    )
    else:
        # Process all XML files and auto-detect parser type
        for filename in os.listdir(directory_path):
            if filename.upper().endswith(".XML"):
                file_path = os.path.join(directory_path, filename)

                total_files += 1
                # Use auto-detection by passing None as parser_type
                result = process_file(file_path, None, branch_id, user_id)

                if result.get("success", False):
                    successful_files += 1
                    results.append(result)
                else:
                    failed_files.append(
                        {
                            "file": filename,
                            "error": result.get("error", "Unknown error"),
                        }
                    )

    return {
        "success": successful_files == total_files,
        "total_files": total_files,
        "successful_files": successful_files,
        "failed_files": failed_files,
        "results": results,
    }


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Process XML files using IATA XML parsers."
    )

    # File or directory input
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("-f", "--file", help="Path to an XML file")
    input_group.add_argument(
        "-d", "--directory", help="Path to a directory containing XML files"
    )

    # Parser type
    parser.add_argument(
        "-t",
        "--type",
        choices=["xffm", "xfwb", "xfzb"],
        help="Type of parser to use (xffm, xfwb, xfzb). If not specified, will be auto-detected from XML content.",
    )

    # Additional parameters
    parser.add_argument("-b", "--branch-id", type=int, default=1, help="Branch ID")
    parser.add_argument("-u", "--user-id", type=int, default=1, help="User ID")
    parser.add_argument("-o", "--output", help="Output file for results (JSON format)")
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Enable verbose output"
    )

    args = parser.parse_args()

    # Set logging level based on verbose flag
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    result = None

    if args.file:
        if not os.path.isfile(args.file):
            logger.error(f"File not found: {args.file}")
            return 1

        logger.info(f"Processing file: {args.file}")
        result = process_file(args.file, args.type, args.branch_id, args.user_id)

    elif args.directory:
        if not os.path.isdir(args.directory):
            logger.error(f"Directory not found: {args.directory}")
            return 1

        logger.info(f"Processing directory: {args.directory}")
        result = process_directory(
            args.directory, args.type, args.branch_id, args.user_id
        )

    # Output results
    if result:
        if args.output:
            with open(args.output, "w") as f:
                json.dump(result, f, indent=2)
            logger.info(f"Results written to {args.output}")
        else:
            print(json.dumps(result, indent=2))

        if not result.get("success", False):
            return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
