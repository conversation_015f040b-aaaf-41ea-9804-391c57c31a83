#!/usr/bin/env python3
"""
Utility functions for XML parsing.
"""

import logging
from datetime import datetime
from dateutil import parser as date_parser

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_element_text(element, xpath, namespaces, default=''):
    """
    Get the text content of an XML element.

    Args:
        element (Element): XML element
        xpath (str): XPath expression
        namespaces (dict): XML namespaces
        default (str): Default value if element not found

    Returns:
        str: Text content of the element
    """
    try:
        result = element.xpath(xpath, namespaces=namespaces)
        if result and len(result) > 0:
            if hasattr(result[0], 'text') and result[0].text is not None:
                return result[0].text.strip()
            elif isinstance(result[0], str):
                return result[0].strip()
    except Exception as e:
        logger.error(f"Error getting element text for {xpath}: {e}")
    return default

def get_element_attr(element, xpath, attr_name, namespaces=None, default=''):
    """
    Get the attribute value of an XML element.

    Args:
        element (Element): XML element
        xpath (str): XPath expression
        attr_name (str): Attribute name
        namespaces (dict): XML namespaces
        default (str): Default value if attribute not found

    Returns:
        str: Attribute value
    """
    try:
        if namespaces:
            result = element.xpath(xpath, namespaces=namespaces)
        else:
            result = element.xpath(xpath)

        if result and len(result) > 0:
            if hasattr(result[0], 'attrib') and attr_name in result[0].attrib:
                return result[0].attrib[attr_name]
            elif hasattr(result[0], 'get') and result[0].get(attr_name) is not None:
                return result[0].get(attr_name)
            elif attr_name in result[0]:
                return result[0][attr_name]
    except Exception as e:
        logger.error(f"Error getting element attribute for {xpath}.{attr_name}: {e}")
    return default

def get_element_children(element, xpath, namespaces):
    """
    Get the child elements of an XML element.

    Args:
        element (Element): XML element
        xpath (str): XPath expression
        namespaces (dict): XML namespaces

    Returns:
        list: Child elements
    """
    try:
        result = element.xpath(xpath, namespaces=namespaces)
        if result:
            return result
    except Exception as e:
        logger.error(f"Error getting element children for {xpath}: {e}")
    return []

def safe_parse_datetime(date_str):
    """
    Safely parse a datetime string.

    Args:
        date_str (str): Datetime string to parse

    Returns:
        datetime: Parsed datetime object or None if parsing fails
    """
    if not date_str:
        return None

    try:
        # Try to parse the datetime string
        return date_parser.parse(date_str)
    except Exception as e:
        logger.error(f"Error parsing datetime '{date_str}': {e}")
        return None
