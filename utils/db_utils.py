#!/usr/bin/env python3
"""
Utility functions for database operations.
"""

import os
import sys
import logging
import psycopg2
from psycopg2.extras import RealDictCursor

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import database configuration
from config.database import DB_CONFIG

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_connection():
    """
    Get a connection to the database.

    Returns:
        Connection: Database connection
    """
    try:
        # Get database connection parameters from DB_CONFIG
        db_host = DB_CONFIG.get('host', 'localhost')
        db_port = DB_CONFIG.get('port', 5432)
        db_name = DB_CONFIG.get('database', 'cargo_handling_system')
        db_user = DB_CONFIG.get('user', 'postgres')
        db_password = DB_CONFIG.get('password', 'postgres')

        # Connect to the database
        conn_string = f"host={db_host} port={db_port} dbname={db_name} user={db_user} password={db_password}"
        conn = psycopg2.connect(conn_string)

        # Set autocommit to True to avoid transaction issues
        conn.autocommit = True

        logger.info(f"Connected to database {db_name} on {db_host}:{db_port}")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

def execute_query(conn, query, params=None):
    """
    Execute a query on the database.

    Args:
        conn (Connection): Database connection
        query (str): SQL query
        params (tuple): Query parameters

    Returns:
        list: Query results
    """
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute(query, params)
        results = cursor.fetchall()
        cursor.close()
        return results
    except Exception as e:
        logger.error(f"Error executing query: {e}")
        raise

def execute_update(conn, query, params=None):
    """
    Execute an update query on the database.

    Args:
        conn (Connection): Database connection
        query (str): SQL query
        params (tuple): Query parameters

    Returns:
        int: Number of rows affected
    """
    try:
        cursor = conn.cursor()
        cursor.execute(query, params)
        rowcount = cursor.rowcount
        conn.commit()
        cursor.close()
        return rowcount
    except Exception as e:
        conn.rollback()
        logger.error(f"Error executing update: {e}")
        raise
