#!/usr/bin/env python3
"""
Verify ULD data in database tables.
"""

import sys
import psycopg2

# Add the parent directory to the path
sys.path.append('/var/www/cargo-mis')

def verify_uld_data():
    """Verify ULD data in database tables."""
    try:
        from python.xml_parsers.config.database import DB_CONFIG
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("ULD Enhancement Database Verification")
        print("=" * 50)
        
        # Check uld_details
        print("\n📦 ULD Details:")
        cursor.execute("""
            SELECT uld_id, uld_type, uld_owner, manifest_id, pieces, weight
            FROM uld_details 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            uld_id, uld_type, uld_owner, manifest_id, pieces, weight = row
            print(f"  ULD {uld_id} ({uld_type}) - {pieces} pieces, {weight} kg - Manifest: {manifest_id}")
        
        # Check uld_awb_allocations
        print("\n📋 ULD AWB Allocations:")
        cursor.execute("""
            SELECT ua.uld_id, ua.awb_number, ua.assigned_pieces, ua.assigned_weight, ua.manifest_id
            FROM uld_awb_allocations ua
            ORDER BY ua.created_at DESC 
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            uld_id, awb_number, pieces, weight, manifest_id = row
            print(f"  ULD {uld_id} ← AWB {awb_number}: {pieces} pieces, {weight} kg")
        
        # Check uld_awbs (detailed AWB data)
        print("\n📄 ULD AWBs (Detailed):")
        cursor.execute("""
            SELECT uld_id, awb_number, pieces, weight, split_code, description
            FROM uld_awbs 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            uld_id, awb_number, pieces, weight, split_code, description = row
            desc_short = (description[:30] + "...") if description and len(description) > 30 else description
            print(f"  ULD {uld_id} - AWB {awb_number}: {pieces} pieces, {weight} kg")
            if split_code:
                print(f"    Split Code: {split_code}")
            if desc_short:
                print(f"    Description: {desc_short}")
        
        # Check uld_movements
        print("\n🚚 ULD Movements:")
        cursor.execute("""
            SELECT uld_id, movement_type, from_status, to_status, movement_time, notes
            FROM uld_movements 
            ORDER BY movement_time DESC 
            LIMIT 10
        """)
        
        for row in cursor.fetchall():
            uld_id, movement_type, from_status, to_status, movement_time, notes = row
            print(f"  ULD {uld_id}: {movement_type} ({from_status} → {to_status}) at {movement_time}")
            if notes:
                print(f"    Notes: {notes}")
        
        # Summary statistics
        print("\n📊 Summary Statistics:")
        
        # Count ULDs by type
        cursor.execute("""
            SELECT uld_type, COUNT(*) as count, SUM(pieces) as total_pieces, SUM(weight) as total_weight
            FROM uld_details 
            GROUP BY uld_type 
            ORDER BY count DESC
        """)
        
        print("  ULD Types:")
        for row in cursor.fetchall():
            uld_type, count, total_pieces, total_weight = row
            print(f"    {uld_type}: {count} ULDs, {total_pieces} pieces, {total_weight} kg")
        
        # Count movements by type
        cursor.execute("""
            SELECT movement_type, COUNT(*) as count
            FROM uld_movements 
            GROUP BY movement_type 
            ORDER BY count DESC
        """)
        
        print("  Movement Types:")
        for row in cursor.fetchall():
            movement_type, count = row
            print(f"    {movement_type}: {count} movements")
        
        cursor.close()
        conn.close()
        
        print("\n✅ ULD data verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database verification error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_uld_data()
