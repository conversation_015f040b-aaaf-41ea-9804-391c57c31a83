lxml-5.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lxml-5.4.0.dist-info/METADATA,sha256=mXSI2nGpmveYNoJAjt5OADE5EtubWFkOv1DOn4vXR5k,3514
lxml-5.4.0.dist-info/RECORD,,
lxml-5.4.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml-5.4.0.dist-info/WHEEL,sha256=8pHnDZvFUPXal97gusf7ZVhDX9QMTBpzGAGy4Uub9cw,113
lxml-5.4.0.dist-info/licenses/LICENSE.txt,sha256=ae20RcEzWoMS1MCScYR-mVbYTw2fck0SU0DMP612eyo,1488
lxml-5.4.0.dist-info/licenses/LICENSES.txt,sha256=QdSd1AaqDhVIptXyGjDWv2OLPNlutyid00jYPtLkA5I,1514
lxml-5.4.0.dist-info/top_level.txt,sha256=NjD988wqaKq512nshNdLt-uDxsjkp4Bh51m6N-dhUrk,5
lxml/ElementInclude.py,sha256=PSLeZFvCa76WHJulPLxcZXJtCI2-4dK2CtqPRiYOAQg,8560
lxml/__init__.py,sha256=wKPEmf7HZLm4A7b7JTYtXo8jTPsdqDkehCy39vJzTs8,574
lxml/__pycache__/ElementInclude.cpython-312.pyc,,
lxml/__pycache__/__init__.cpython-312.pyc,,
lxml/__pycache__/_elementpath.cpython-312.pyc,,
lxml/__pycache__/builder.cpython-312.pyc,,
lxml/__pycache__/cssselect.cpython-312.pyc,,
lxml/__pycache__/doctestcompare.cpython-312.pyc,,
lxml/__pycache__/pyclasslookup.cpython-312.pyc,,
lxml/__pycache__/sax.cpython-312.pyc,,
lxml/__pycache__/usedoctest.cpython-312.pyc,,
lxml/_elementpath.cpython-312-x86_64-linux-gnu.so,sha256=tENrYbXjv7pWSjowS207GLelS_chOx7RwW1afQNThzQ,204992
lxml/_elementpath.py,sha256=5-PzX8ZIxPIvESKDGVL829sejYkvx991FSQyM-J0LqE,10888
lxml/apihelpers.pxi,sha256=zipCUecXzy5INij_qdEEqZoLp9LYB3CJQUzIzFVH1Ow,63610
lxml/builder.cpython-312-x86_64-linux-gnu.so,sha256=lX8J-jceUa1AX9gp9slBAxG094HdtK7ERhOxnH7NVIg,116536
lxml/builder.py,sha256=XD0DQc_G-D950Ym2NwDqxF2v9frtldxdfmvYhxhpP64,8100
lxml/classlookup.pxi,sha256=Tax8Vhbm5C6UCjgmRFsYjW0pFHxIuTthH1MOgASDLgc,22435
lxml/cleanup.pxi,sha256=ZNEpbv7qx_ICPzsxhCaMUHCOfiznOoZ_u3jlYXHAuh4,8454
lxml/cssselect.py,sha256=_wZdX-B9p5MeIYABmENIYRWEkwXwX-7jO8Dkf-1rUZU,3306
lxml/debug.pxi,sha256=zhxiMeNnKtkjn_FWboHk1T-Vtcg_vEGV8jfYK0XyXHM,3282
lxml/docloader.pxi,sha256=bYSZAxxbBEfVzfLXTUWFRfOyUTfV23L7i9hR2dgtSNY,5772
lxml/doctestcompare.py,sha256=40EDnkwpcvW86qNa86990OXF42xdHaosSZoiBsEjkzU,17731
lxml/dtd.pxi,sha256=IAKkmA4ZoC68sqAWcTqoS8jEGYcPQrVMCZgn4iLBYko,15281
lxml/etree.cpython-312-x86_64-linux-gnu.so,sha256=dzTYJ8s5tXIxupRu4_yUIgW6_IiNkBBWW0ADcNgG9d4,5279472
lxml/etree.h,sha256=rrJ2b-3YWpNtCL_j2Wd2VhXtcv9EbDT6Ju3ldIb8fyQ,9912
lxml/etree.pyx,sha256=LhRTPTEvlFT3PA7L4vvidp9HYQVqX78VZEPUj2ENlSE,134517
lxml/etree_api.h,sha256=8qTtQmUdvNqy2grAE8N4BSn-gIMPpiyoe5C4Geo5KzU,17063
lxml/extensions.pxi,sha256=58O6mBbDeWnkiIsRd9T7mLodylo7typz2dbjpJryAyY,32088
lxml/html/ElementSoup.py,sha256=s_dLobLMuKn2DhexR-iDXdZrMFg1RjLy1feHsIeZMpw,320
lxml/html/__init__.py,sha256=ap2X3ZzRaufsJppVEVa7ctw1bYtBfs7FSRBCUIKPyDk,64302
lxml/html/__pycache__/ElementSoup.cpython-312.pyc,,
lxml/html/__pycache__/__init__.cpython-312.pyc,,
lxml/html/__pycache__/_diffcommand.cpython-312.pyc,,
lxml/html/__pycache__/_html5builder.cpython-312.pyc,,
lxml/html/__pycache__/_setmixin.cpython-312.pyc,,
lxml/html/__pycache__/builder.cpython-312.pyc,,
lxml/html/__pycache__/clean.cpython-312.pyc,,
lxml/html/__pycache__/defs.cpython-312.pyc,,
lxml/html/__pycache__/diff.cpython-312.pyc,,
lxml/html/__pycache__/formfill.cpython-312.pyc,,
lxml/html/__pycache__/html5parser.cpython-312.pyc,,
lxml/html/__pycache__/soupparser.cpython-312.pyc,,
lxml/html/__pycache__/usedoctest.cpython-312.pyc,,
lxml/html/_diffcommand.py,sha256=kz_7EP9PmYWuczlZcGiw74_rG0eTKvQ2lrO0rkiwlYE,2081
lxml/html/_html5builder.py,sha256=NLaT-Ev-aBgJpeQl-6ZbJChLZK5GV-znDkHOJD5VQC4,3230
lxml/html/_setmixin.py,sha256=8IFIOLmVz0G-XzsD2tCEkSFWO-dgPBHgvHufC8ni67s,1188
lxml/html/builder.py,sha256=aRgS-Ea9bli-muGX0iUQGKAe9D93P8BspQ2WPuiWJcU,4492
lxml/html/clean.py,sha256=FghSJy4jt2RaBy6dgusowkU18hxpZ4XLE5ceCK9qxyA,503
lxml/html/defs.py,sha256=ZzOp2TmY9f_ein9GIcDPyN8-f5HVptzSj56umimWub4,4236
lxml/html/diff.cpython-312-x86_64-linux-gnu.so,sha256=cs1ZFdVqmgNt5WvgL8PpyA7nmmk-TXadpxEp50eHmzc,360632
lxml/html/diff.py,sha256=n2jMJW8D2OYfG-SKbdmpSjZ3Lwk516WwJfMn6yvQiuM,30393
lxml/html/formfill.py,sha256=umgk0BbkAI1W6q9musFbL-cDnI_aap2NsLBJqk0UmVI,9681
lxml/html/html5parser.py,sha256=dnyC4cqHxywjZSzk0mu2L7THTZjxhg4yF4pncjusa_w,8634
lxml/html/soupparser.py,sha256=xo8VvNeOEb-SChuXLKCRECh8J7HBiJLE9sAbEskoUUQ,10197
lxml/html/usedoctest.py,sha256=tPlmVz4KK1GRKV5DJLrdVECeqsT9PlDzSqqTodVi5s0,249
lxml/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__pycache__/__init__.cpython-312.pyc,,
lxml/includes/c14n.pxd,sha256=DBQcOJ0c_YS245ohMb8fmuEC1kFyv1LrNY_8Mf-syZg,1110
lxml/includes/config.pxd,sha256=H6Mrl8It21hzRI2hzMId9W48QqkYYkoLT4dniLNmdTw,96
lxml/includes/dtdvalid.pxd,sha256=Nv0OykjYehv2lO-Zj--q6jS3TAC_dvQVPSgPMuse1NM,689
lxml/includes/etree_defs.h,sha256=wWC0nsE3NCLBABsKjo_UMBtUJr7yPL7-e8OLc-tGI5o,14245
lxml/includes/etreepublic.pxd,sha256=Bn4d3JkWPqXputXqI-eJ0xmPrwNFPTfDCa7axgjB7FM,10184
lxml/includes/extlibs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/extlibs/__pycache__/__init__.cpython-312.pyc,,
lxml/includes/extlibs/libcharset.h,sha256=GA0FumrbNI4VDGlzq3lf5CLaCwXgn4unw2l0btGQFwI,1510
lxml/includes/extlibs/localcharset.h,sha256=Z_AagaQeq0aDE7NPsVOqEf4nO4KcUp46ggo4d0ONIOQ,6338
lxml/includes/extlibs/zconf.h,sha256=ROVD_0UUx6mgHWSAGcLJqB0RBcv6PHfx-vbNhur6ir0,16464
lxml/includes/extlibs/zlib.h,sha256=ilV5r3LqT0J_8ApBUPDMs_xcHkN59ybhARM7Grn8YAw,96829
lxml/includes/htmlparser.pxd,sha256=9uASkP5dU7OE2lCOLT-z2e01qSbFlp4ehgwdostF_qk,2802
lxml/includes/libexslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libexslt/__pycache__/__init__.cpython-312.pyc,,
lxml/includes/libexslt/exslt.h,sha256=eSW5tMJAewSUANLqk7AGEiU8b2BbCNRyauHnez7nKSU,3114
lxml/includes/libexslt/exsltconfig.h,sha256=QHxzEbRlv_h0USBvpr0Zrl0Muzlc71VCrvgR6lqnLEY,1172
lxml/includes/libexslt/exsltexports.h,sha256=1Jm9KTXm2FUUJIZ6V6-Uw55yG0BMULX3_goyxDd2LL8,1077
lxml/includes/libxml/HTMLparser.h,sha256=bTNBlK1FiuzqrbvURca8Mb8lWlxXdv9FkFNx3sTIl1I,9849
lxml/includes/libxml/HTMLtree.h,sha256=xQMeETDtY8XZnkV9rGkI4UgUIp71I4z2O3R_P1AEwc4,3502
lxml/includes/libxml/SAX.h,sha256=TG2pkIInEA1vLbMfhB02mZhYSoTKxX-m0RaUGmYyzCo,4418
lxml/includes/libxml/SAX2.h,sha256=5fiNvrQmkgIzgZJk-38P1txfs-jIvAcL-_hDAE2pQFs,4430
lxml/includes/libxml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxml/__pycache__/__init__.cpython-312.pyc,,
lxml/includes/libxml/c14n.h,sha256=BSBXw6nIZutC8mWvbRrLLmoWjw3wRt-nM93vjXGMCm8,2742
lxml/includes/libxml/catalog.h,sha256=_aTvqPlvW9WSwGr0UsUr50H3xvLK3KjIT7NEaE8L4Ww,4618
lxml/includes/libxml/chvalid.h,sha256=TZcceNp6Cw0QlYwIqK9GxyYqL5UiAjpQyjt_yrZGTQE,5087
lxml/includes/libxml/debugXML.h,sha256=ipnmZwKQ854tI7Xg1lMJivCu0101mdV3a7MzJ6XTwr0,4934
lxml/includes/libxml/dict.h,sha256=SweaPGMtTTf4je6dNTIoEzcfEvpsAT9_PhR7FC0K-rQ,1770
lxml/includes/libxml/encoding.h,sha256=6IUWcr7rfCv24artcz2aQs2rGgyLWlG9QDKIoJqlX8E,8369
lxml/includes/libxml/entities.h,sha256=eO0-pFUe1i2TobrqrV4IQ1dhDv-rNO0th5BvN1H6c8Y,4907
lxml/includes/libxml/globals.h,sha256=GCp5Iy7IOre0MC1FbFTdpidiSeVFT4fA2t0Facbiw0Q,890
lxml/includes/libxml/hash.h,sha256=KIIpAYKBfGUU3ydWhGehUyfuauZz_Ps0gyambzQo_rc,7017
lxml/includes/libxml/list.h,sha256=oh7iJNQajRA_cHsNk9CcFPYkaW2smf4J_MpedPPjC4k,3128
lxml/includes/libxml/nanoftp.h,sha256=q-OGmv3ltTTTbB_n6ehpGFJpS6wwME-pNTkxcYhHKvo,4013
lxml/includes/libxml/nanohttp.h,sha256=bLbzYjAyAKmP3ComMOPH6XaUImu6bNAESF1HrVtRve0,2124
lxml/includes/libxml/parser.h,sha256=xUWJ0ir_iCXAYoJHvjjmBgSLczpqA2bzqgFP8BuvddM,44153
lxml/includes/libxml/parserInternals.h,sha256=w_UeuoNmPEAL9emdxVVpL3RkjH8g0lpG0VjYy4d5I8c,16810
lxml/includes/libxml/relaxng.h,sha256=miygBGaAxpxiG16kzGWbah9Pl84TunzqKIQf9GcTkhE,5830
lxml/includes/libxml/schemasInternals.h,sha256=V8M4In3zf24EX55Yt4dcfxwp7NpHGYViKnLKwtyrPJ4,26233
lxml/includes/libxml/schematron.h,sha256=8EhPDhvtlMxl9e0C5rSbEruOvzJS5BC_OOFbq9RXZnY,4255
lxml/includes/libxml/threads.h,sha256=vWXv_Nvf7a1iJVZO22J7nCUZkTwM1plW6lL_v9Wckd0,1730
lxml/includes/libxml/tree.h,sha256=rSE1S31puk14dOt7jJcasn7TwPfD-_n7EIZURTb8gJs,38871
lxml/includes/libxml/uri.h,sha256=J9teJHme5z883c4twF5oImEYY-E3xSvhdSGpyRVtvIg,2855
lxml/includes/libxml/valid.h,sha256=USiYQBLcJTjSbahs7e-_0bWUxO709D8rN_MVLVG1Upk,13305
lxml/includes/libxml/xinclude.h,sha256=jyHaV6Q8si8oyv0YESkV9O3ebNQEoD68seoTnX9oBm8,3109
lxml/includes/libxml/xlink.h,sha256=UwJVQvFAax69tdMkePYiAlfkpzTTGZM7hWMyGzEDdwk,5002
lxml/includes/libxml/xmlIO.h,sha256=504sS2fmdXUkP4SRpgTpKOjl5s9cUMF3BPR6p5rethg,12455
lxml/includes/libxml/xmlautomata.h,sha256=6V_99Ozmfjy1EywHByHLBrgvBsItuYoV34qifJDdXDc,3787
lxml/includes/libxml/xmlerror.h,sha256=p9gdPGcrkwC9TI2LKU24Hy_9maBR9FJczvd5-eZ3_cc,37703
lxml/includes/libxml/xmlexports.h,sha256=z5MV1kkrtf3gTR4chqKzK32PAF3okS_lzMuc5Vf8AKM,3280
lxml/includes/libxml/xmlmemory.h,sha256=G2fC02wCvZrbsxMu0oR8NpBq0bqD4immx4ZPbY2hV5Q,4904
lxml/includes/libxml/xmlmodule.h,sha256=y8AmygZrR3wf5UEfyhu6Sf6sn7TTO47g0FUfwdtexA8,1138
lxml/includes/libxml/xmlreader.h,sha256=zY2aVUwZll2MxvC4S47cOStwy-8GHEvKgPztdRfQI-M,12205
lxml/includes/libxml/xmlregexp.h,sha256=AK69btlcPhG_i_6wVB-VABljotbg241WFsL34STKxmg,5149
lxml/includes/libxml/xmlsave.h,sha256=cKguyA0ak_BleqTfxJ6i_OGsdqlLH4cdmnCn8NidnmI,2568
lxml/includes/libxml/xmlschemas.h,sha256=lXHsYTgPFpvSZF8LSFmHlF6EycX1PkzzW7zejosOn34,6902
lxml/includes/libxml/xmlschemastypes.h,sha256=MYwlGmoKAo3lHRaaKgnCXiLmPT9KRjdxyCJ7TEyZ6jM,4583
lxml/includes/libxml/xmlstring.h,sha256=d5PpqxP1I1sfmCUHvVJtjoC9h7hLHcAAQ5ok_Rtf50I,5271
lxml/includes/libxml/xmlunicode.h,sha256=GkO2n9YstUIvh3EB-Wo10-835_jwsPXpMn2rMXuHsAg,11125
lxml/includes/libxml/xmlversion.h,sha256=recdRjJFLercTCNkjzzlzWqojy2Uf9a1XcpFU1r6Ko0,5399
lxml/includes/libxml/xmlwriter.h,sha256=BEUwYNKx3xymDE9vepksEK7yVq9SXYm1d2pQnzlPy90,20688
lxml/includes/libxml/xpath.h,sha256=Xp5oSgnU5mKKUTchUSwyG3YFNpUP1fn7-TFpwRBNYIE,16578
lxml/includes/libxml/xpathInternals.h,sha256=y3F0P7awBk9qhRQmmEgtSDEHoSvbLN_Q2vIifflypSI,18419
lxml/includes/libxml/xpointer.h,sha256=9MKw7uRowx53od3plREL_szioEudglZS6H2EUetBToQ,3647
lxml/includes/libxslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxslt/__pycache__/__init__.cpython-312.pyc,,
lxml/includes/libxslt/attributes.h,sha256=qKwzfGf7r89esLC65s96iYJWRA-s-Ezss2_V6Mmo1hk,957
lxml/includes/libxslt/documents.h,sha256=kBihgH5pqRvFalhm_fOFHtJTFhTpBcm681yT5dxgwfw,2704
lxml/includes/libxslt/extensions.h,sha256=W5UMyJqUP_1zt6sXZ0mgc0gAIwDJrZ8gjByhyrWqvd8,6899
lxml/includes/libxslt/extra.h,sha256=6X3Wu3NdPtrlqz-Koo7dB-rccnnszi6j3zg599gTByg,1640
lxml/includes/libxslt/functions.h,sha256=fc4CZj-9KeBHzO9-WWU_bNqmaEZAz3n7NNwClIBXk14,1972
lxml/includes/libxslt/imports.h,sha256=18kIjoGqdFXR63Ce3ZtzxsTiYV3XGKpchYakMUPDuUI,1840
lxml/includes/libxslt/keys.h,sha256=16v25VEluS7jYhgg6gYFwVxgGMn-1ctnlhhWWT4RcBY,1155
lxml/includes/libxslt/namespaces.h,sha256=VofSn2Kkn-a5JyRKCmY3jPp7amQy3n09vzy0KUQt4q0,1666
lxml/includes/libxslt/numbersInternals.h,sha256=Eg5gYZ5p3h0_e5wyI61S-0E6_ArVJzv0yr63j6BU2fc,2019
lxml/includes/libxslt/pattern.h,sha256=tJ-BPfs9UYgiZMMoQZbhij3g7xVppYq7TrrOu25eR7Q,2110
lxml/includes/libxslt/preproc.h,sha256=D_LjEdHhsdyBnEAvflnwFgoR4hGUb72kgEhXkkmPRsw,896
lxml/includes/libxslt/security.h,sha256=fUD1cy_WxFCTvTNAF0WOQIU4p5CNWn1LHFyZJd-Fx5U,2652
lxml/includes/libxslt/templates.h,sha256=bnt6Jqui6KU5pNUdMNPbQZkZ5d-VTWqC0TMGkOlVoIo,2268
lxml/includes/libxslt/transform.h,sha256=ICT7meUV0OTAx27WaKVrKj-aUmR9LSpTNaOAJd2UStg,6311
lxml/includes/libxslt/variables.h,sha256=cQAgPe4QCcK2uKbWg7Iz-9peM9xWGm7m3M6jQm0sjIA,3143
lxml/includes/libxslt/xslt.h,sha256=wmFx2Q31Pd8Iq2phAQpY9J3QQatb8lWg3gABtqKFgEw,1964
lxml/includes/libxslt/xsltInternals.h,sha256=2EbEKYmnYZq0HjGnUMAlpqnqZJurRXzjlgk5Js1WYaY,57949
lxml/includes/libxslt/xsltconfig.h,sha256=cV5scdRK6xmOHeOg3OCw6hBfcQ_nrtNs_tKefX67304,2910
lxml/includes/libxslt/xsltexports.h,sha256=1-luH-0bCIgBAlKAXhV-dqHBfwOAQNDamiYbxIlTf0k,1124
lxml/includes/libxslt/xsltlocale.h,sha256=ppxGEmJfZIJgwRQzCM0_77p9WNekEWq1NrdYZrQl4IE,942
lxml/includes/libxslt/xsltutils.h,sha256=1eguYgR9-jeNOVlBUktHboaq-VLX6JXraO80TfbARKM,9085
lxml/includes/lxml-version.h,sha256=mv0AHAqDkk6-siiMNCjme9nv-Wt36RzCXO3eVe7bghc,71
lxml/includes/relaxng.pxd,sha256=HzHlQ6mCcf_tj_JZ9NAVJTVAv8ScCkE8Ifq15y3bS0c,2615
lxml/includes/schematron.pxd,sha256=Hob7xh-K-MKqp7WiG8thMagf5EkQzmgfi4ds0EF91JA,1604
lxml/includes/tree.pxd,sha256=Z5mHkABQPlnwu81WTTq7R4fz17rJAkamOvXf_Jp-nxg,20391
lxml/includes/uri.pxd,sha256=3vOXw6AbSPxAM9uo71T1qnfx-wd9ezXLDQtWsb2zX0I,145
lxml/includes/xinclude.pxd,sha256=CuO_XZNB6E2JK1qXXWn11APrjFQV5kA6SMyb77WZn0A,804
lxml/includes/xmlerror.pxd,sha256=pNP5juL4hRHdVdXUWzMspbdtgPqJdHLDu4jns3ItvrI,58016
lxml/includes/xmlparser.pxd,sha256=d79Wmyof6BX_rjBkKYyDXhBBC3O-yCfyhx8PrI0wm5E,11434
lxml/includes/xmlschema.pxd,sha256=OLZPd2WDJyopiXJJyo-dAyyYHaeSYFiMAI4tqIiv-Ik,1702
lxml/includes/xpath.pxd,sha256=e8-ZYUbRG7N1mHETAlknJ_QqAteOosrYLRgpH-OsTkg,5603
lxml/includes/xslt.pxd,sha256=4yl3pOu7pAvsx5Tc-W4IWCoB8wgtSSR62HI1jqu6jko,8241
lxml/isoschematron/__init__.py,sha256=uauerYeKTlWFCJSqieIHhF5l6rYV2myeEJ0Imd1LzRc,13274
lxml/isoschematron/__pycache__/__init__.cpython-312.pyc,,
lxml/isoschematron/resources/rng/iso-schematron.rng,sha256=VsWxPyi3iViJDDbjJJw0wWkEHkLrz9zoCA8zJLor9N4,18337
lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl,sha256=ObebsB8Wt-d3uIA_U5NU85TpnQ3PxPX38TdOAqosMac,3172
lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl,sha256=QweRrIIM-zFcgg98GXA2CaWfIbgVE0XKEeYSfvv67A0,4563
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl,sha256=xSZ_Ekq_I-62ZpiE5AqYYHwFW_qh855zt9V4_s7rbkY,11703
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl,sha256=x42QJ-dxQ1waPzydsCoQnp2Xj15y53nW43O7BuoDRHk,39957
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl,sha256=Tr9BnO6pzjVWwhqJfm10UlvAy95EgfSCz2iMlrVGT6Q,2015
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl,sha256=ue8q_88X4e_jsJizo31GRNBxNhdxkEE9fY20oq0Iqwk,71764
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl,sha256=BBAdsVSi5zAzeGepuN6gS1saQINDqITXKplmmj4dTWg,20382
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt,sha256=OGLiFswuLJEW5EPYKOeoauuCJFEtVa6jyzBE1OcJI98,3310
lxml/iterparse.pxi,sha256=JXvYhSOCaRjT_hYbRGMlJt2rlqx0TiRpN4FE1jQc63w,16521
lxml/lxml.etree.h,sha256=rrJ2b-3YWpNtCL_j2Wd2VhXtcv9EbDT6Ju3ldIb8fyQ,9912
lxml/lxml.etree_api.h,sha256=B_NgpI420F_R1Im5ewrwfUOZKf4tztaJsYzriFzA7fs,17068
lxml/nsclasses.pxi,sha256=5pzNBhBtlqObPdThL9QIGRs1Dxj1qnr0PyXuTCURqTg,9129
lxml/objectify.cpython-312-x86_64-linux-gnu.so,sha256=gXnLMg6U7KCyDAQoxRAKmNV789kjkwiaViQNe1UM2Fg,2986984
lxml/objectify.pyx,sha256=pvaZmS21G-qlNjPuVaB_dgGgi0alxNALo7gFOV8Ispc,75735
lxml/objectpath.pxi,sha256=s5TNG2-EbaWWKLFAiX303B95zK_Ui8ausB__3QvFFGw,11450
lxml/parser.pxi,sha256=LnahQZQl0qCLWtH1NMMHy0_fwG7maRsL-py0q5lauw0,82040
lxml/parsertarget.pxi,sha256=v1PidxRaG5giwXcTDkpBI7PDFmsZuOcK0y9LdkQaY8M,6326
lxml/proxy.pxi,sha256=TPfavn4-ufhVlr-GKciz2qXCTUFtvGdxO-F-h30yIws,23697
lxml/public-api.pxi,sha256=XoP6_cJOEoQIItvE1RiYCKYD1ry4AobaOr4XLo0KSE4,6666
lxml/pyclasslookup.py,sha256=gLD1HM2HtITYYiGzjEOewSwbB7XkVx_NZv_quCt79Oc,92
lxml/readonlytree.pxi,sha256=ddRYczhHieJ4XUvWvTPW9N9oQ8vuKtv7lC1mtE1qvH8,18976
lxml/relaxng.pxi,sha256=3OQ-fZMzP-KF5vM6HTozT_9ee3J0DJnpj9RcHC8LoMw,6339
lxml/sax.cpython-312-x86_64-linux-gnu.so,sha256=IKScwqOOrVDBAQ4p1ZkAjzlGZ65kpB78VLa-8Ua2tGA,186040
lxml/sax.py,sha256=8e9Ef4Cfbwr7AHMGCBBZcotBkOehQtdaCxsVCgWM5_c,9303
lxml/saxparser.pxi,sha256=9nJEnVFFFx7vueUJ3fSeDjxG4gLgrXsDBWVkD_f8U1Q,33322
lxml/schematron.pxi,sha256=hFn1p4egpvvz-Tkk2AH7cbVOavTC-A7mzHABHrGh7g4,5908
lxml/serializer.pxi,sha256=kiHyYa5EK1T_hpsXni-JSa6XuhXlU4BaESP0c-Qkbek,65753
lxml/usedoctest.py,sha256=qRgZKQVcAZcl-zN0AIXVJnOsETUXz2nPXkxuzs1lGgk,230
lxml/xinclude.pxi,sha256=7eBrI_OK47mmrHQ0ixbixRI8pKqQ1nwkMV-OmKUVlD4,2456
lxml/xmlerror.pxi,sha256=K7YIB21sLWdf_2GxyUymRZ5vDr3O4OJ5vGjrklBGa6o,49854
lxml/xmlid.pxi,sha256=5zf9oR6bsCtavGiOmilNyHqYwgG_bnrIabSd2SURtm0,6073
lxml/xmlschema.pxi,sha256=mumNoHni5S3BQPtcmOHRd61KRaVWu4eOie2wQeB0e6E,8490
lxml/xpath.pxi,sha256=aqW24V817dUxps4Gnc8h7Tm3QVlITKvxU5_9WgJUIFg,19132
lxml/xslt.pxi,sha256=R2fpAf0P7GA1Dhjg0-fGVUbxCk8bBAshuoAs4ODwqlc,36023
lxml/xsltext.pxi,sha256=TImDiAPlAezC07P7RY1N9YChA7AuKFH-G53hXdel9yc,11088
