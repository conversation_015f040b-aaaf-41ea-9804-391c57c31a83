#!/usr/bin/env python3
"""
Enhanced Integrated Cargo XML Parser.

This is the enhanced version of the integrated cargo parser that implements
the new business logic requirements:

1. Processing Status Lifecycle (PENDING -> AWAITING_CHECKIN -> CHECKED_IN -> RECONCILED)
2. Expected vs Declared vs Checked-in field separation
3. Partial suffix generation (one per flight arrival)
4. SHA-256 duplicate prevention for files and AWBs
5. In-transit logic based on destination branches
6. Comprehensive logging and audit trails

The system processes:
- XFFM (Flight Manifest XML) - Expected cargo declarations
- XFWB (Master Waybill XML) - Official cargo declarations
- XFZB (House Waybill XML) - House waybill integration
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import DB_CONFIG
from processors.enhanced_xffm_processor import EnhancedXFFMProcessor
from processors.enhanced_xfwb_processor import EnhancedXFWBProcessor
from processors.xfzb_integrated_processor import XFZBIntegratedProcessor  # Use existing XFZB processor
from utils.message_type_detector import MessageTypeDetector

try:
    import psycopg2
    import psycopg2.extras
except ImportError:
    print("Error: psycopg2 not installed. Please install it with: pip install psycopg2-binary")
    sys.exit(1)


class EnhancedIntegratedCargoParser:
    """
    Enhanced integrated cargo XML parser implementing the new business logic.

    Key Features:
    - Processing status lifecycle management
    - Expected/declared/checked-in field separation
    - Partial suffix generation for P and D split types
    - File and AWB content duplicate prevention
    - In-transit logic based on destination branches
    - Comprehensive audit trails and logging
    """

    def __init__(self, branch_id=1, user_id=1, log_level=logging.INFO):
        """
        Initialize the enhanced integrated cargo parser.

        Args:
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            log_level: Logging level
        """
        self.branch_id = branch_id
        self.user_id = user_id

        # Setup logging
        self.logger = self._setup_logging(log_level)

        # Initialize database connection
        self.db_connection = None
        self.db_cursor = None

        # Initialize processors
        self.xffm_processor = None
        self.xfwb_processor = None
        self.xfzb_processor = None

        # Initialize message type detector
        self.message_detector = MessageTypeDetector()

    def _setup_logging(self, log_level):
        """Setup logging configuration."""
        logger = logging.getLogger('EnhancedIntegratedCargoParser')
        logger.setLevel(log_level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def connect_database(self):
        """Establish database connection."""
        try:
            self.db_connection = psycopg2.connect(**DB_CONFIG)
            self.db_cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Initialize enhanced processors
            self.xffm_processor = EnhancedXFFMProcessor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )
            self.xfwb_processor = EnhancedXFWBProcessor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )
            self.xfzb_processor = XFZBIntegratedProcessor(
                self.db_connection, self.db_cursor, self.branch_id, self.user_id, self.logger
            )

            self.logger.info("Database connection established successfully")

        except Exception as e:
            self.logger.error(f"Failed to connect to database: {str(e)}")
            raise

    def disconnect_database(self):
        """Close database connection."""
        try:
            if self.db_cursor:
                self.db_cursor.close()
            if self.db_connection:
                self.db_connection.close()
            self.logger.info("Database connection closed")
        except Exception as e:
            self.logger.error(f"Error closing database connection: {str(e)}")

    def process_file(self, file_path: str, extract_only: bool = False) -> Dict[str, Any]:
        """
        Process a single XML file using the enhanced processors.

        Args:
            file_path (str): Path to XML file
            extract_only (bool): If True, only extract data without processing

        Returns:
            dict: Processing result with statistics and status
        """
        if not os.path.exists(file_path):
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': f"File not found: {file_path}"
            }

        try:
            # Detect message type
            message_type = self.message_detector.detect_from_file(file_path)

            if not message_type:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': 'Could not detect message type'
                }

            # Convert to uppercase for consistency
            message_type = message_type.upper()
            self.logger.info(f"Processing {message_type} file: {file_path}")

            # If extract_only mode, just extract and return data
            if extract_only:
                return self._extract_only(file_path, message_type)

            # Route to appropriate enhanced processor
            if message_type == 'XFFM':
                return self.xffm_processor.process_xffm_file(file_path)
            elif message_type == 'XFWB':
                return self.xfwb_processor.process_xfwb_file(file_path)
            elif message_type == 'XFZB':
                return self.xfzb_processor.process_xfzb_file(file_path)
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': f'Unsupported message type: {message_type}'
                }

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': str(e)
            }

    def _extract_only(self, file_path: str, message_type: str) -> Dict[str, Any]:
        """
        Extract data from XML file without processing.

        Args:
            file_path (str): Path to XML file
            message_type (str): Message type (XFFM, XFWB, XFZB)

        Returns:
            dict: Extraction result
        """
        try:
            if message_type == 'XFFM':
                extractor = self.xffm_processor.extractor
            elif message_type == 'XFWB':
                extractor = self.xfwb_processor.extractor
            elif message_type == 'XFZB':
                extractor = self.xfzb_processor.extractor
            else:
                return {
                    'success': False,
                    'error': f'Unsupported message type for extraction: {message_type}'
                }

            result = extractor.extract_from_file(file_path)

            if result['success']:
                return {
                    'success': True,
                    'message_type': message_type,
                    'data': result['data']
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Extraction failed')
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Extraction error: {str(e)}'
            }

    def get_processing_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        Get enhanced processing statistics.

        Args:
            days (int): Number of days to look back

        Returns:
            dict: Processing statistics
        """
        try:
            stats = {}

            # Get processing status statistics
            self.db_cursor.execute("""
                SELECT processing_status, COUNT(*) as count
                FROM master_waybills
                WHERE branch_id = %s
                GROUP BY processing_status
                ORDER BY processing_status
            """, (self.branch_id,))

            processing_status_stats = self.db_cursor.fetchall()
            stats['processing_status'] = {
                'master_waybills': {row[0]: row[1] for row in processing_status_stats}
            }

            # Get recent processing activity
            self.db_cursor.execute("""
                SELECT message_type, status, COUNT(*) as count,
                       SUM(awbs_processed) as total_awbs,
                       SUM(ulds_processed) as total_ulds,
                       AVG(duration_seconds) as avg_duration
                FROM processing_logs
                WHERE started_at >= NOW() - INTERVAL '%s days'
                AND branch_id = %s
                GROUP BY message_type, status
                ORDER BY message_type, status
            """, (days, self.branch_id))

            recent_activity = self.db_cursor.fetchall()
            stats['recent_activity'] = [dict(row) for row in recent_activity]

            # Get duplicate prevention statistics
            self.db_cursor.execute("""
                SELECT message_type, COUNT(*) as files_processed,
                       SUM(attempt_count) as total_attempts,
                       COUNT(CASE WHEN attempt_count > 1 THEN 1 END) as duplicates_prevented
                FROM duplicate_files
                WHERE first_processed_at >= NOW() - INTERVAL '%s days'
                AND branch_id = %s
                GROUP BY message_type
                ORDER BY message_type
            """, (days, self.branch_id))

            duplicate_stats = self.db_cursor.fetchall()
            stats['duplicate_prevention'] = [dict(row) for row in duplicate_stats]

            return stats

        except Exception as e:
            self.logger.error(f"Error getting processing statistics: {str(e)}")
            return {'error': str(e)}

    def __enter__(self):
        """Context manager entry."""
        self.connect_database()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect_database()


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description='Enhanced Integrated Cargo XML Parser')
    parser.add_argument('file_path', nargs='?', help='Path to XML file to process')
    parser.add_argument('--extract-only', action='store_true',
                       help='Only extract data, do not process')
    parser.add_argument('--format', choices=['json', 'pretty'], default='pretty',
                       help='Output format')
    parser.add_argument('--branch-id', type=int, default=1,
                       help='Branch ID for processing')
    parser.add_argument('--user-id', type=int, default=1,
                       help='User ID for processing')
    parser.add_argument('--stats', action='store_true',
                       help='Show processing statistics')
    parser.add_argument('--days', type=int, default=7,
                       help='Number of days for statistics')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    log_level = logging.DEBUG if args.verbose else logging.INFO

    try:
        with EnhancedIntegratedCargoParser(args.branch_id, args.user_id, log_level) as parser:
            if args.stats:
                # Show processing statistics
                stats = parser.get_processing_statistics(args.days)
                if args.format == 'json':
                    print(json.dumps(stats, indent=2, default=str))
                else:
                    print("\n" + "="*60)
                    print("ENHANCED CARGO XML PARSER - PROCESSING STATISTICS")
                    print("="*60)
                    print(f"Period: Last {args.days} days")
                    print(f"Branch ID: {args.branch_id}")
                    # Processing status summary
                    if 'processing_status' in stats:
                        print("\n📊 PROCESSING STATUS SUMMARY:")
                        for status, count in stats['processing_status']['master_waybills'].items():
                            print(f"   {status}: {count}")

                    # Recent activity
                    if 'recent_activity' in stats:
                        print("\n📈 RECENT ACTIVITY:")
                        for activity in stats['recent_activity']:
                            print(f"   {activity['message_type']} {activity['status']}: "
                                 f"{activity['count']} files, {activity['total_awbs']} AWBs")

                    # Duplicate prevention
                    if 'duplicate_prevention' in stats:
                        print("\n🛡️  DUPLICATE PREVENTION:")
                        for dup_stat in stats['duplicate_prevention']:
                            print(f"   {dup_stat['message_type']}: "
                                 f"{dup_stat['duplicates_prevented']} duplicates prevented")

                return

            if not args.file_path:
                parser.error("file_path is required when not using --stats")

            # Process file
            result = parser.process_file(args.file_path, args.extract_only)

            if args.format == 'json':
                print(json.dumps(result, indent=2, default=str))
            else:
                if result['success']:
                    print(f"✅ Successfully processed: {result['file_name']}")
                    if 'awb_count' in result:
                        print(f"   AWBs processed: {result['awb_count']}")
                    if 'uld_count' in result:
                        print(f"   ULDs processed: {result['uld_count']}")
                    if 'partial_count' in result:
                        print(f"   Partials created: {result['partial_count']}")
                    if 'processing_time_ms' in result:
                        print(f"   Processing time: {result['processing_time_ms']}ms")
                else:
                    print(f"❌ Failed to process: {result['file_name']}")
                    print(f"   Error: {result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
