import unittest
import os
import sys
from lxml import etree as ET

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extractors.xfwb_extractor import XFWBExtractor
from utils.awb_utils import normalize_awb_number, is_valid_awb_format, extract_awb_from_text

class TestRealAWBExtraction(unittest.TestCase):
    def setUp(self):
        self.extractor = XFWBExtractor()
        self.example_file_path = '/var/www/aircargomis/examples/XFWB -706-51723943.xml'
        
        # Check if the example file exists
        if not os.path.exists(self.example_file_path):
            self.skipTest(f"Example file {self.example_file_path} not found")
        
        # Parse the XML file
        try:
            self.tree = ET.parse(self.example_file_path)
            self.root = self.tree.getroot()
        except Exception as e:
            self.skipTest(f"Failed to parse XML file: {e}")
    
    def test_extract_awb_from_real_xml(self):
        """Test AWB extraction from a real XML file"""
        awb = self.extractor.extract_awb_from_xml(self.root)
        self.assertEqual(awb, "706-51723943")
    
    def test_extract_awb_from_text_in_real_xml(self):
        """Test AWB extraction from text in a real XML file"""
        # Convert XML to string
        xml_str = ET.tostring(self.root, encoding='utf-8').decode('utf-8')
        awb = extract_awb_from_text(xml_str)
        self.assertEqual(awb, "706-51723943")
    
    def test_normalize_real_awb(self):
        """Test normalization of the real AWB number"""
        # The AWB number in the file is 706-51723943
        self.assertEqual(normalize_awb_number("706-51723943"), "706-5172394")
    
    def test_is_valid_real_awb_format(self):
        """Test validation of the real AWB format"""
        self.assertTrue(is_valid_awb_format("706-51723943"))

if __name__ == '__main__':
    unittest.main()