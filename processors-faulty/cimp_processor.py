#!/usr/bin/env python3
"""
CIMP Segment Processor for XML Parsers.

This module implements structured processing by CIMP (Cargo Interchange Message Procedures)
segments to ensure industry standard compliance and better data organization.
"""

import logging
import os
import sys
from typing import Any, Dict, List, Optional

from lxml import etree

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validation.validation_framework import ValidationResult


class CIMPSegmentProcessor:
    """Process XML data according to CIMP segment structure."""

    def __init__(self, parser, validation_result: ValidationResult):
        """
        Initialize CIMP processor.

        Args:
            parser: Parent parser instance
            validation_result: ValidationResult instance for storing validation messages
        """
        self.parser = parser
        self.validation_result = validation_result
        self.logger = logging.getLogger(self.__class__.__name__)

        # CIMP segment definitions
        self.segment_definitions = {
            "02": {
                "name": "AWB Consignment Details",
                "required": True,
                "description": "Air Waybill number, type, origin/destination",
            },
            "03": {
                "name": "Flight Booking Information",
                "required": False,
                "description": "Flight details and routing information",
            },
            "04": {
                "name": "Flight Routing",
                "required": False,
                "description": "Detailed routing and transit information",
            },
            "05": {
                "name": "Shipper Information",
                "required": True,
                "description": "Shipper party details and address",
            },
            "06": {
                "name": "Consignee Information",
                "required": True,
                "description": "Consignee party details and address",
            },
            "07": {
                "name": "Agent Information",
                "required": False,
                "description": "Agent and forwarder details",
            },
            "08": {
                "name": "Accounting Information",
                "required": False,
                "description": "Charges and accounting details",
            },
            "09": {
                "name": "Shipment Description",
                "required": False,
                "description": "Goods description and handling instructions",
            },
            "10": {
                "name": "Customs Information",
                "required": False,
                "description": "Customs declarations and regulatory data",
            },
        }

    def process_all_segments(self, root: etree.Element) -> Dict[str, Any]:
        """
        Process all CIMP segments in order.

        Args:
            root: XML root element

        Returns:
            Dictionary containing processed waybill data
        """
        waybill_data = {}

        try:
            # Process segments in order
            self.logger.info("Starting CIMP segment processing")

            # Segment 2: AWB Consignment Details (Mandatory)
            awb_details = self.process_segment_02(root)
            waybill_data.update(awb_details)

            # Segment 3-4: Flight Bookings and Routing
            routing_data = self.process_segment_03_04(root)
            waybill_data.update(routing_data)

            # Segment 5: Shipper (Mandatory)
            shipper_data = self.process_segment_05(root)
            waybill_data.update(shipper_data)

            # Segment 6: Consignee (Mandatory)
            consignee_data = self.process_segment_06(root)
            waybill_data.update(consignee_data)

            # Segment 7: Agent Information
            agent_data = self.process_segment_07(root)
            waybill_data.update(agent_data)

            # Segment 8: Accounting Information
            accounting_data = self.process_segment_08(root)
            waybill_data.update(accounting_data)

            # Segment 9: Shipment Description
            shipment_data = self.process_segment_09(root)
            waybill_data.update(shipment_data)

            # Segment 10: Customs Information
            customs_data = self.process_segment_10(root)
            waybill_data.update(customs_data)

            # Validate segment completeness
            self._validate_segment_completeness(waybill_data)

            self.logger.info("CIMP segment processing completed")

        except Exception as e:
            self.validation_result.add_error(
                "CIMP_PROCESSING_FAILED", f"CIMP segment processing failed: {str(e)}"
            )
            self.logger.error(f"CIMP segment processing failed: {e}")
            raise

        return waybill_data

    def process_segment_02(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 2: AWB Consignment Details."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 2: AWB Consignment Details")

            # Extract AWB number with validation
            awb_number = self._extract_awb_number(root)
            if not awb_number:
                self.validation_result.add_error(
                    "MISSING_AWB",
                    "AWB number is required (CIMP Segment 2)",
                    "awb_number",
                )
            elif not self._validate_awb_format(awb_number):
                self.validation_result.add_error(
                    "INVALID_AWB_FORMAT",
                    f"Invalid AWB format: {awb_number} (CIMP Segment 2)",
                    "awb_number",
                    awb_number,
                )
            else:
                segment_data["awb_number"] = awb_number

            # Extract type code with validation
            type_code = self._extract_type_code(root)
            if type_code not in ["740", "741", "703"]:
                self.validation_result.add_warning(
                    "UNEXPECTED_TYPE_CODE",
                    f"Unexpected type code: {type_code} (CIMP Segment 2)",
                    "type_code",
                    type_code,
                )
            segment_data["type_code"] = type_code

            # Extract origin/destination with validation
            origin = self._extract_origin_location(root)
            destination = self._extract_destination_location(root)

            if not origin:
                self.validation_result.add_error(
                    "MISSING_ORIGIN",
                    "Origin location is required (CIMP Segment 2)",
                    "origin_airport",
                )
            if not destination:
                self.validation_result.add_error(
                    "MISSING_DESTINATION",
                    "Destination location is required (CIMP Segment 2)",
                    "destination_airport",
                )

            segment_data.update(
                {"origin_airport": origin, "destination_airport": destination}
            )

            # Extract weight and pieces
            total_weight = self._extract_total_weight(root)
            total_pieces = self._extract_total_pieces(root)

            if total_weight is not None:
                segment_data["total_weight"] = total_weight
            if total_pieces is not None:
                segment_data["total_pieces"] = total_pieces

        except Exception as e:
            self.validation_result.add_error(
                "SEGMENT_02_PROCESSING_ERROR",
                f"Error processing CIMP Segment 2: {str(e)}",
            )
            self.logger.error(f"Error processing CIMP Segment 2: {e}")

        return segment_data

    def process_segment_03_04(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segments 3-4: Flight Booking and Routing."""
        segment_data = {}

        try:
            self.logger.debug(
                "Processing CIMP Segments 3-4: Flight Booking and Routing"
            )

            # Extract flight information
            flight_details = self._extract_flight_details(root)
            if flight_details:
                segment_data["flight_details"] = flight_details

            # Extract routing information
            routing_info = self._extract_routing_information(root)
            if routing_info:
                segment_data["routing_info"] = routing_info

        except Exception as e:
            self.validation_result.add_warning(
                "SEGMENT_03_04_PROCESSING_ERROR",
                f"Error processing CIMP Segments 3-4: {str(e)}",
            )
            self.logger.warning(f"Error processing CIMP Segments 3-4: {e}")

        return segment_data

    def process_segment_05(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 5: Shipper Information."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 5: Shipper Information")

            # Extract shipper information
            shipper_data = self._extract_shipper_information(root)

            if not shipper_data.get("name"):
                self.validation_result.add_error(
                    "MISSING_SHIPPER",
                    "Shipper name is required (CIMP Segment 5)",
                    "shipper_name",
                )

            # Validate shipper data quality
            self._validate_party_data(shipper_data, "shipper", "CIMP Segment 5")

            # Map to standard field names
            segment_data.update(
                {
                    "shipper_name": shipper_data.get("name"),
                    "shipper_address": shipper_data.get("address"),
                    "shipper_city": shipper_data.get("city"),
                    "shipper_country": shipper_data.get("country"),
                    "shipper_postal_code": shipper_data.get("postal_code"),
                    "shipper_contact": shipper_data.get("contact"),
                    "shipper_account_id": shipper_data.get("account_id"),
                }
            )

        except Exception as e:
            self.validation_result.add_error(
                "SEGMENT_05_PROCESSING_ERROR",
                f"Error processing CIMP Segment 5: {str(e)}",
            )
            self.logger.error(f"Error processing CIMP Segment 5: {e}")

        return segment_data

    def process_segment_06(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 6: Consignee Information."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 6: Consignee Information")

            # Extract consignee information
            consignee_data = self._extract_consignee_information(root)

            if not consignee_data.get("name"):
                self.validation_result.add_error(
                    "MISSING_CONSIGNEE",
                    "Consignee name is required (CIMP Segment 6)",
                    "consignee_name",
                )

            # Validate consignee data quality
            self._validate_party_data(consignee_data, "consignee", "CIMP Segment 6")

            # Map to standard field names
            segment_data.update(
                {
                    "consignee_name": consignee_data.get("name"),
                    "consignee_address": consignee_data.get("address"),
                    "consignee_city": consignee_data.get("city"),
                    "consignee_country": consignee_data.get("country"),
                    "consignee_postal_code": consignee_data.get("postal_code"),
                    "consignee_contact": consignee_data.get("contact"),
                    "consignee_account_id": consignee_data.get("account_id"),
                }
            )

        except Exception as e:
            self.validation_result.add_error(
                "SEGMENT_06_PROCESSING_ERROR",
                f"Error processing CIMP Segment 6: {str(e)}",
            )
            self.logger.error(f"Error processing CIMP Segment 6: {e}")

        return segment_data

    def process_segment_07(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 7: Agent Information."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 7: Agent Information")

            # Extract agent/forwarder information
            agent_data = self._extract_agent_information(root)

            if agent_data:
                segment_data.update(
                    {
                        "agent_name": agent_data.get("name"),
                        "agent_code": agent_data.get("code"),
                        "agent_account_id": agent_data.get("account_id"),
                        "forwarder_name": agent_data.get("forwarder_name"),
                        "forwarder_code": agent_data.get("forwarder_code"),
                    }
                )

        except Exception as e:
            self.validation_result.add_warning(
                "SEGMENT_07_PROCESSING_ERROR",
                f"Error processing CIMP Segment 7: {str(e)}",
            )
            self.logger.warning(f"Error processing CIMP Segment 7: {e}")

        return segment_data

    def process_segment_08(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 8: Accounting Information."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 8: Accounting Information")

            # Extract accounting information
            accounting_data = self._extract_accounting_information(root)

            if accounting_data:
                segment_data.update(
                    {
                        "currency_code": accounting_data.get("currency"),
                        "prepaid_collect_indicator": accounting_data.get(
                            "prepaid_collect"
                        ),
                        "charges": accounting_data.get("charges"),
                        "total_amount": accounting_data.get("total_amount"),
                    }
                )

        except Exception as e:
            self.validation_result.add_warning(
                "SEGMENT_08_PROCESSING_ERROR",
                f"Error processing CIMP Segment 8: {str(e)}",
            )
            self.logger.warning(f"Error processing CIMP Segment 8: {e}")

        return segment_data

    def process_segment_09(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 9: Shipment Description."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 9: Shipment Description")

            # Extract shipment description and handling instructions
            shipment_data = self._extract_shipment_description(root)

            if shipment_data:
                segment_data.update(
                    {
                        "goods_description": shipment_data.get("description"),
                        "summary_description": shipment_data.get("summary"),
                        "special_handling_codes": shipment_data.get("special_handling"),
                        "handling_instructions": shipment_data.get(
                            "handling_instructions"
                        ),
                    }
                )

        except Exception as e:
            self.validation_result.add_warning(
                "SEGMENT_09_PROCESSING_ERROR",
                f"Error processing CIMP Segment 9: {str(e)}",
            )
            self.logger.warning(f"Error processing CIMP Segment 9: {e}")

        return segment_data

    def process_segment_10(self, root: etree.Element) -> Dict[str, Any]:
        """Process CIMP Segment 10: Customs Information."""
        segment_data = {}

        try:
            self.logger.debug("Processing CIMP Segment 10: Customs Information")

            # Extract customs information
            customs_data = self._extract_customs_information(root)

            if customs_data:
                segment_data.update(
                    {
                        "customs_declarations": customs_data.get("declarations"),
                        "regulatory_info": customs_data.get("regulatory"),
                        "export_declarations": customs_data.get("export"),
                        "import_declarations": customs_data.get("import"),
                    }
                )

        except Exception as e:
            self.validation_result.add_warning(
                "SEGMENT_10_PROCESSING_ERROR",
                f"Error processing CIMP Segment 10: {str(e)}",
            )
            self.logger.warning(f"Error processing CIMP Segment 10: {e}")

        return segment_data

    def _validate_segment_completeness(self, waybill_data: Dict[str, Any]) -> None:
        """Validate that all required segments have been processed."""
        required_segments = ["02", "05", "06"]

        for segment_id in required_segments:
            segment_info = self.segment_definitions[segment_id]
            segment_name = segment_info["name"]

            # Check if segment data is present based on expected fields
            if segment_id == "02":  # AWB Details
                if not waybill_data.get("awb_number"):
                    self.validation_result.add_error(
                        "INCOMPLETE_SEGMENT_02",
                        f"Required CIMP Segment 2 ({segment_name}) is incomplete",
                    )
            elif segment_id == "05":  # Shipper
                if not waybill_data.get("shipper_name"):
                    self.validation_result.add_error(
                        "INCOMPLETE_SEGMENT_05",
                        f"Required CIMP Segment 5 ({segment_name}) is incomplete",
                    )
            elif segment_id == "06":  # Consignee
                if not waybill_data.get("consignee_name"):
                    self.validation_result.add_error(
                        "INCOMPLETE_SEGMENT_06",
                        f"Required CIMP Segment 6 ({segment_name}) is incomplete",
                    )

    # Helper methods for extracting data from XML
    # These methods delegate to the parent parser's extraction methods

    def _extract_awb_number(self, root: etree.Element) -> Optional[str]:
        """Extract AWB number from XML."""
        return self.parser.get_element_text(".//ns2:BusinessHeaderDocument/ID", root)

    def _extract_type_code(self, root: etree.Element) -> Optional[str]:
        """Extract type code from XML."""
        return self.parser.get_element_text(
            ".//ns2:MessageHeaderDocument/TypeCode", root
        )

    def _extract_origin_location(self, root: etree.Element) -> Optional[str]:
        """Extract origin location from XML."""
        return self.parser.get_element_text(".//OriginLocation/ID", root)

    def _extract_destination_location(self, root: etree.Element) -> Optional[str]:
        """Extract destination location from XML."""
        return self.parser.get_element_text(".//FinalDestinationLocation/ID", root)

    def _extract_total_weight(self, root: etree.Element) -> Optional[float]:
        """Extract total weight from XML."""
        weight_text = self.parser.get_element_text(
            ".//IncludedTareGrossWeightMeasure", root
        )
        if weight_text:
            try:
                return float(weight_text)
            except ValueError:
                return None
        return None

    def _extract_total_pieces(self, root: etree.Element) -> Optional[int]:
        """Extract total pieces from XML."""
        pieces_text = self.parser.get_element_text(".//TotalPieceQuantity", root)
        if pieces_text:
            try:
                return int(pieces_text)
            except ValueError:
                return None
        return None

    def _validate_awb_format(self, awb_number: str) -> bool:
        """Validate AWB number format."""
        if not awb_number:
            return False

        # Remove hyphens and spaces
        clean_awb = awb_number.replace("-", "").replace(" ", "")

        # Check length and format
        return len(clean_awb) == 11 and clean_awb.isdigit()

    def _validate_party_data(
        self, party_data: Dict[str, Any], party_type: str, segment: str
    ) -> None:
        """Validate party data quality."""
        name = party_data.get("name", "")
        if name and len(name.strip()) < 3:
            self.validation_result.add_warning(
                f"SHORT_{party_type.upper()}_NAME",
                f"{party_type.title()} name seems too short ({segment})",
                f"{party_type}_name",
                name,
            )

    # Placeholder methods for data extraction - implement based on XML structure
    def _extract_flight_details(self, root: etree.Element) -> Optional[Dict[str, Any]]:
        """Extract flight details from XML."""
        # Implementation depends on specific XML structure
        return None

    def _extract_routing_information(
        self, root: etree.Element
    ) -> Optional[Dict[str, Any]]:
        """Extract routing information from XML."""
        # Implementation depends on specific XML structure
        return None

    def _extract_shipper_information(self, root: etree.Element) -> Dict[str, Any]:
        """Extract shipper information from XML."""
        shipper_data = {}

        # Extract from ConsignorParty element
        shipper_element = self.parser.find_element(".//ConsignorParty", root)
        if shipper_element is not None:
            shipper_data["name"] = self.parser.get_element_text("./n", shipper_element)
            shipper_data["account_id"] = self.parser.get_element_text(
                "./AccountID", shipper_element
            )

            # Extract address
            address_element = self.parser.find_element(
                "./PostalStructuredAddress", shipper_element
            )
            if address_element is not None:
                shipper_data["address"] = self.parser.get_element_text(
                    "./StreetName", address_element
                )
                shipper_data["city"] = self.parser.get_element_text(
                    "./CityName", address_element
                )
                shipper_data["country"] = self.parser.get_element_text(
                    "./CountryID", address_element
                )
                shipper_data["postal_code"] = self.parser.get_element_text(
                    "./PostcodeCode", address_element
                )

            # Extract contact
            contact_element = self.parser.find_element(
                "./DefinedTradeContact", shipper_element
            )
            if contact_element is not None:
                phone_element = self.parser.find_element(
                    "./DirectTelephoneCommunication", contact_element
                )
                if phone_element is not None:
                    shipper_data["contact"] = self.parser.get_element_text(
                        "./CompleteNumber", phone_element
                    )

        return shipper_data

    def _extract_consignee_information(self, root: etree.Element) -> Dict[str, Any]:
        """Extract consignee information from XML."""
        consignee_data = {}

        # Extract from ConsigneeParty element
        consignee_element = self.parser.find_element(".//ConsigneeParty", root)
        if consignee_element is not None:
            consignee_data["name"] = self.parser.get_element_text(
                "./n", consignee_element
            )
            consignee_data["account_id"] = self.parser.get_element_text(
                "./AccountID", consignee_element
            )

            # Extract address
            address_element = self.parser.find_element(
                "./PostalStructuredAddress", consignee_element
            )
            if address_element is not None:
                consignee_data["address"] = self.parser.get_element_text(
                    "./StreetName", address_element
                )
                consignee_data["city"] = self.parser.get_element_text(
                    "./CityName", address_element
                )
                consignee_data["country"] = self.parser.get_element_text(
                    "./CountryID", address_element
                )
                consignee_data["postal_code"] = self.parser.get_element_text(
                    "./PostcodeCode", address_element
                )

            # Extract contact
            contact_element = self.parser.find_element(
                "./DefinedTradeContact", consignee_element
            )
            if contact_element is not None:
                phone_element = self.parser.find_element(
                    "./DirectTelephoneCommunication", contact_element
                )
                if phone_element is not None:
                    consignee_data["contact"] = self.parser.get_element_text(
                        "./CompleteNumber", phone_element
                    )

        return consignee_data

    def _extract_agent_information(
        self, root: etree.Element
    ) -> Optional[Dict[str, Any]]:
        """Extract agent information from XML."""
        agent_data = {}

        # Extract from FreightForwarderParty element
        agent_element = self.parser.find_element(".//FreightForwarderParty", root)
        if agent_element is not None:
            agent_data["name"] = self.parser.get_element_text("./n", agent_element)
            agent_data["account_id"] = self.parser.get_element_text(
                "./AccountID", agent_element
            )
            agent_data["code"] = self.parser.get_element_text(
                "./CargoAgentID", agent_element
            )

            return agent_data

        return None

    def _extract_accounting_information(
        self, root: etree.Element
    ) -> Optional[Dict[str, Any]]:
        """Extract accounting information from XML."""
        accounting_data = {}

        # Extract currency
        currency_element = self.parser.find_element(
            ".//ApplicableOriginCurrencyExchange", root
        )
        if currency_element is not None:
            accounting_data["currency"] = self.parser.get_element_text(
                "./SourceCurrencyCode", currency_element
            )

        # Extract prepaid/collect indicator
        prepaid_element = self.parser.find_element(
            ".//TotalChargePrepaidIndicator", root
        )
        if prepaid_element is not None and prepaid_element.text:
            accounting_data["prepaid_collect"] = (
                "P" if prepaid_element.text.lower() == "true" else "C"
            )

        return accounting_data if accounting_data else None

    def _extract_shipment_description(
        self, root: etree.Element
    ) -> Optional[Dict[str, Any]]:
        """Extract shipment description from XML."""
        shipment_data = {}

        # Extract goods description
        description_element = self.parser.find_element(
            ".//NatureIdentificationTransportCargo/Identification", root
        )
        if description_element is not None:
            shipment_data["description"] = description_element.text

        # Extract summary description (for house waybills)
        summary_element = self.parser.find_element(".//SummaryDescription", root)
        if summary_element is not None:
            shipment_data["summary"] = summary_element.text

        # Extract special handling codes
        sph_elements = self.parser.find_elements(
            ".//HandlingSPHInstructions/DescriptionCode", root
        )
        if sph_elements:
            shipment_data["special_handling"] = [
                elem.text for elem in sph_elements if elem.text
            ]

        # Extract handling instructions
        osi_elements = self.parser.find_elements(
            ".//HandlingOSIInstructions/Description", root
        )
        if osi_elements:
            shipment_data["handling_instructions"] = [
                elem.text for elem in osi_elements if elem.text
            ]

        return shipment_data if shipment_data else None

    def _extract_customs_information(
        self, root: etree.Element
    ) -> Optional[Dict[str, Any]]:
        """Extract customs information from XML."""
        customs_data = {}

        # Extract customs notes
        customs_elements = self.parser.find_elements(".//IncludedCustomsNote", root)
        if customs_elements:
            declarations = []
            for customs_elem in customs_elements:
                declaration = {
                    "content_code": self.parser.get_element_text(
                        "./ContentCode", customs_elem
                    ),
                    "content": self.parser.get_element_text("./Content", customs_elem),
                    "subject_code": self.parser.get_element_text(
                        "./SubjectCode", customs_elem
                    ),
                    "country_id": self.parser.get_element_text(
                        "./CountryID", customs_elem
                    ),
                }
                declarations.append(declaration)
            customs_data["declarations"] = declarations

        return customs_data if customs_data else None
