#!/usr/bin/env python3
"""
Enhanced XFFM (Flight Manifest) Processor.

This processor implements the new business logic requirements:
- XFFM files processed first to register flight-level declarations
- Expected pieces, weight, and volume tracking from manifest
- Partial suffix generation for P and D split types
- In-transit logic based on destination branches
- Duplicate prevention using SHA-256 hashing
- Processing status lifecycle management
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

from extractors.xffm_extractor import XFFMExtractor
from database.enhanced_operations import EnhancedDatabaseOperations


class EnhancedXFFMProcessor:
    """
    Enhanced XFFM processor implementing the new cargo manifest processing logic.
    
    Key Features:
    - File-level duplicate prevention
    - AWB content duplicate prevention
    - Partial suffix generation (one per flight arrival)
    - Expected pieces tracking from manifest declarations
    - In-transit logic based on destination branches
    - Processing status lifecycle management
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize enhanced XFFM processor.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID for in-transit logic
            user_id (int): Current user ID for audit trails
            logger: Logger instance
        """
        self.db_connection = db_connection
        self.db_cursor = db_cursor
        self.branch_id = branch_id
        self.user_id = user_id
        self.logger = logger

        # Initialize database operations
        self.db_ops = EnhancedDatabaseOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )

        # Initialize extractor
        self.extractor = XFFMExtractor(logger=logger)

    def process_xffm_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process XFFM file with enhanced business logic.

        Args:
            file_path (str): Path to XFFM XML file

        Returns:
            dict: Processing result with statistics and status
        """
        start_time = time.time()
        file_name = os.path.basename(file_path)
        
        # Start processing log
        log_id = self.db_ops.start_processing_log(file_name, 'XFFM')

        try:
            # Check for file-level duplicates
            file_hash = self.db_ops.generate_file_hash(file_path)
            existing_file = self.db_ops.check_file_duplicate(file_hash)
            
            if existing_file:
                self.db_ops.record_duplicate_attempt(file_hash)
                self.db_ops.mark_processing_duplicate(log_id)
                
                self.logger.info(f"Duplicate XFFM file detected: {file_name} "
                               f"(first processed: {existing_file['first_processed_at']})")
                
                return {
                    'success': False,
                    'file_name': file_name,
                    'message': 'Duplicate file detected',
                    'duplicate_info': existing_file,
                    'log_id': log_id
                }

            # Record file as being processed
            file_size = os.path.getsize(file_path)
            self.db_ops.record_file_processed(file_path, file_hash, 'XFFM', file_size)

            # Extract data from XML
            extraction_result = self.extractor.extract_from_file(file_path)
            
            if not extraction_result['success']:
                error_msg = f"XML extraction failed: {extraction_result.get('error', 'Unknown error')}"
                self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
                
                return {
                    'success': False,
                    'file_name': file_name,
                    'error': error_msg,
                    'log_id': log_id
                }

            # Process the extracted data
            processing_result = self._process_flight_manifest(
                extraction_result['data'], 
                file_name, 
                log_id
            )

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            processing_result['processing_time_ms'] = processing_time_ms

            # Complete processing log
            self.db_ops.complete_processing_log(
                log_id,
                processing_result['success'],
                processing_result.get('awb_count', 0),
                processing_result.get('uld_count', 0),
                processing_result.get('error'),
                {
                    'manifest_id': processing_result.get('manifest_id'),
                    'awbs_processed': processing_result.get('awb_count', 0),
                    'ulds_processed': processing_result.get('uld_count', 0),
                    'partials_created': processing_result.get('partial_count', 0),
                    'duplicates_skipped': processing_result.get('duplicates_skipped', 0)
                }
            )

            return processing_result

        except Exception as e:
            error_msg = f"XFFM processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
            
            return {
                'success': False,
                'file_name': file_name,
                'error': error_msg,
                'log_id': log_id
            }

    def _process_flight_manifest(self, data: Dict[str, Any], file_name: str, log_id: str) -> Dict[str, Any]:
        """
        Process flight manifest data with enhanced business logic.

        Args:
            data (dict): Extracted XFFM data
            file_name (str): Source file name
            log_id (str): Processing log ID

        Returns:
            dict: Processing result
        """
        result = {
            'success': True,
            'file_name': file_name,
            'log_id': log_id,
            'manifest_id': None,
            'awb_count': 0,
            'uld_count': 0,
            'partial_count': 0,
            'duplicates_skipped': 0,
            'errors': []
        }

        try:
            # Get flight information
            flight_info = data.get('flight_info', {})
            manifest_id = flight_info.get('manifest_id')
            
            if not manifest_id:
                raise ValueError("Manifest ID not found in XFFM data")
            
            result['manifest_id'] = manifest_id

            # Process ULDs and their AWBs
            ulds = data.get('ulds', [])
            for uld_data in ulds:
                try:
                    # Process ULD
                    uld_result = self._process_uld(uld_data, manifest_id, file_name)
                    result['uld_count'] += 1
                    result['awb_count'] += uld_result['awb_count']
                    result['partial_count'] += uld_result['partial_count']
                    result['duplicates_skipped'] += uld_result['duplicates_skipped']
                    
                except Exception as e:
                    error_msg = f"Error processing ULD {uld_data.get('uld_id', 'unknown')}: {str(e)}"
                    self.logger.error(error_msg)
                    result['errors'].append(error_msg)

            # Process loose AWBs (not in ULDs)
            loose_awbs = data.get('waybills', [])
            for awb_data in loose_awbs:
                try:
                    awb_result = self._process_awb(awb_data, None, manifest_id, file_name)
                    if awb_result['processed']:
                        result['awb_count'] += 1
                        if awb_result['partial_created']:
                            result['partial_count'] += 1
                    else:
                        result['duplicates_skipped'] += 1
                        
                except Exception as e:
                    error_msg = f"Error processing AWB {awb_data.get('awb_number', 'unknown')}: {str(e)}"
                    self.logger.error(error_msg)
                    result['errors'].append(error_msg)

            # Commit transaction
            self.db_connection.commit()

            self.logger.info(f"XFFM processing completed: {result['awb_count']} AWBs, "
                           f"{result['uld_count']} ULDs, {result['partial_count']} partials created")

        except Exception as e:
            # Rollback transaction on error
            self.db_connection.rollback()
            result['success'] = False
            result['error'] = str(e)
            self.logger.error(f"Flight manifest processing failed: {str(e)}", exc_info=True)

        return result

    def _process_uld(self, uld_data: Dict[str, Any], manifest_id: str, file_name: str) -> Dict[str, Any]:
        """
        Process ULD and its AWBs.

        Args:
            uld_data (dict): ULD data
            manifest_id (str): Manifest ID
            file_name (str): Source file name

        Returns:
            dict: ULD processing result
        """
        result = {
            'awb_count': 0,
            'partial_count': 0,
            'duplicates_skipped': 0
        }

        # Create ULD record
        self.db_ops.create_uld_record_enhanced(uld_data, manifest_id)

        # Process AWBs in this ULD
        awbs = uld_data.get('awbs', [])
        for awb_data in awbs:
            awb_result = self._process_awb(awb_data, uld_data, manifest_id, file_name)
            if awb_result['processed']:
                result['awb_count'] += 1
                if awb_result['partial_created']:
                    result['partial_count'] += 1
            else:
                result['duplicates_skipped'] += 1

        return result

    def _process_awb(self, awb_data: Dict[str, Any], uld_data: Optional[Dict[str, Any]], 
                    manifest_id: str, file_name: str) -> Dict[str, Any]:
        """
        Process individual AWB with enhanced business logic.

        Args:
            awb_data (dict): AWB data
            uld_data (dict): ULD data if AWB is in ULD
            manifest_id (str): Manifest ID
            file_name (str): Source file name

        Returns:
            dict: AWB processing result
        """
        awb_number = awb_data.get('awb_number')
        if not awb_number:
            raise ValueError("AWB number not found in AWB data")

        # Check for AWB content duplicates
        content_hash = self.db_ops.generate_awb_content_hash(awb_data)
        if self.db_ops.check_awb_content_duplicate(awb_number, content_hash):
            self.logger.info(f"Duplicate AWB content detected: {awb_number}")
            return {'processed': False, 'partial_created': False, 'reason': 'duplicate_content'}

        # Record AWB as processed
        self.db_ops.record_awb_processed(awb_number, content_hash, manifest_id, file_name)

        # Add manifest and flight info to AWB data
        awb_data['manifest_id'] = manifest_id
        
        # Handle split types and create records
        split_result = self.db_ops.handle_split_types(awb_data, uld_data, manifest_id)

        return {
            'processed': True,
            'partial_created': split_result['partial_created'],
            'split_type': split_result['split_type'],
            'suffix': split_result.get('suffix')
        }
