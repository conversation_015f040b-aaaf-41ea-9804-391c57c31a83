#!/usr/bin/env python3
"""
Integrated Cargo XML Parser - Simplified Version.

This is a simplified orchestrator that uses the tested individual parser scripts:
- process_xffm.py for XFFM (Flight Manifest XML) files
- process_xfwb.py for XFWB (Master Waybill XML) files  
- process_xfzb.py for XFZB (House Waybill XML) files

This ensures consistency between command-line usage and Laravel web interface.
"""

import argparse
import json
import logging
import os
import sys
import time
from pathlib import Path

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.message_type_detector import MessageTypeDetector


class IntegratedCargoParser:
    """
    Simplified orchestrator that delegates to tested individual parser scripts.
    """

    def __init__(self, branch_id=1, user_id=1, log_level=logging.INFO):
        """
        Initialize the integrated cargo parser.

        Args:
            branch_id (int): Current branch ID
            user_id (int): Current user ID
            log_level: Logging level
        """
        self.branch_id = branch_id
        self.user_id = user_id

        # Setup logging
        self.logger = self._setup_logging(log_level)

        # Initialize message type detector
        self.message_detector = MessageTypeDetector()

    def _setup_logging(self, log_level):
        """Setup logging configuration."""
        logger = logging.getLogger('IntegratedCargoParser')
        logger.setLevel(log_level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def process_file(self, file_path: str) -> dict:
        """
        Process a single XML file using the appropriate tested parser script.

        Args:
            file_path (str): Path to XML file

        Returns:
            dict: Processing result with statistics and status
        """
        if not os.path.exists(file_path):
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': f"File not found: {file_path}"
            }

        try:
            # Detect message type
            message_type = self.message_detector.detect_from_file(file_path)

            if not message_type:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': 'Could not detect message type'
                }

            # Convert to uppercase for consistency
            message_type = message_type.upper()
            self.logger.info(f"Processing {message_type} file: {file_path}")

            # Delegate to the appropriate tested parser script
            if message_type == 'XFFM':
                return self._call_parser_script('process_xffm.py', file_path)
            elif message_type == 'XFWB':
                return self._call_parser_script('process_xfwb.py', file_path)
            elif message_type == 'XFZB':
                return self._call_parser_script('process_xfzb.py', file_path)
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': f'Unsupported message type: {message_type}'
                }

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': str(e)
            }

    def _call_parser_script(self, script_name: str, file_path: str) -> dict:
        """
        Call the appropriate parser script and return its result.

        Args:
            script_name (str): Name of the parser script to call
            file_path (str): Path to XML file

        Returns:
            dict: Parser result
        """
        try:
            # Import the appropriate parser function
            if script_name == 'process_xffm.py':
                from process_xffm import process_xffm
                return process_xffm(file_path, self.branch_id, self.user_id)
            elif script_name == 'process_xfwb.py':
                from process_xfwb import process_xfwb
                return process_xfwb(file_path, self.branch_id, self.user_id)
            elif script_name == 'process_xfzb.py':
                from process_xfzb import process_xfzb
                return process_xfzb(file_path, self.branch_id, self.user_id)
            else:
                return {
                    'success': False,
                    'file_name': os.path.basename(file_path),
                    'error': f'Unknown parser script: {script_name}'
                }

        except Exception as e:
            self.logger.error(f"Error calling {script_name}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'file_name': os.path.basename(file_path),
                'error': f'Error calling {script_name}: {str(e)}'
            }

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        pass


def main():
    """Main entry point for command-line usage."""
    parser = argparse.ArgumentParser(description='Integrated Cargo XML Parser')
    parser.add_argument('file_path', help='Path to XML file to process')
    parser.add_argument('--branch-id', type=int, default=1, help='Branch ID')
    parser.add_argument('--user-id', type=int, default=1, help='User ID')
    parser.add_argument('--format', choices=['json', 'text'], default='json', help='Output format')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    log_level = logging.DEBUG if args.verbose else logging.INFO

    try:
        with IntegratedCargoParser(args.branch_id, args.user_id, log_level) as parser:
            result = parser.process_file(args.file_path)

            if args.format == 'json':
                print(json.dumps(result))
            else:
                if result['success']:
                    print(f"✅ Successfully processed {result.get('file_name', 'file')}")
                    if 'awb_count' in result:
                        print(f"   AWBs processed: {result['awb_count']}")
                    if 'partial_count' in result:
                        print(f"   Partial waybills: {result['partial_count']}")
                else:
                    print(f"❌ Failed to process {result.get('file_name', 'file')}: {result.get('error', 'Unknown error')}")

            return 0 if result['success'] else 1

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'file_name': os.path.basename(args.file_path) if args.file_path else 'unknown'
        }

        if args.format == 'json':
            print(json.dumps(error_result))
        else:
            print(f"❌ Error: {str(e)}")

        return 1


if __name__ == '__main__':
    sys.exit(main())
